/**
 * TimelineManager - Verwaltet alle timeline- und marker-bezogenen Funktionen des Flowering Widgets
 * 
 * Verantwortlichkeiten:
 * - Laden und Verwal<PERSON> von <PERSON>er-Daten
 * - Aktualisierung der Marker-Liste UI
 * - Verwaltung von Marker-Operationen (CRUD)
 * - Event-Handling für marker-bezogene Interaktionen
 * - Filter-Funktionalität für Marker
 * 
 * <AUTHOR> Agent
 * @date 2025-01-14
 */
class FloweringTimelineManager {
    constructor(widget) {
        this.widget = widget;
        
        // Datenstrukturen
        this.markers = [];
        
        // UI-Zustand
        this.currentEditMarkerId = null;
        this.currentDeleteMarkerId = null;
        
        // Event-Listener Setup
        this.setupEventListeners();
    }

    // ===== ÖFFENTLICHE API =====

    /**
     * Lädt Marker-Daten von <PERSON>
     */
    async loadMarkers() {
        try {
            const response = await fetch(`/flowering/markers/${this.widget.currentPlantId}`);
            const data = await response.json();
            
            this.markers = data.markers;
            this.updateMarkersList();
            this.updateTriggerConditions();
        } catch (error) {
            console.error('🌺 TimelineManager: Fehler beim Laden der Marker:', error);
        }
    }

    /**
     * Aktualisiert die Marker-Liste in der UI
     */
    updateMarkersList() {
        const markersList = this.widget.getElementById('markersList');
        if (!markersList) return;
        
        markersList.innerHTML = this.markers.map(marker => `
            <div class="marker-item" data-marker-id="${marker.id}" data-category="${marker.category}" data-importance="${marker.importance}">
                <div class="marker-header">
                    <div class="marker-info">
                        <div class="marker-title">${marker.event_name}</div>
                        <div class="marker-meta">
                            <span class="marker-category">${this.getCategoryName(marker.category)}</span>
                            <span class="marker-importance ${marker.importance}">${marker.importance}</span>
                            <span>Tag ${marker.bloom_day}</span>
                            <span>${this.formatDate(marker.date)}</span>
                        </div>
                    </div>
                    <div class="marker-actions">
                        <button class="marker-btn edit" data-marker-id="${marker.id}">
                            ✏️ Bearbeiten
                        </button>
                        <button class="marker-btn delete" data-marker-id="${marker.id}">
                            🗑️ Löschen
                        </button>
                    </div>
                </div>
                ${marker.notes ? `<div class="marker-notes">${marker.notes}</div>` : ''}
                
                <!-- Inline Bearbeitungs-Formular -->
                <div class="marker-edit-form" id="editMarkerForm${marker.id}" style="display: none;">
                    <div class="form-header">
                        <h5>✏️ Marker bearbeiten</h5>
                        <button class="btn-close" data-marker-id="${marker.id}">×</button>
                    </div>
                    <form class="marker-edit-form-content" data-marker-id="${marker.id}">
                        <div class="form-row">
                            <div class="form-group">
                                <label>Event-Typ:</label>
                                <input type="text" value="${marker.event_type}" readonly>
                            </div>
                            <div class="form-group">
                                <label>Event-Name:</label>
                                <input type="text" name="event_name" value="${marker.event_name}" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Blüte-Tag:</label>
                                <input type="number" name="bloom_day" value="${marker.bloom_day}" min="1" max="100" required>
                            </div>
                            <div class="form-group">
                                <label>Kategorie:</label>
                                <select name="category">
                                    <option value="trichome" ${marker.category === 'trichome' ? 'selected' : ''}>Trichome</option>
                                    <option value="flush" ${marker.category === 'flush' ? 'selected' : ''}>Flush</option>
                                    <option value="harvest" ${marker.category === 'harvest' ? 'selected' : ''}>Ernte</option>
                                    <option value="general" ${marker.category === 'general' ? 'selected' : ''}>Allgemein</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>Wichtigkeit:</label>
                                <select name="importance">
                                    <option value="low" ${marker.importance === 'low' ? 'selected' : ''}>Niedrig</option>
                                    <option value="medium" ${marker.importance === 'medium' ? 'selected' : ''}>Mittel</option>
                                    <option value="high" ${marker.importance === 'high' ? 'selected' : ''}>Hoch</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Notizen:</label>
                            <textarea name="notes" rows="2">${marker.notes || ''}</textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary btn-sm">Speichern</button>
                            <button type="button" class="btn btn-secondary btn-sm" data-marker-id="${marker.id}">Abbrechen</button>
                        </div>
                    </form>
                </div>
                
                <!-- Inline Lösch-Bestätigung -->
                <div class="marker-delete-form" id="deleteMarkerForm${marker.id}" style="display: none;">
                    <div class="form-header">
                        <h5>🗑️ Marker löschen</h5>
                        <button class="btn-close" data-marker-id="${marker.id}">×</button>
                    </div>
                    <div class="delete-confirmation">
                        <p>Möchtest du den Marker "<strong>${marker.event_name}</strong>" wirklich löschen?</p>
                        <p class="delete-warning">Diese Aktion kann nicht rückgängig gemacht werden.</p>
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" data-marker-id="${marker.id}">Abbrechen</button>
                            <button type="button" class="btn btn-danger" data-marker-id="${marker.id}">Löschen</button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
        
        // Event-Listener für dynamisch erstellte Elemente hinzufügen
        this.setupDynamicEventListeners();
    }

    /**
     * Speichert einen neuen Marker
     */
    async saveMarker() {
        const markerForm = this.widget.getElementById('markerForm');
        if (!markerForm) return;

        const formData = new FormData(markerForm);
        const markerData = {
            plant_id: this.widget.currentPlantId,
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            event_type: formData.get('event_type'),
            event_name: formData.get('event_name'),
            category: formData.get('category'),
            importance: formData.get('importance'),
            notes: formData.get('notes')
        };

        try {
            const response = await fetch(`/flowering/marker/${this.widget.currentPlantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(markerData)
            });

            if (response.ok) {
                const result = await response.json();
                this.widget.showSuccess('Marker erfolgreich gespeichert!');
                this.widget.hideMarkerForm();
                
                // Daten neu laden
                await this.loadMarkers();
            } else {
                const error = await response.json();
                console.error('🌺 TimelineManager: Fehler beim Speichern:', error);
                this.widget.showError(`Fehler beim Speichern: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 TimelineManager: Fehler beim Speichern des Markers:', error);
            this.widget.showError('Fehler beim Speichern des Markers');
        }
    }

    /**
     * Bearbeitet einen bestehenden Marker
     */
    async editMarker(markerId) {
        this.widget.openMarkerModal(markerId);
    }

    /**
     * Löscht einen Marker
     */
    async deleteMarker(markerId) {
        // Diese Methode wird durch die Inline-Löschung ersetzt
        this.showDeleteMarkerForm(markerId);
    }

    /**
     * Filtert Marker nach Kategorie und Wichtigkeit
     */
    filterMarkers() {
        const categoryFilter = this.widget.getElementById('categoryFilter');
        const importanceFilter = this.widget.getElementById('importanceFilter');
        const markersList = this.widget.getElementById('markersList');
        
        if (!categoryFilter || !importanceFilter || !markersList) {
            console.error('🌺 TimelineManager: Filter-Elemente nicht gefunden');
            return;
        }
        
        const selectedCategory = categoryFilter.value;
        const selectedImportance = importanceFilter.value;
        
        const markerItems = markersList.querySelectorAll('.marker-item');
        
        markerItems.forEach(item => {
            const category = item.getAttribute('data-category');
            const importance = item.getAttribute('data-importance');
            
            const categoryMatch = selectedCategory === 'all' || category === selectedCategory;
            const importanceMatch = selectedImportance === 'all' || importance === selectedImportance;
            
            if (categoryMatch && importanceMatch) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // ===== INLINE FORMULAR-METHODEN =====

    /**
     * Zeigt das Bearbeitungsformular für einen Marker
     */
    showEditMarkerForm(markerId) {
        this.hideAllMarkerForms();
        
        const editForm = document.getElementById(`editMarkerForm${markerId}`);
        if (editForm) {
            editForm.style.display = 'block';
            this.currentEditMarkerId = markerId;
        }
    }

    /**
     * Versteckt das Bearbeitungsformular für einen Marker
     */
    hideEditMarkerForm(markerId) {
        const editForm = document.getElementById(`editMarkerForm${markerId}`);
        if (editForm) {
            editForm.style.display = 'none';
        }
        this.currentEditMarkerId = null;
    }

    /**
     * Zeigt das Löschformular für einen Marker
     */
    showDeleteMarkerForm(markerId) {
        this.hideAllMarkerForms();
        
        const deleteForm = document.getElementById(`deleteMarkerForm${markerId}`);
        if (deleteForm) {
            deleteForm.style.display = 'block';
            this.currentDeleteMarkerId = markerId;
        }
    }

    /**
     * Versteckt das Löschformular für einen Marker
     */
    hideDeleteMarkerForm(markerId) {
        const deleteForm = document.getElementById(`deleteMarkerForm${markerId}`);
        if (deleteForm) {
            deleteForm.style.display = 'none';
        }
        this.currentDeleteMarkerId = null;
    }

    /**
     * Versteckt alle Marker-Formulare
     */
    hideAllMarkerForms() {
        const editForms = document.querySelectorAll('.marker-edit-form');
        const deleteForms = document.querySelectorAll('.marker-delete-form');
        
        editForms.forEach(form => {
            form.style.display = 'none';
        });
        
        deleteForms.forEach(form => {
            form.style.display = 'none';
        });
        
        this.currentEditMarkerId = null;
        this.currentDeleteMarkerId = null;
    }

    /**
     * Führt die Bearbeitung eines Markers durch
     */
    async submitEditMarker(event, markerId) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);

        // Finde den ursprünglichen Marker, um event_type zu beibehalten
        const originalMarker = this.markers.find(m => m.id == markerId);
        if (!originalMarker) {
            this.widget.showError('Marker nicht gefunden');
            return;
        }

        const updateData = {
            event_type: originalMarker.event_type, // Beibehalten
            event_name: formData.get('event_name'),
            bloom_day: parseInt(formData.get('bloom_day')),
            category: formData.get('category'),
            importance: formData.get('importance'),
            notes: formData.get('notes')
        };

        try {
            const response = await fetch(`/flowering/marker/${this.widget.currentPlantId}/${markerId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });

            if (response.ok) {
                const result = await response.json();
                this.widget.showSuccess('Marker erfolgreich bearbeitet!');
                this.hideEditMarkerForm(markerId);

                // Daten neu laden
                await this.loadMarkers();
            } else {
                const error = await response.json();
                console.error('🌺 TimelineManager: Fehler beim Bearbeiten:', error);
                this.widget.showError(`Fehler beim Bearbeiten: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 TimelineManager: Fehler beim Bearbeiten des Markers:', error);
            this.widget.showError('Fehler beim Bearbeiten des Markers');
        }
    }

    /**
     * Führt die Löschung eines Markers durch
     */
    async submitDeleteMarker(markerId) {
        try {
            const response = await fetch(`/flowering/marker/${this.widget.currentPlantId}/${markerId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                const result = await response.json();
                this.widget.showSuccess('Marker erfolgreich gelöscht!');
                this.hideDeleteMarkerForm(markerId);

                // Daten neu laden
                await this.loadMarkers();
            } else {
                const error = await response.json();
                console.error('🌺 TimelineManager: Fehler beim Löschen:', error);
                this.widget.showError(`Fehler beim Löschen: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 TimelineManager: Fehler beim Löschen des Markers:', error);
            this.widget.showError('Fehler beim Löschen des Markers');
        }
    }

    /**
     * Aktualisiert Trigger-Bedingungen basierend auf Markern
     */
    updateTriggerConditions() {
        // Diese Methode wird vom Haupt-Widget implementiert
        // da sie Teil der Trigger-Funktionalität ist
        return this.widget.updateTriggerConditions();
    }

    /**
     * Hilfsmethoden für UI-Formatierung
     */
    getCategoryName(category) {
        const categoryNames = {
            'trichome': 'Trichome',
            'flush': 'Flush',
            'harvest': 'Ernte',
            'general': 'Allgemein'
        };
        return categoryNames[category] || category;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('de-DE');
    }

    // ===== PRIVATE HILFSMETHODEN =====

    /**
     * Initialisiert Event-Listener für timeline-bezogene Elemente
     */
    setupEventListeners() {
        // Add Marker Button
        const addMarkerBtn = this.widget.getElementById('addMarkerBtn');
        if (addMarkerBtn) {
            addMarkerBtn.addEventListener('click', () => {
                this.widget.openMarkerModal();
            });
        }

        // Filter Event-Listener
        this.setupFilterListeners();

        // Marker Form Event-Listener
        this.setupMarkerFormListeners();
    }

    /**
     * Initialisiert Filter Event-Listener
     */
    setupFilterListeners() {
        // Kategorie-Filter
        const categoryFilter = this.widget.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.filterMarkers();
            });
        }

        // Wichtigkeit-Filter
        const importanceFilter = this.widget.getElementById('importanceFilter');
        if (importanceFilter) {
            importanceFilter.addEventListener('change', () => {
                this.filterMarkers();
            });
        }
    }

    /**
     * Initialisiert Marker-Formular Event-Listener
     */
    setupMarkerFormListeners() {
        const addMarkerBtn = this.widget.getElementById('addMarkerBtn');
        const closeMarkerForm = this.widget.getElementById('closeMarkerForm');
        const cancelMarkerForm = this.widget.getElementById('cancelMarkerForm');
        const markerForm = this.widget.getElementById('markerForm');
        const markerFormContainer = this.widget.getElementById('markerFormContainer');

        if (addMarkerBtn) {
            addMarkerBtn.addEventListener('click', () => {
                this.widget.showMarkerForm();
            });
        }

        if (closeMarkerForm) {
            closeMarkerForm.addEventListener('click', () => {
                this.widget.hideMarkerForm();
            });
        }

        if (cancelMarkerForm) {
            cancelMarkerForm.addEventListener('click', () => {
                this.widget.hideMarkerForm();
            });
        }

        if (markerForm) {
            markerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveMarker();
            });
        }
    }

    /**
     * Initialisiert Event-Listener für dynamisch erstellte Elemente
     */
    setupDynamicEventListeners() {
        // Event-Listener für Bearbeiten-Buttons
        document.querySelectorAll('.marker-btn.edit').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.showEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Löschen-Buttons
        document.querySelectorAll('.marker-btn.delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.showDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Close-Buttons in Edit-Formularen
        document.querySelectorAll('.marker-edit-form .btn-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Close-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Abbrechen-Buttons in Edit-Formularen
        document.querySelectorAll('.marker-edit-form .btn-secondary').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Abbrechen-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-secondary').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Löschen-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-danger').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.submitDeleteMarker(markerId);
            });
        });

        // Event-Listener für Edit-Formulare
        document.querySelectorAll('.marker-edit-form-content').forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const markerId = e.target.getAttribute('data-marker-id');
                this.submitEditMarker(e, markerId);
            });
        });
    }

    /**
     * Hilfsmethode für DOM-Zugriff über das Haupt-Widget
     */
    getElementById(id) {
        return this.widget.getElementById(id);
    }
}

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FloweringTimelineManager;
}
