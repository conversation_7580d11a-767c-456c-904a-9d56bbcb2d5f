/**
 * FloweringFormManager - Verwaltet alle Formular-Operationen für das Flowering Widget
 * 
 * Zuständigkeiten:
 * - Formular-Anzeige und -Verstecken
 * - Event-Listener für Formulare
 * - Formular-Validierung und -Submission
 * - Modal-Dialog-Management
 * - Formular-Daten-Sammlung und -Verarbeitung
 */
class FloweringFormManager {
    constructor(widget) {
        this.widget = widget;
        this.currentEditIndex = null;
        this.currentDeleteIndex = null;
        this.currentEditMarkerId = null;
        this.isSubmitting = false;
        
        console.log('📝 FloweringFormManager: Initialisiert');
    }

    /**
     * Initialisiert alle Formular-Event-Listener
     */
    setupEventListeners() {
        this.setupObservationForm();
        this.setupMarkerForms();
        this.setupSliderListeners();
    }

    /**
     * Event-Listener für Trichome-Beobachtungsformular
     */
    setupObservationForm() {
        const addBtn = this.widget.getElementById('addObservationBtn');
        const formContainer = this.widget.getElementById('observationFormContainer');
        const cancelBtn = this.widget.getElementById('cancelObservationBtn');
        const form = this.widget.getElementById('trichomeObservationForm');
        
        if (addBtn) {
            addBtn.addEventListener('click', () => {
                this.showObservationForm();
            });
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideObservationForm();
            });
        }
        
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitObservation();
            });
        }
    }

    /**
     * Event-Listener für Marker-Formulare
     */
    setupMarkerForms() {
        // Event-Delegation für dynamisch erstellte Marker-Formulare
        const timelineContainer = this.widget.getElementById('timelineContainer');
        if (timelineContainer) {
            timelineContainer.addEventListener('click', (e) => {
                if (e.target.classList.contains('edit-marker-btn')) {
                    const markerId = e.target.dataset.markerId;
                    this.showEditMarkerForm(markerId);
                } else if (e.target.classList.contains('delete-marker-btn')) {
                    const markerId = e.target.dataset.markerId;
                    this.showDeleteMarkerForm(markerId);
                } else if (e.target.classList.contains('cancel-edit-btn')) {
                    const markerId = e.target.dataset.markerId;
                    this.hideEditMarkerForm(markerId);
                } else if (e.target.classList.contains('cancel-delete-btn')) {
                    const markerId = e.target.dataset.markerId;
                    this.hideDeleteMarkerForm(markerId);
                }
            });

            timelineContainer.addEventListener('submit', (e) => {
                if (e.target.classList.contains('marker-edit-form')) {
                    e.preventDefault();
                    this.submitEditMarker(e.target);
                } else if (e.target.classList.contains('marker-delete-form')) {
                    e.preventDefault();
                    this.submitDeleteMarker(e.target);
                }
            });
        }
    }

    /**
     * Event-Listener für Schieberegler (Live-Updates)
     */
    setupSliderListeners() {
        const sliders = ['clear_percentage', 'milky_percentage', 'amber_percentage'];
        
        sliders.forEach(sliderId => {
            const slider = this.widget.getElementById(sliderId);
            const display = this.widget.getElementById(`${sliderId}_display`);
            
            if (slider && display) {
                slider.addEventListener('input', (e) => {
                    display.textContent = e.target.value + '%';
                    this.updateSliderSum();
                });
            }
        });
    }

    /**
     * Beobachtungsformular anzeigen
     */
    showObservationForm() {
        this.hideAllForms();
        
        const formContainer = this.widget.getElementById('observationFormContainer');
        if (formContainer) {
            formContainer.style.display = 'block';
        }
        
        this.resetObservationForm();
    }

    /**
     * Beobachtungsformular verstecken
     */
    hideObservationForm() {
        const formContainer = this.widget.getElementById('observationFormContainer');
        if (formContainer) {
            formContainer.style.display = 'none';
        }
        
        this.resetObservationForm();
    }

    /**
     * Bearbeitungsformular für Beobachtung anzeigen
     */
    showEditForm(index) {
        this.hideAllForms();
        
        this.currentEditIndex = index;
        this.currentDeleteIndex = null;
        this.clearObservationIndicators();
        
        const container = this.widget.getElementById('observationActionFormContainer');
        if (!container) return;
        
        // Markiere den aktuellen Eintrag
        const item = document.querySelector(`.observation-item[data-observation-index="${index}"]`);
        if (item) {
            item.classList.add('is-editing');
        }
        
        // Hole die Beobachtungsdaten
        const observation = this.widget.trichomeData?.observations?.[index];
        if (!observation) return;
        
        // Erstelle Bearbeitungsformular
        container.innerHTML = this.createEditFormHTML(observation, index);
        
        // Event-Listener für das neue Formular
        this.setupEditFormListeners(index);
    }

    /**
     * Löschformular für Beobachtung anzeigen
     */
    showDeleteForm(index) {
        this.hideAllForms();
        
        this.currentDeleteIndex = index;
        this.currentEditIndex = null;
        this.clearObservationIndicators();
        
        const container = this.widget.getElementById('observationActionFormContainer');
        if (!container) return;
        
        // Markiere den aktuellen Eintrag
        const item = document.querySelector(`.observation-item[data-observation-index="${index}"]`);
        if (item) {
            item.classList.add('is-deleting');
        }
        
        // Hole die Beobachtungsdaten
        const observation = this.widget.trichomeData?.observations?.[index];
        if (!observation) return;
        
        // Erstelle Löschformular
        container.innerHTML = this.createDeleteFormHTML(observation, index);
        
        // Event-Listener für das neue Formular
        this.setupDeleteFormListeners(index);
    }

    /**
     * Marker-Bearbeitungsformular anzeigen
     */
    showEditMarkerForm(markerId) {
        this.hideAllMarkerForms();
        
        const editForm = this.widget.getElementById(`editMarkerForm${markerId}`);
        if (editForm) {
            editForm.style.display = 'block';
            this.currentEditMarkerId = markerId;
        }
    }

    /**
     * Marker-Bearbeitungsformular verstecken
     */
    hideEditMarkerForm(markerId) {
        const editForm = this.widget.getElementById(`editMarkerForm${markerId}`);
        if (editForm) {
            editForm.style.display = 'none';
        }
        this.currentEditMarkerId = null;
    }

    /**
     * Marker-Löschformular anzeigen
     */
    showDeleteMarkerForm(markerId) {
        this.hideAllMarkerForms();
        
        const deleteForm = this.widget.getElementById(`deleteMarkerForm${markerId}`);
        if (deleteForm) {
            deleteForm.style.display = 'block';
        }
    }

    /**
     * Marker-Löschformular verstecken
     */
    hideDeleteMarkerForm(markerId) {
        const deleteForm = this.widget.getElementById(`deleteMarkerForm${markerId}`);
        if (deleteForm) {
            deleteForm.style.display = 'none';
        }
    }

    /**
     * Alle Formulare verstecken
     */
    hideAllForms() {
        // Neues Eintrag-Formular schließen
        const newFormContainer = this.widget.getElementById('observationFormContainer');
        if (newFormContainer) {
            newFormContainer.style.display = 'none';
        }
        
        // Bearbeiten/Löschen-Formular schließen
        const actionFormContainer = this.widget.getElementById('observationActionFormContainer');
        if (actionFormContainer) {
            actionFormContainer.innerHTML = '';
        }
        
        // Alle Hervorhebungen entfernen
        this.clearObservationIndicators();
        
        // Indizes zurücksetzen
        this.currentEditIndex = null;
        this.currentDeleteIndex = null;
    }

    /**
     * Alle Marker-Formulare verstecken
     */
    hideAllMarkerForms() {
        const editForms = document.querySelectorAll('.marker-edit-form');
        const deleteForms = document.querySelectorAll('.marker-delete-form');
        
        editForms.forEach(form => {
            form.style.display = 'none';
        });
        
        deleteForms.forEach(form => {
            form.style.display = 'none';
        });
        
        this.currentEditMarkerId = null;
    }

    /**
     * Alle Bearbeitungsformulare verstecken
     */
    hideAllEditForms() {
        const editForms = document.querySelectorAll('.edit-form');
        editForms.forEach(form => {
            form.style.display = 'none';
        });
    }

    /**
     * Beobachtungsformular zurücksetzen
     */
    resetObservationForm() {
        const form = this.widget.getElementById('trichomeObservationForm');
        if (form) {
            form.reset();
            
            // Aktuelle Werte für Datum und Blütetag setzen
            this.setDefaultFormValues();
            
            // Schieberegler-Anzeigen aktualisieren
            this.updateSliderDisplays();
            this.updateSliderSum();
        }
    }

    /**
     * Standard-Formularwerte setzen
     */
    setDefaultFormValues() {
        // Aktuelles Datum setzen
        const dateInput = this.widget.getElementById('date');
        if (dateInput) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }

        // Aktuellen Blütetag setzen
        const bloomDayInput = this.widget.getElementById('bloom_day');
        if (bloomDayInput && this.widget.floweringData?.flowering_status?.current_day) {
            bloomDayInput.value = this.widget.floweringData.flowering_status.current_day;
        }
    }

    /**
     * Schieberegler-Anzeigen aktualisieren
     */
    updateSliderDisplays() {
        const sliders = ['clear_percentage', 'milky_percentage', 'amber_percentage'];

        sliders.forEach(sliderId => {
            const slider = this.widget.getElementById(sliderId);
            const display = this.widget.getElementById(`${sliderId}_display`);

            if (slider && display) {
                display.textContent = slider.value + '%';
            }
        });
    }

    /**
     * Schieberegler-Summe aktualisieren
     */
    updateSliderSum() {
        const clearSlider = this.widget.getElementById('clear_percentage');
        const milkySlider = this.widget.getElementById('milky_percentage');
        const amberSlider = this.widget.getElementById('amber_percentage');
        const sumDisplay = this.widget.getElementById('slider_sum');

        if (clearSlider && milkySlider && amberSlider && sumDisplay) {
            const sum = parseInt(clearSlider.value) + parseInt(milkySlider.value) + parseInt(amberSlider.value);
            sumDisplay.textContent = sum;

            // Warnung bei ungleich 100%
            if (sum !== 100) {
                sumDisplay.style.color = '#dc3545';
                sumDisplay.style.fontWeight = 'bold';
            } else {
                sumDisplay.style.color = '#28a745';
                sumDisplay.style.fontWeight = 'normal';
            }
        }
    }

    /**
     * Beobachtungsindikatoren löschen
     */
    clearObservationIndicators() {
        const items = document.querySelectorAll('.observation-item');
        items.forEach(item => {
            item.classList.remove('is-editing', 'is-deleting');
        });
    }

    /**
     * Beobachtung absenden
     */
    async submitObservation() {
        if (this.isSubmitting) return;
        this.isSubmitting = true;

        try {
            // Delegiere an TrichomeManager falls verfügbar
            if (this.widget.trichomeManager) {
                await this.widget.trichomeManager.submitObservation();
            } else {
                // Fallback: Direkte Implementierung
                await this.submitObservationDirect();
            }
        } catch (error) {
            console.error('📝 FormManager: Fehler beim Absenden der Beobachtung:', error);
            this.widget.showError('Fehler beim Speichern der Beobachtung: ' + error.message);
        } finally {
            this.isSubmitting = false;
        }
    }

    /**
     * Direkte Beobachtungs-Submission (Fallback)
     */
    async submitObservationDirect() {
        const form = this.widget.getElementById('trichomeObservationForm');
        if (!form) return;

        const formData = new FormData(form);
        const observationData = {
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            location: formData.get('location'),
            clear_percentage: parseInt(formData.get('clear_percentage')),
            milky_percentage: parseInt(formData.get('milky_percentage')),
            amber_percentage: parseInt(formData.get('amber_percentage')),
            notes: formData.get('notes') || ''
        };

        // Validierung
        if (!this.validateObservationData(observationData)) {
            return;
        }

        // API-Aufruf
        const response = await fetch(`/flowering/trichome-observation/${this.widget.currentPlantId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(observationData)
        });

        if (response.ok) {
            this.widget.showSuccess('Beobachtung erfolgreich gespeichert!');
            this.hideObservationForm();

            // Daten neu laden
            if (this.widget.loadTrichomeStatus) {
                await this.widget.loadTrichomeStatus();
            }
        } else {
            throw new Error('Fehler beim Speichern der Beobachtung');
        }
    }

    /**
     * Beobachtungsdaten validieren
     */
    validateObservationData(data) {
        // Delegiere an ValidationManager falls verfügbar
        if (this.widget.validationManager) {
            const result = this.widget.validationManager.validateObservationData(data);

            if (!result.valid) {
                this.widget.validationManager.displayValidationResult(result);
                return false;
            }

            // Zeige Warnungen falls vorhanden
            if (result.warnings.length > 0) {
                this.widget.validationManager.displayValidationResult(result);
            }

            return true;
        }

        // Fallback: Originale Implementierung
        const sum = data.clear_percentage + data.milky_percentage + data.amber_percentage;
        if (sum !== 100) {
            this.widget.showError('Die Prozentsätze müssen zusammen 100% ergeben!');
            return false;
        }

        const observationDate = new Date(data.date);
        const today = new Date();
        if (observationDate > today) {
            this.widget.showError('Das Datum darf nicht in der Zukunft liegen!');
            return false;
        }

        if (data.bloom_day < 1) {
            this.widget.showError('Der Blütetag muss mindestens 1 sein!');
            return false;
        }

        return true;
    }

    /**
     * Formular-Daten sammeln
     */
    collectFormData(formElement) {
        const formData = new FormData(formElement);
        const data = {};

        for (const [key, value] of formData.entries()) {
            // Numerische Werte konvertieren
            if (key.includes('percentage') || key === 'bloom_day') {
                data[key] = parseInt(value);
            } else {
                data[key] = value;
            }
        }

        return data;
    }

    /**
     * Button-Loading-State setzen
     */
    setButtonLoading(button, isLoading) {
        if (!button) return;

        if (isLoading) {
            button.disabled = true;
            button.dataset.originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> Wird gespeichert...';
        } else {
            button.disabled = false;
            if (button.dataset.originalText) {
                button.innerHTML = button.dataset.originalText;
                delete button.dataset.originalText;
            }
        }
    }

    /**
     * HTML für Bearbeitungsformular erstellen
     */
    createEditFormHTML(observation, index) {
        return `
            <div class="edit-form-container">
                <h4>Beobachtung bearbeiten</h4>
                <form class="edit-observation-form" data-index="${index}">
                    <div class="form-row">
                        <div class="form-group">
                            <label>Datum:</label>
                            <input type="date" name="date" value="${observation.date}" required>
                        </div>
                        <div class="form-group">
                            <label>Blütetag:</label>
                            <input type="number" name="bloom_day" value="${observation.bloom_day}" min="1" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Klar (%):</label>
                            <input type="range" name="clear_percentage" value="${observation.clear_percentage}" min="0" max="100">
                            <span class="percentage-display">${observation.clear_percentage}%</span>
                        </div>
                        <div class="form-group">
                            <label>Milchig (%):</label>
                            <input type="range" name="milky_percentage" value="${observation.milky_percentage}" min="0" max="100">
                            <span class="percentage-display">${observation.milky_percentage}%</span>
                        </div>
                        <div class="form-group">
                            <label>Bernstein (%):</label>
                            <input type="range" name="amber_percentage" value="${observation.amber_percentage}" min="0" max="100">
                            <span class="percentage-display">${observation.amber_percentage}%</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Notizen:</label>
                        <textarea name="notes" rows="3">${observation.notes || ''}</textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Speichern</button>
                        <button type="button" class="btn btn-secondary" onclick="floweringWidget.formManager.hideAllForms()">Abbrechen</button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * HTML für Löschformular erstellen
     */
    createDeleteFormHTML(observation, index) {
        return `
            <div class="delete-form-container">
                <h4>Beobachtung löschen</h4>
                <p>Möchten Sie diese Beobachtung wirklich löschen?</p>
                <div class="observation-preview">
                    <strong>Datum:</strong> ${observation.date}<br>
                    <strong>Blütetag:</strong> ${observation.bloom_day}<br>
                    <strong>Trichome:</strong> ${observation.clear_percentage}% klar, ${observation.milky_percentage}% milchig, ${observation.amber_percentage}% bernstein
                </div>
                <form class="delete-observation-form" data-index="${index}">
                    <div class="form-actions">
                        <button type="submit" class="btn btn-danger">Löschen</button>
                        <button type="button" class="btn btn-secondary" onclick="floweringWidget.formManager.hideAllForms()">Abbrechen</button>
                    </div>
                </form>
            </div>
        `;
    }

    /**
     * Event-Listener für Bearbeitungsformular einrichten
     */
    setupEditFormListeners(index) {
        const form = document.querySelector(`.edit-observation-form[data-index="${index}"]`);
        if (!form) return;

        // Formular-Submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.submitEditObservation(index);
        });

        // Schieberegler-Updates
        const sliders = form.querySelectorAll('input[type="range"]');
        sliders.forEach(slider => {
            const display = slider.nextElementSibling;
            slider.addEventListener('input', (e) => {
                display.textContent = e.target.value + '%';
            });
        });
    }

    /**
     * Event-Listener für Löschformular einrichten
     */
    setupDeleteFormListeners(index) {
        const form = document.querySelector(`.delete-observation-form[data-index="${index}"]`);
        if (!form) return;

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            await this.submitDeleteObservation(index);
        });
    }

    /**
     * Bearbeitete Beobachtung absenden
     */
    async submitEditObservation(index) {
        if (this.isSubmitting) return;
        this.isSubmitting = true;

        try {
            // Delegiere an TrichomeManager falls verfügbar
            if (this.widget.trichomeManager) {
                await this.widget.trichomeManager.editObservation(index);
            } else {
                // Fallback: Direkte Implementierung
                const form = document.querySelector(`.edit-observation-form[data-index="${index}"]`);
                const data = this.collectFormData(form);

                if (!this.validateObservationData(data)) {
                    return;
                }

                const response = await fetch(`/flowering/trichome-observation/${this.widget.currentPlantId}/${index}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    this.widget.showSuccess('Beobachtung erfolgreich aktualisiert!');
                    this.hideAllForms();
                    await this.widget.loadTrichomeStatus();
                } else {
                    throw new Error('Fehler beim Aktualisieren der Beobachtung');
                }
            }
        } catch (error) {
            console.error('📝 FormManager: Fehler beim Bearbeiten der Beobachtung:', error);
            this.widget.showError('Fehler beim Aktualisieren der Beobachtung: ' + error.message);
        } finally {
            this.isSubmitting = false;
        }
    }

    /**
     * Beobachtung löschen
     */
    async submitDeleteObservation(index) {
        if (this.isSubmitting) return;
        this.isSubmitting = true;

        try {
            // Delegiere an TrichomeManager falls verfügbar
            if (this.widget.trichomeManager) {
                await this.widget.trichomeManager.deleteObservation(index);
            } else {
                // Fallback: Direkte Implementierung
                const response = await fetch(`/flowering/trichome-observation/${this.widget.currentPlantId}/${index}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    this.widget.showSuccess('Beobachtung erfolgreich gelöscht!');
                    this.hideAllForms();
                    await this.widget.loadTrichomeStatus();
                } else {
                    throw new Error('Fehler beim Löschen der Beobachtung');
                }
            }
        } catch (error) {
            console.error('📝 FormManager: Fehler beim Löschen der Beobachtung:', error);
            this.widget.showError('Fehler beim Löschen der Beobachtung: ' + error.message);
        } finally {
            this.isSubmitting = false;
        }
    }

    /**
     * Marker bearbeiten
     */
    async submitEditMarker(form) {
        if (this.isSubmitting) return;
        this.isSubmitting = true;

        try {
            // Delegiere an TimelineManager falls verfügbar
            if (this.widget.timelineManager) {
                await this.widget.timelineManager.submitEditMarker(form);
            }
        } catch (error) {
            console.error('📝 FormManager: Fehler beim Bearbeiten des Markers:', error);
            this.widget.showError('Fehler beim Bearbeiten des Markers: ' + error.message);
        } finally {
            this.isSubmitting = false;
        }
    }

    /**
     * Marker löschen
     */
    async submitDeleteMarker(form) {
        if (this.isSubmitting) return;
        this.isSubmitting = true;

        try {
            // Delegiere an TimelineManager falls verfügbar
            if (this.widget.timelineManager) {
                await this.widget.timelineManager.submitDeleteMarker(form);
            }
        } catch (error) {
            console.error('📝 FormManager: Fehler beim Löschen des Markers:', error);
            this.widget.showError('Fehler beim Löschen des Markers: ' + error.message);
        } finally {
            this.isSubmitting = false;
        }
    }

    /**
     * Cleanup-Methode
     */
    cleanup() {
        this.hideAllForms();
        this.hideAllMarkerForms();
        this.currentEditIndex = null;
        this.currentDeleteIndex = null;
        this.currentEditMarkerId = null;
        this.isSubmitting = false;

        console.log('📝 FormManager: Cleanup durchgeführt');
    }
}

// Export für Modulverwendung
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FloweringFormManager;
}
