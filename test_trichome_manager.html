<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TrichomeManager Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Flowering Widget Module Integration Test</h1>
        <p>Diese Seite testet die Integration der TrichomeManager und TimelineManager Module mit dem FloweringWidget.</p>

        <div class="test-section">
            <h2>1. Modul-Verfügbarkeit Test</h2>
            <button onclick="testModuleAvailability()">Module testen</button>
            <div id="moduleTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>2. Widget-Initialisierung Test</h2>
            <button onclick="testWidgetInitialization()">Widget initialisieren</button>
            <div id="widgetTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>3. TrichomeManager Integration Test</h2>
            <button onclick="testTrichomeManagerIntegration()">Integration testen</button>
            <div id="integrationTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>4. TrichomeManager API-Methoden Test</h2>
            <button onclick="testTrichomeAPIMethods()">TrichomeManager API testen</button>
            <div id="trichomeApiTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>5. TimelineManager Integration Test</h2>
            <button onclick="testTimelineManagerIntegration()">TimelineManager testen</button>
            <div id="timelineTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>6. TimelineManager API-Methoden Test</h2>
            <button onclick="testTimelineAPIMethods()">TimelineManager API testen</button>
            <div id="timelineApiTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>7. UIRenderer Integration Test</h2>
            <button onclick="testUIRendererIntegration()">UIRenderer testen</button>
            <div id="uiRendererTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>8. UIRenderer API-Methoden Test</h2>
            <button onclick="testUIRendererAPIMethods()">UIRenderer API testen</button>
            <div id="uiRendererApiTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>9. DataManager Integration Test</h2>
            <button onclick="testDataManagerIntegration()">DataManager testen</button>
            <div id="dataManagerTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>10. DataManager API-Methoden Test</h2>
            <button onclick="testDataManagerAPIMethods()">DataManager API testen</button>
            <div id="dataManagerApiTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>11. FormManager Integration Test</h2>
            <button onclick="testFormManagerIntegration()">FormManager testen</button>
            <div id="formManagerTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>12. FormManager API-Methoden Test</h2>
            <button onclick="testFormManagerAPIMethods()">FormManager API testen</button>
            <div id="formManagerApiTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>13. ValidationManager Integration Test</h2>
            <button onclick="testValidationManagerIntegration()">ValidationManager testen</button>
            <div id="validationManagerTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>14. ValidationManager API-Methoden Test</h2>
            <button onclick="testValidationManagerAPIMethods()">ValidationManager API testen</button>
            <div id="validationManagerApiTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>15. Validierung Funktionstest</h2>
            <button onclick="testValidationFunctionality()">Validierung testen</button>
            <div id="validationFunctionalityTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>16. Notification System Test</h2>
            <button onclick="testNotificationSystem()">Notifications testen</button>
            <div id="notificationTestResult" class="test-result"></div>
        </div>

        <div class="test-section">
            <h2>17. Console Log</h2>
            <button onclick="clearLog()">Log leeren</button>
            <div id="consoleLog" class="log"></div>
        </div>
    </div>

    <!-- Mock DOM-Elemente für Tests -->
    <div style="display: none;">
        <div class="flowering-widget-container">
            <div id="trichomeTab"></div>
            <div id="addObservationBtn"></div>
            <div id="trichomeObservationModalSave"></div>
            <div id="observationFormContainer"></div>
            <div id="trichomeObservationForm"></div>
            <div id="observationList"></div>
            <div id="trichomeStatusBadge"></div>
            <div id="badgeIcon"></div>
            <div id="badgeText"></div>
        </div>
    </div>

    <!-- Module laden -->
    <script src="static/scripts/widgets/flowering/data-manager.js"></script>
    <script src="static/scripts/widgets/flowering/validation-manager.js"></script>
    <script src="static/scripts/widgets/flowering/form-manager.js"></script>
    <script src="static/scripts/widgets/flowering/trichome-manager.js"></script>
    <script src="static/scripts/widgets/flowering/timeline-manager.js"></script>
    <script src="static/scripts/widgets/flowering/ui-renderer.js"></script>

    <script>
        // Mock FloweringWidget für Tests
        class MockFloweringWidget {
            constructor() {
                this.currentPlantId = 'TEST_PLANT';
                this.element = document.querySelector('.flowering-widget-container');
            }

            getElementById(id) {
                return document.getElementById(id);
            }

            showSuccess(message) {
                this.log(`SUCCESS: ${message}`);
            }

            showError(message) {
                this.log(`ERROR: ${message}`);
            }

            hideObservationForm() {
                this.log('hideObservationForm() called');
            }

            hideAllForms() {
                this.log('hideAllForms() called');
            }

            loadTrichomeGuidelines() {
                this.log('loadTrichomeGuidelines() called');
                return Promise.resolve();
            }

            log(message) {
                const logElement = document.getElementById('consoleLog');
                const timestamp = new Date().toLocaleTimeString();
                logElement.innerHTML += `[${timestamp}] ${message}<br>`;
                logElement.scrollTop = logElement.scrollHeight;
            }
        }

        let mockWidget = null;
        let dataManager = null;
        let validationManager = null;
        let formManager = null;
        let trichomeManager = null;
        let timelineManager = null;
        let uiRenderer = null;

        function testModuleAvailability() {
            const resultDiv = document.getElementById('moduleTestResult');

            try {
                const dataManagerAvailable = typeof FloweringDataManager !== 'undefined';
                const validationManagerAvailable = typeof FloweringValidationManager !== 'undefined';
                const formManagerAvailable = typeof FloweringFormManager !== 'undefined';
                const trichomeAvailable = typeof FloweringTrichomeManager !== 'undefined';
                const timelineAvailable = typeof FloweringTimelineManager !== 'undefined';
                const uiRendererAvailable = typeof FloweringUIRenderer !== 'undefined';

                if (dataManagerAvailable && validationManagerAvailable && formManagerAvailable && trichomeAvailable && timelineAvailable && uiRendererAvailable) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ Alle sechs Module sind verfügbar: DataManager, ValidationManager, FormManager, TrichomeManager, TimelineManager & UIRenderer';
                } else {
                    resultDiv.className = 'test-result error';
                    const missing = [];
                    if (!dataManagerAvailable) missing.push('FloweringDataManager');
                    if (!validationManagerAvailable) missing.push('FloweringValidationManager');
                    if (!formManagerAvailable) missing.push('FloweringFormManager');
                    if (!trichomeAvailable) missing.push('FloweringTrichomeManager');
                    if (!timelineAvailable) missing.push('FloweringTimelineManager');
                    if (!uiRendererAvailable) missing.push('FloweringUIRenderer');
                    resultDiv.innerHTML = `❌ Fehlende Module: ${missing.join(', ')}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen der Modul-Verfügbarkeit: ${error.message}`;
            }
        }

        function testWidgetInitialization() {
            const resultDiv = document.getElementById('widgetTestResult');
            
            try {
                mockWidget = new MockFloweringWidget();
                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✅ Mock FloweringWidget erfolgreich initialisiert';
                mockWidget.log('Mock FloweringWidget initialisiert');
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Initialisieren des Mock Widgets: ${error.message}`;
            }
        }

        function testTrichomeManagerIntegration() {
            const resultDiv = document.getElementById('integrationTestResult');
            
            try {
                if (!mockWidget) {
                    throw new Error('Mock Widget muss zuerst initialisiert werden');
                }

                trichomeManager = new FloweringTrichomeManager(mockWidget);
                
                if (trichomeManager && trichomeManager.widget === mockWidget) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ TrichomeManager erfolgreich mit Widget integriert';
                    mockWidget.log('TrichomeManager erfolgreich initialisiert');
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ TrichomeManager Integration fehlgeschlagen';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler bei der TrichomeManager Integration: ${error.message}`;
            }
        }

        function testTrichomeAPIMethods() {
            const resultDiv = document.getElementById('trichomeApiTestResult');

            try {
                if (!trichomeManager) {
                    throw new Error('TrichomeManager muss zuerst initialisiert werden');
                }

                const methods = [
                    'loadTrichomeData',
                    'loadTrichomeStatus',
                    'loadTrichomeTrigger',
                    'loadTrichomeRecommendation',
                    'loadTrichomeProgress',
                    'submitObservation',
                    'editObservation',
                    'deleteObservation',
                    'updateTrichomeStatus',
                    'updateTrichomeBadge',
                    'updateTrichomeSegments'
                ];

                const missingMethods = methods.filter(method => typeof trichomeManager[method] !== 'function');

                if (missingMethods.length === 0) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `✅ Alle ${methods.length} TrichomeManager API-Methoden sind verfügbar`;
                    mockWidget.log(`TrichomeManager API-Methoden verfügbar: ${methods.join(', ')}`);
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ Fehlende TrichomeManager Methoden: ${missingMethods.join(', ')}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen der TrichomeManager API-Methoden: ${error.message}`;
            }
        }

        function testTimelineManagerIntegration() {
            const resultDiv = document.getElementById('timelineTestResult');

            try {
                if (!mockWidget) {
                    throw new Error('Mock Widget muss zuerst initialisiert werden');
                }

                timelineManager = new FloweringTimelineManager(mockWidget);

                if (timelineManager && timelineManager.widget === mockWidget) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ TimelineManager erfolgreich mit Widget integriert';
                    mockWidget.log('TimelineManager erfolgreich initialisiert');
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ TimelineManager Integration fehlgeschlagen';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler bei der TimelineManager Integration: ${error.message}`;
            }
        }

        function testTimelineAPIMethods() {
            const resultDiv = document.getElementById('timelineApiTestResult');

            try {
                if (!timelineManager) {
                    throw new Error('TimelineManager muss zuerst initialisiert werden');
                }

                const methods = [
                    'loadMarkers',
                    'updateMarkersList',
                    'saveMarker',
                    'editMarker',
                    'deleteMarker',
                    'filterMarkers',
                    'showEditMarkerForm',
                    'hideEditMarkerForm',
                    'showDeleteMarkerForm',
                    'hideDeleteMarkerForm',
                    'submitEditMarker',
                    'submitDeleteMarker',
                    'hideAllMarkerForms'
                ];

                const missingMethods = methods.filter(method => typeof timelineManager[method] !== 'function');

                if (missingMethods.length === 0) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `✅ Alle ${methods.length} TimelineManager API-Methoden sind verfügbar`;
                    mockWidget.log(`TimelineManager API-Methoden verfügbar: ${methods.join(', ')}`);
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ Fehlende TimelineManager Methoden: ${missingMethods.join(', ')}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen der TimelineManager API-Methoden: ${error.message}`;
            }
        }

        function testUIRendererIntegration() {
            const resultDiv = document.getElementById('uiRendererTestResult');

            try {
                if (!mockWidget) {
                    throw new Error('Mock Widget muss zuerst initialisiert werden');
                }

                uiRenderer = new FloweringUIRenderer(mockWidget);

                if (uiRenderer && uiRenderer.widget === mockWidget) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ UIRenderer erfolgreich mit Widget integriert';
                    mockWidget.log('UIRenderer erfolgreich initialisiert');
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ UIRenderer Integration fehlgeschlagen';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler bei der UIRenderer Integration: ${error.message}`;
            }
        }

        function testUIRendererAPIMethods() {
            const resultDiv = document.getElementById('uiRendererApiTestResult');

            try {
                if (!uiRenderer) {
                    throw new Error('UIRenderer muss zuerst initialisiert werden');
                }

                const methods = [
                    'updateOverview',
                    'updateProgressCircle',
                    'showSuccess',
                    'showError',
                    'showInfo',
                    'updatePrediction',
                    'updateMilestones',
                    'updateFlushTriggerStatus',
                    'showHarvestPrediction',
                    'updateTriggerConditions',
                    'updateSliderSum',
                    'updateFlushProgress'
                ];

                const missingMethods = methods.filter(method => typeof uiRenderer[method] !== 'function');

                if (missingMethods.length === 0) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `✅ Alle ${methods.length} UIRenderer API-Methoden sind verfügbar`;
                    mockWidget.log(`UIRenderer API-Methoden verfügbar: ${methods.join(', ')}`);
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ Fehlende UIRenderer Methoden: ${missingMethods.join(', ')}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen der UIRenderer API-Methoden: ${error.message}`;
            }
        }

        function testDataManagerIntegration() {
            const resultDiv = document.getElementById('dataManagerTestResult');

            try {
                if (!mockWidget) {
                    throw new Error('Mock Widget muss zuerst initialisiert werden');
                }

                dataManager = new FloweringDataManager(mockWidget);

                if (dataManager && dataManager.widget === mockWidget) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ DataManager erfolgreich mit Widget integriert';
                    mockWidget.log('DataManager erfolgreich initialisiert');
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ DataManager Integration fehlgeschlagen';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler bei der DataManager Integration: ${error.message}`;
            }
        }

        function testDataManagerAPIMethods() {
            const resultDiv = document.getElementById('dataManagerApiTestResult');

            try {
                if (!dataManager) {
                    throw new Error('DataManager muss zuerst initialisiert werden');
                }

                const methods = [
                    'getPlantIdFromDOM',
                    'getCacheKey',
                    'setCache',
                    'getCache',
                    'clearCache',
                    'apiCall',
                    'loadFloweringStatus',
                    'loadFlushTriggerStatus',
                    'loadPrediction',
                    'loadTrichomeStatus',
                    'loadTrichomeRecommendation',
                    'loadTrichomeProgress',
                    'loadMarkers',
                    'loadLightingData',
                    'saveData',
                    'deleteData',
                    'setLocalStorage',
                    'getLocalStorage',
                    'removeLocalStorage',
                    'validatePlantData',
                    'validateTrichomeData',
                    'formatDate',
                    'formatPercentage',
                    'cleanup'
                ];

                const missingMethods = methods.filter(method => typeof dataManager[method] !== 'function');

                if (missingMethods.length === 0) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `✅ Alle ${methods.length} DataManager API-Methoden sind verfügbar`;
                    mockWidget.log(`DataManager API-Methoden verfügbar: ${methods.join(', ')}`);
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ Fehlende DataManager Methoden: ${missingMethods.join(', ')}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen der DataManager API-Methoden: ${error.message}`;
            }
        }

        function testFormManagerIntegration() {
            const resultDiv = document.getElementById('formManagerTestResult');

            try {
                if (!mockWidget) {
                    throw new Error('Mock Widget muss zuerst initialisiert werden');
                }

                formManager = new FloweringFormManager(mockWidget);

                if (formManager && formManager.widget === mockWidget) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ FormManager erfolgreich mit Widget integriert';
                    mockWidget.log('FormManager erfolgreich initialisiert');
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ FormManager Integration fehlgeschlagen';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler bei der FormManager Integration: ${error.message}`;
            }
        }

        function testFormManagerAPIMethods() {
            const resultDiv = document.getElementById('formManagerApiTestResult');

            try {
                if (!formManager) {
                    throw new Error('FormManager muss zuerst initialisiert werden');
                }

                const methods = [
                    'setupEventListeners',
                    'setupObservationForm',
                    'setupMarkerForms',
                    'setupSliderListeners',
                    'showObservationForm',
                    'hideObservationForm',
                    'showEditForm',
                    'showDeleteForm',
                    'showEditMarkerForm',
                    'hideEditMarkerForm',
                    'showDeleteMarkerForm',
                    'hideDeleteMarkerForm',
                    'hideAllForms',
                    'hideAllMarkerForms',
                    'hideAllEditForms',
                    'resetObservationForm',
                    'setDefaultFormValues',
                    'updateSliderDisplays',
                    'updateSliderSum',
                    'clearObservationIndicators',
                    'submitObservation',
                    'validateObservationData',
                    'collectFormData',
                    'setButtonLoading',
                    'cleanup'
                ];

                const missingMethods = methods.filter(method => typeof formManager[method] !== 'function');

                if (missingMethods.length === 0) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `✅ Alle ${methods.length} FormManager API-Methoden sind verfügbar`;
                    mockWidget.log(`FormManager API-Methoden verfügbar: ${methods.join(', ')}`);
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ Fehlende FormManager Methoden: ${missingMethods.join(', ')}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen der FormManager API-Methoden: ${error.message}`;
            }
        }

        function testValidationManagerIntegration() {
            const resultDiv = document.getElementById('validationManagerTestResult');

            try {
                if (!mockWidget) {
                    throw new Error('Mock Widget muss zuerst initialisiert werden');
                }

                validationManager = new FloweringValidationManager(mockWidget);

                if (validationManager && validationManager.widget === mockWidget) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ ValidationManager erfolgreich mit Widget integriert';
                    mockWidget.log('ValidationManager erfolgreich initialisiert');
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ ValidationManager Integration fehlgeschlagen';
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler bei der ValidationManager Integration: ${error.message}`;
            }
        }

        function testValidationManagerAPIMethods() {
            const resultDiv = document.getElementById('validationManagerApiTestResult');

            try {
                if (!validationManager) {
                    throw new Error('ValidationManager muss zuerst initialisiert werden');
                }

                const methods = [
                    'initializeValidationRules',
                    'initializeErrorMessages',
                    'validateTrichomeData',
                    'validateDate',
                    'validateBloomDay',
                    'validateMarkerData',
                    'validatePlantData',
                    'validateObservationData',
                    'normalizeNumber',
                    'isInRange',
                    'validateEmail',
                    'validateUrl',
                    'validateDataIntegrity',
                    'validateFormData',
                    'validateApiResponse',
                    'validateBatchData',
                    'formatErrorMessage',
                    'displayValidationResult',
                    'updateValidationRules',
                    'cleanup'
                ];

                const missingMethods = methods.filter(method => typeof validationManager[method] !== 'function');

                if (missingMethods.length === 0) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `✅ Alle ${methods.length} ValidationManager API-Methoden sind verfügbar`;
                    mockWidget.log(`ValidationManager API-Methoden verfügbar: ${methods.join(', ')}`);
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `❌ Fehlende ValidationManager Methoden: ${missingMethods.join(', ')}`;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen der ValidationManager API-Methoden: ${error.message}`;
            }
        }

        function testValidationFunctionality() {
            const resultDiv = document.getElementById('validationFunctionalityTestResult');

            try {
                if (!validationManager) {
                    throw new Error('ValidationManager muss zuerst initialisiert werden');
                }

                let allTestsPassed = true;
                const testResults = [];

                // Test 1: Gültige Trichom-Daten
                const validTrichome = validationManager.validateTrichomeData({
                    clear_percentage: 30,
                    milky_percentage: 50,
                    amber_percentage: 20
                });
                if (validTrichome.valid) {
                    testResults.push('✅ Gültige Trichom-Daten erkannt');
                } else {
                    testResults.push('❌ Gültige Trichom-Daten fälschlicherweise abgelehnt');
                    allTestsPassed = false;
                }

                // Test 2: Ungültige Trichom-Summe
                const invalidTrichome = validationManager.validateTrichomeData({
                    clear_percentage: 50,
                    milky_percentage: 50,
                    amber_percentage: 50
                });
                if (!invalidTrichome.valid) {
                    testResults.push('✅ Ungültige Trichom-Summe erkannt');
                } else {
                    testResults.push('❌ Ungültige Trichom-Summe nicht erkannt');
                    allTestsPassed = false;
                }

                // Test 3: Zukunftsdatum
                const futureDate = new Date();
                futureDate.setDate(futureDate.getDate() + 1);
                const futureDateString = futureDate.toISOString().split('T')[0];
                console.log('🔍 Test Debug: Zukunftsdatum-Test', {
                    futureDate: futureDateString,
                    today: new Date().toISOString().split('T')[0]
                });
                const futureDateResult = validationManager.validateDate(futureDateString);
                console.log('🔍 Test Debug: Validierungsergebnis', futureDateResult);
                if (!futureDateResult.valid) {
                    testResults.push('✅ Zukunftsdatum erkannt');
                } else {
                    testResults.push('❌ Zukunftsdatum nicht erkannt');
                    allTestsPassed = false;
                }

                // Test 4: Gültiger Blütetag
                const validBloomDay = validationManager.validateBloomDay(45);
                if (validBloomDay.valid) {
                    testResults.push('✅ Gültiger Blütetag erkannt');
                } else {
                    testResults.push('❌ Gültiger Blütetag fälschlicherweise abgelehnt');
                    allTestsPassed = false;
                }

                // Test 5: Ungültiger Blütetag
                const invalidBloomDay = validationManager.validateBloomDay(-5);
                if (!invalidBloomDay.valid) {
                    testResults.push('✅ Ungültiger Blütetag erkannt');
                } else {
                    testResults.push('❌ Ungültiger Blütetag nicht erkannt');
                    allTestsPassed = false;
                }

                if (allTestsPassed) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = '✅ Alle Validierungstests bestanden:<br>' + testResults.join('<br>');
                    mockWidget.log('ValidationManager Funktionalitätstests erfolgreich');
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = '❌ Einige Validierungstests fehlgeschlagen:<br>' + testResults.join('<br>');
                }

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen der ValidationManager Funktionalität: ${error.message}`;
            }
        }

        function testNotificationSystem() {
            const resultDiv = document.getElementById('notificationTestResult');

            try {
                if (!uiRenderer) {
                    throw new Error('UIRenderer muss zuerst initialisiert werden');
                }

                // Test verschiedene Notification-Typen
                uiRenderer.showSuccess('Test Success Notification');
                uiRenderer.showError('Test Error Notification');
                uiRenderer.showInfo('Test Info Notification');

                resultDiv.className = 'test-result success';
                resultDiv.innerHTML = '✅ Notification-System getestet - Schaue rechts oben für die Notifications!';
                mockWidget.log('Notification-System erfolgreich getestet');

                // Cleanup nach 6 Sekunden
                setTimeout(() => {
                    if (uiRenderer.cleanup) {
                        uiRenderer.cleanup();
                    }
                }, 6000);

            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `❌ Fehler beim Testen des Notification-Systems: ${error.message}`;
            }
        }

        function clearLog() {
            document.getElementById('consoleLog').innerHTML = '';
        }

        // Automatische Tests beim Laden der Seite
        window.addEventListener('load', () => {
            setTimeout(() => {
                testModuleAvailability();
            }, 100);
        });
    </script>
</body>
</html>
