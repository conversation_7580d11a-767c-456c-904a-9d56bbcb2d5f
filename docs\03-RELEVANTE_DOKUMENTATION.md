# 📚 Relevante Dokumentation - Grow-Tagebuch

**Index:** 03 - Relevante Dokumentation  
**Datum:** 11.07.2025  
**Zweck:** Übersicht der relevanten Dokumentation für die Basis-Version  
**Status:** Dokumentationssammlung

---

## ⭐ **AKTUELL RELEVANT (für Basis-Version)**

### **1. `_reports/FULL_VERSION_ANALYSE.md`** 🔥
- **Zweck:** Vollständige Blaupause der Full-Version
- **Inhalt:** Architektur, API-Endpunkte, Datenbank-Schema, Frontend-Module
- **Verwendung:** Referenz für alle Funktionen, die migriert werden sollen
- **Status:** ✅ Vollständig erstellt

### **2. `_reports/BASIS_VERSION_PLAN.md`** 🔥
- **Zweck:** Aktueller Entwicklungsplan für Basis-Version
- **Inhalt:** Best Practices, Hybrid-Konzept, Feature-Flags, Ordner-Struktur
- **Verwendung:** Blaupause für die neue Entwicklung
- **Status:** ✅ Vollständig erstellt

### **3. `README.md`** ⚠️
- **Zweck:** Projekt-Übersicht (muss aktualisiert werden)
- **Inhalt:** Installation, Features, Verwendung
- **Verwendung:** Muss für Basis-Version angepasst werden
- **Status:** 🔄 Muss aktualisiert werden

### **4. `docs/BLUETE_MANAGEMENT_WIDGET.md`** 🌺
- **Zweck:** Dokumentation des neuen Blüte-Management-Widgets
- **Inhalt:** Widget-Tabs, API-Endpunkte, Guideline-Integration, Marker-System, Trichome-Analyse, Flush-Trigger, Prognose
- **Verwendung:** Referenz für das integrierte Blüte-Management-System
- **Status:** ✅ Vollständig erstellt (13.07.2025)

---

## 📚 **ARCHIV - WICHTIGE REFERENZEN**

### **4. `_reports/AKTUELLE_FUNKTIONALITAET.md`**
- **Zweck:** Status der Full-Version (Juli 2025)
- **Inhalt:** Kritische Funktionen, JavaScript-Module, API-Endpoints
- **Verwendung:** Prüfung, ob alle wichtigen Features migriert wurden
- **Relevanz:** Hoch - Vollständigkeits-Check

### **5. `FEATURES_OVERVIEW.md`**
- **Zweck:** Detaillierte Feature-Übersicht
- **Inhalt:** Alle implementierten Features mit Status
- **Verwendung:** Feature-Planung für Plugin-System
- **Relevanz:** Hoch - Feature-Referenz

### **6. `ROADMAP.md`**
- **Zweck:** Entwicklungs-Roadmap
- **Inhalt:** Kurz-/Mittelfristige Ziele, technische Verbesserungen
- **Verwendung:** Planung der Basis-Version Phasen
- **Relevanz:** Mittel - Entwicklungs-Planung

### **7. `PROJEKT_BESCHREIBUNG.md`**
- **Zweck:** Projekt-Beschreibung und Ziele
- **Inhalt:** Technische Architektur, Kernfunktionen, Statistiken
- **Verwendung:** Verständnis der Projekt-Ziele
- **Relevanz:** Mittel - Projekt-Kontext

---

## 🔧 **TECHNISCHE REFERENZEN**

### **8. `_reports/ALLE_MESSGERAET_BESCHRAENKUNGEN_ENTFERNT.md`**
- **Zweck:** Technische Details zu Messgeräten
- **Inhalt:** Sensor-Integrationen, Bluetooth, APERO
- **Verwendung:** Plugin-Entwicklung für Sensor-System
- **Relevanz:** Niedrig - Für Plugin-System

### **9. `_reports/APERO_BLE_ANALYSE_OPTIONEN.md`**
- **Zweck:** APERO Pen Integration Details
- **Inhalt:** Bluetooth-Analyse, Messoptionen
- **Verwendung:** APERO-Plugin Entwicklung
- **Relevanz:** Niedrig - Für Sensor-Plugin

---

## 🗂️ **ARCHIVIERUNG-STRATEGIE**

### **Behalten (für Basis-Version):**
- ✅ `FULL_VERSION_ANALYSE.md` - Hauptreferenz
- ✅ `BASIS_VERSION_PLAN.md` - Aktueller Plan
- ✅ `AKTUELLE_FUNKTIONALITAET.md` - Vollständigkeits-Check
- ✅ `FEATURES_OVERVIEW.md` - Feature-Referenz

### **Aktualisieren:**
- 🔄 `README.md` - Für Basis-Version anpassen
- 🔄 `ROADMAP.md` - Neue Phasen hinzufügen

### **Archivieren (später):**
- 📦 `PROJEKT_BESCHREIBUNG.md` - Historisch
- 📦 `ALLE_MESSGERAET_BESCHRAENKUNGEN_ENTFERNT.md` - Technisch
- 📦 `APERO_BLE_ANALYSE_OPTIONEN.md` - Technisch

---

## 🎯 **VERWENDUNG FÜR BASIS-VERSION**

### **Phase 1: Planung**
1. **`BASIS_VERSION_PLAN.md`** - Entwicklungsplan
2. **`FULL_VERSION_ANALYSE.md`** - Funktions-Referenz

### **Phase 2: Entwicklung**
1. **`AKTUELLE_FUNKTIONALITAET.md`** - Vollständigkeits-Check
2. **`FEATURES_OVERVIEW.md`** - Feature-Planung

### **Phase 3: Plugin-System**
1. **Technische Referenzen** - Sensor-Integrationen
2. **`ROADMAP.md`** - Erweiterte Planung

### **Phase 4: Dokumentation**
1. **`README.md`** - Aktualisieren
2. **Neue Dokumentation** - Basis-Version

---

## 📝 **NÄCHSTE SCHRITTE**

1. **Backup erstellen** - Full-Version sichern
2. **Basis-Version entwickeln** - Nach Plan
3. **Dokumentation aktualisieren** - Schrittweise
4. **Vollständigkeits-Check** - Gegen alle Referenzen

---

**Diese Übersicht stellt sicher, dass wir keine wichtigen Features oder Ideen vergessen!** 