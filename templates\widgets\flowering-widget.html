<!-- Blüte-Zeitmanagement Widget -->
<div class="flowering-widget-container">
    <!-- Header mit Pflanze-Info -->
    <div class="widget-header">
        <div class="plant-info">
            <h1>🌺 Blüte-Zeitmanagement</h1>
            <div class="plant-details">
                <span class="plant-id" data-plant-id="{{ plant.id }}">{{ plant.id }}</span>
                <span class="strain-name" id="strainName">{{ plant.strain or 'Sorte nicht angegeben' }}</span>
            </div>
        </div>
        <div class="current-status">
            <div class="bloom-day">
                <span class="day-number" id="currentDay">41</span>
                <span class="day-label">Blüte-Tag</span>
            </div>
            <div class="progress-circle">
                <div class="progress-ring">
                    <svg width="60" height="60">
                        <circle class="progress-ring-bg" cx="30" cy="30" r="25"></circle>
                        <circle class="progress-ring-fill" cx="30" cy="30" r="25" id="progressCircle"></circle>
                    </svg>
                    <span class="progress-text" id="progressText">--</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Hauptbereich mit Tabs -->
    <div class="widget-main">
        <div class="tab-navigation">
            <button class="tab-btn active" data-tab="overview">Übersicht</button>
            <button class="tab-btn" data-tab="trichome">Trichome</button>
            <button class="tab-btn" data-tab="lighting">Beleuchtung</button>

            <button class="tab-btn" data-tab="markers">Marker</button>
            <button class="tab-btn" data-tab="flush-trigger">Flush-Trigger</button>
            <button class="tab-btn" data-tab="prediction">Prognose</button>
        </div>

        <!-- Tab-Inhalte -->
        <div class="tab-content">
            <!-- Übersicht Tab -->
            <div class="tab-pane active" id="overview">
                <div class="overview-grid">
                    <!-- Aktuelle Phase -->
                    <div class="info-card phase-card">
                        <h3>🎯 Aktuelle Phase <span class="phase-percentage" id="phasePercentage">(Grow: --% | Blüte: --%)</span></h3>
                        <div class="phase-info">
                            <div class="phase-name" id="currentPhase">--</div>
                            <div class="phase-progress">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="phaseProgress" style="width: 0%"></div>
                                </div>
                                <span class="progress-label" id="phaseProgressText">--%</span>
                            </div>
                        </div>
                        <div class="phase-description" id="phaseDescription">
                            Lade Phasen-Informationen...
                        </div>
                    </div>

                    <!-- Flush-Status -->
                    <div class="info-card flush-card" id="flushCard">
                        <h3>🚿 Flush-Status</h3>
                        <div class="flush-info">
                            <div class="flush-status" id="flushStatus">Noch nicht empfohlen</div>
                            <div class="flush-countdown" id="flushCountdown">
                                <span class="days-remaining">20</span>
                                <span class="countdown-label">Tage bis Flush</span>
                            </div>
                        </div>
                        <div class="flush-reason" id="flushReason">
                            Automatische Empfehlung basierend auf Trichome-Status
                        </div>
                    </div>

                    <!-- Ernte-Prognose -->
                    <div class="info-card harvest-card">
                        <h3>✂️ Ernte-Prognose</h3>
                        <div class="harvest-windows">
                            <div class="harvest-option early">
                                <span class="harvest-label">Früh</span>
                                <span class="harvest-days" id="harvestEarly">Tag 60-64</span>
                            </div>
                            <div class="harvest-option optimal">
                                <span class="harvest-label">Optimal</span>
                                <span class="harvest-days" id="harvestOptimal">Tag 65-72</span>
                            </div>
                            <div class="harvest-option late">
                                <span class="harvest-label">Spät</span>
                                <span class="harvest-days" id="harvestLate">Tag 73-78</span>
                            </div>
                        </div>
                    </div>

                    <!-- Nächste Meilensteine -->
                    <div class="info-card milestones-card">
                        <h3>🎯 Nächste Meilensteine</h3>
                        <div class="milestones-list" id="milestonesList">
                            <!-- Wird dynamisch gefüllt -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Trichome Tab -->
            <div class="tab-pane" id="trichome">
                <div class="trichome-container">
                    <div class="trichome-header">
                        <h3>🔬 Trichom-Analyse & Flush-Trigger</h3>
                        <div class="trichome-status-indicator" id="trichomeStatusIndicator">
                            <span class="status-dot" id="trichomeStatusDot"></span>
                            <span class="status-text" id="trichomeStatusText">Lade Trichom-Status...</span>
                        </div>
                    </div>

                    <!-- Trichom-Status-Karte -->
                    <div class="trichome-status-card" id="trichomeStatusCard">
                        <div class="status-header">
                            <h4>📊 Aktueller Trichom-Status</h4>
                            <div class="last-update" id="lastTrichomeUpdate">Letzte Analyse: --</div>
                        </div>
                        
                        <!-- Status-Badge -->
                        <div class="trichome-status-badge" id="trichomeStatusBadge">
                            <span class="badge-icon" id="badgeIcon">🔬</span>
                            <span class="badge-text" id="badgeText">Trichom-Status wird geladen...</span>
                        </div>
                        
                        <!-- Trichom-Segmente Container -->
                        <div class="trichome-segments-container">
                            <h5>Trichom-Verteilung</h5>
                            <div class="trichome-segments" id="trichomeSegments">
                                <div class="trichome-segment clear-segment" id="clearSegmentBar">
                                    <div class="segment-label">Klar</div>
                                    <div class="segment-value" id="clearValueBar">0%</div>
                                </div>
                                <div class="trichome-segment milky-segment" id="milkySegmentBar">
                                    <div class="segment-label">Milchig</div>
                                    <div class="segment-value" id="milkyValueBar">0%</div>
                                </div>
                                <div class="trichome-segment amber-segment" id="amberSegmentBar">
                                    <div class="segment-label">Bernstein</div>
                                    <div class="segment-value" id="amberValueBar">0%</div>
                                </div>
                            </div>
                        </div>

                        <!-- Flush-Warnung Alert -->
                        <div class="flush-alert" id="flushAlert" style="display: none;">
                            <div class="alert-content">
                                <span class="alert-icon" id="alertIcon">🚰</span>
                                <div class="alert-text">
                                    <strong id="alertTitle">Flush empfohlen:</strong>
                                    <span id="alertMessage">Zielverhältnis erreicht</span>
                                </div>
                            </div>
                        </div>

                        <div class="trichome-summary">
                            <div class="maturity-level" id="maturityLevel">
                                <span class="level-label">Reifegrad:</span>
                                <span class="level-value" id="maturityLevelValue">--</span>
                            </div>
                            <div class="flush-progress">
                                <span class="progress-label">Flush-Fortschritt:</span>
                                <div class="progress-circle small">
                                    <svg width="40" height="40">
                                        <circle class="progress-ring-bg" cx="20" cy="20" r="15"></circle>
                                        <circle class="progress-ring-fill" cx="20" cy="20" r="15" id="flushProgressCircle"></circle>
                                    </svg>
                                    <span class="progress-text" id="flushProgressText">0%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Flush-Trigger-Karte -->
                    <div class="flush-trigger-card" id="flushTriggerCard">
                        <div class="trigger-header">
                            <h4>🚿 Flush-Trigger</h4>
                            <div class="trigger-indicator" id="triggerIndicator">
                                <span class="trigger-dot" id="triggerDot"></span>
                                <span class="trigger-text" id="triggerText">Überwachung aktiv</span>
                            </div>
                        </div>
                        
                        <div class="trigger-content">
                            <div class="trigger-message" id="triggerMessage">
                                Trichom-Status wird überwacht...
                            </div>
                            <div class="trigger-actions">
                                <button class="btn btn-primary" id="manualFlushBtn" disabled>Flush manuell starten</button>
                            </div>
                        </div>
                    </div>



                    <!-- Trichom-Empfehlung -->
                    <div class="trichome-recommendation-card" id="trichomeRecommendationCard">
                        <div class="recommendation-header">
                            <h4>💡 Trichom-Empfehlung</h4>
                            <div class="urgency-badge" id="urgencyBadge">
                                <span class="urgency-text" id="urgencyText">Niedrig</span>
                            </div>
                        </div>
                        
                        <div class="recommendation-content">
                            <div class="recommendation-message" id="recommendationMessage">
                                Trichom-Daten werden geladen...
                            </div>
                            <div class="recommendation-action" id="recommendationAction">
                                <span class="action-text" id="actionText">--</span>
                            </div>
                        </div>
                    </div>

                    <!-- Trichom-Fortschritt -->
                    <div class="trichome-progress-card" id="trichomeProgressCard">
                        <div class="progress-header">
                            <h4>📈 Trichom-Fortschritt</h4>
                            <div class="progress-info" id="progressInfo">
                                <span class="entries-count" id="entriesCount">0 Einträge</span>
                            </div>
                        </div>
                        
                        <div class="progress-content">
                            <div class="trichome-columns">
                                <div class="trichome-column clear-column">
                                    <div class="column-icon">💎</div>
                                    <div class="column-label">Klar</div>
                                    <div class="column-value" id="clearTrendValue">0%</div>
                                    <div class="column-trend">
                                        <span class="trend-arrow" id="clearTrend">→</span>
                                    </div>
                                </div>
                                <div class="trichome-column milky-column">
                                    <div class="column-icon">🥛</div>
                                    <div class="column-label">Milchig</div>
                                    <div class="column-value" id="milkyTrendValue">0%</div>
                                    <div class="column-trend">
                                        <span class="trend-arrow" id="milkyTrend">→</span>
                                    </div>
                                </div>
                                <div class="trichome-column amber-column">
                                    <div class="column-icon">🍯</div>
                                    <div class="column-label">Bernstein</div>
                                    <div class="column-value" id="amberTrendValue">0%</div>
                                    <div class="column-trend">
                                        <span class="trend-arrow" id="amberTrend">→</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Trichom-Beobachtungslog -->
                    <div class="trichome-observation-card" id="trichomeObservationCard">
                        <div class="observation-header">
                            <h4>📋 Beobachtungslog</h4>
                            <button class="btn btn-secondary btn-sm" id="addObservationBtn">Neuer Eintrag</button>
                        </div>
                        
                        <!-- Zentrale Aktionsformulare für Bearbeiten & Löschen -->
                        <div id="observationActionFormContainer"></div>
                        
                        <!-- Trichom-Beobachtungsformular (direkt sichtbar) -->
                        <div class="observation-form-container" id="observationFormContainer" style="display: none;">
                            <div class="form-card compact">
                                <h5>🔬 Neue Trichom-Beobachtung</h5>
                                <form id="trichomeObservationForm">
                                    <div class="form-row compact">
                                        <div class="form-group">
                                            <label for="observationDate">Datum:</label>
                                            <input type="date" id="observationDate" name="date" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="observationBloomDay">Blüte-Tag:</label>
                                            <input type="number" id="observationBloomDay" name="bloom_day" min="1" max="100" required>
                                        </div>
                                    </div>
                                    
                                    <!-- Location als Radio-Buttons -->
                                    <div class="form-group">
                                        <label>Ort:</label>
                                        <div class="radio-group">
                                            <label class="radio-option">
                                                <input type="radio" name="location" value="Top Buds" required checked>
                                                <span class="radio-label">Top Buds</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="location" value="Side Buds">
                                                <span class="radio-label">Side Buds</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="location" value="Lower Buds">
                                                <span class="radio-label">Lower Buds</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="location" value="Sugar Leaves">
                                                <span class="radio-label">Sugar Leaves</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="location" value="Fan Leaves">
                                                <span class="radio-label">Fan Leaves</span>
                                            </label>
                                            <label class="radio-option">
                                                <input type="radio" name="location" value="Mixed">
                                                <span class="radio-label">Mixed</span>
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <!-- Trichom-Werte als Schieberegler -->
                                    <div class="form-group">
                                        <label>Trichom-Verteilung:</label>
                                        <div class="slider-group">
                                            <div class="slider-item">
                                                <label for="clearPercentage">Klar: <span id="clearPercentageDisplay">0</span>%</label>
                                                <input type="range" id="clearPercentage" name="clear_percentage" min="0" max="100" value="0" class="trichome-slider clear-slider">
                                            </div>
                                            <div class="slider-item">
                                                <label for="milkyPercentage">Milchig: <span id="milkyPercentageDisplay">0</span>%</label>
                                                <input type="range" id="milkyPercentage" name="milky_percentage" min="0" max="100" value="0" class="trichome-slider milky-slider">
                                            </div>
                                            <div class="slider-item">
                                                <label for="amberPercentage">Bernstein: <span id="amberPercentageDisplay">0</span>%</label>
                                                <input type="range" id="amberPercentage" name="amber_percentage" min="0" max="100" value="0" class="trichome-slider amber-slider">
                                            </div>
                                        </div>
                                        
                                        <!-- Live-Summenanzeige -->
                                        <div class="percentage-summary">
                                            <div class="sum-display" id="sumDisplay">
                                                <span class="sum-label">Summe:</span>
                                                <span class="sum-value" id="sumValue">0%</span>
                                            </div>
                                            <div class="sum-warning" id="sumWarning" style="display: none;">
                                                <span class="warning-icon">⚠️</span>
                                                <span class="warning-text">Summe über 100%!</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="observationNotes">Notizen:</label>
                                        <textarea id="observationNotes" name="notes" rows="2" placeholder="Zusätzliche Beobachtungen..."></textarea>
                                    </div>
                                    <div class="form-actions compact">
                                        <button type="submit" class="btn btn-primary btn-sm">Speichern</button>
                                        <button type="button" class="btn btn-secondary btn-sm" id="cancelObservationBtn">Abbrechen</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="observation-content">
                            <div class="observation-list" id="observationList">
                                <!-- Wird dynamisch gefüllt -->
                                <div class="observation-placeholder">
                                    <span class="placeholder-text">Keine Beobachtungen verfügbar</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Trichome-Guidelines -->
                    <div class="trichome-guidelines">
                        <h4>🔬 Trichome-Guidelines</h4>
                        <div class="guidelines-tabs">
                            <button class="guideline-tab active" data-tab="startbedingungen">Event-Marker</button>
                            <button class="guideline-tab" data-tab="methoden">Ernte-Empfehlungen</button>
                            <button class="guideline-tab" data-tab="fehlerquellen">Strain-Abhängigkeit</button>
                            <button class="guideline-tab" data-tab="faustregeln">Faustregeln</button>
                        </div>
                        
                        <div class="guidelines-content">
                            <div class="guideline-panel active" id="trichomeStartbedingungenContent">
                                <!-- Wird dynamisch gefüllt -->
                            </div>
                            <div class="guideline-panel" id="trichomeMethodenContent">
                                <!-- Wird dynamisch gefüllt -->
                            </div>
                            <div class="guideline-panel" id="trichomeFehlerquellenContent">
                                <!-- Wird dynamisch gefüllt -->
                            </div>
                            <div class="guideline-panel" id="trichomeFaustregelnContent">
                                <!-- Wird dynamisch gefüllt -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Beleuchtung Tab -->
            <div class="tab-pane" id="lighting">
                <div class="lighting-container">
                    <div class="lighting-header">
                        <h3>💡 Blüte-Beleuchtung</h3>
                        <div class="lighting-status" id="lightingStatus">
                            <span class="status-indicator" id="lightingStatusIndicator"></span>
                            <span class="status-text" id="lightingStatusText">Lade Beleuchtungsdaten...</span>
                        </div>
                    </div>
                    
                    <!-- Beleuchtungs-Einstellungen -->
                    <div class="lighting-settings" id="lightingSettings">
                        <!-- Wird dynamisch durch JavaScript gefüllt -->
                    </div>
                    
                    <!-- Beleuchtungs-Übersicht -->
                    <div class="lighting-overview" id="lightingOverview">
                        <div class="text-center">
                            <i class="fa fa-spinner fa-spin"></i>
                            <p>Beleuchtungsdaten werden geladen...</p>
                        </div>
                    </div>
                    
                    <!-- Beleuchtungs-Guidelines -->
                    <div class="lighting-guidelines" id="lightingGuidelines">
                        <!-- Wird dynamisch durch JavaScript gefüllt -->
                    </div>
                    
                    <!-- Phase 6 Features Container -->
                    <div class="phase6-features" id="phase6Features">
                        <!-- KI-Empfehlungen, Smart Schedule, Pattern Recognition, IoT Status werden hier eingefügt -->
                    </div>
                </div>
            </div>



            <!-- Marker Tab -->
            <div class="tab-pane" id="markers">
                <div class="markers-container">
                    <div class="markers-header">
                        <h3>📍 Marker-Verwaltung</h3>
                        <button class="btn btn-primary" id="addMarkerBtn">Neuer Marker</button>
                    </div>
                    
                    <!-- Inline Marker-Formular -->
                    <div class="marker-form-container" id="markerFormContainer" style="display: none;">
                        <div class="marker-form-card">
                            <div class="form-header">
                                <h4>📝 Neuen Marker hinzufügen</h4>
                                <button class="btn-close" id="closeMarkerForm">×</button>
                            </div>
                            <form id="markerForm" class="marker-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="markerDate">Datum:</label>
                                        <input type="date" id="markerDate" name="date" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="markerBloomDay">Blüte-Tag (BT):</label>
                                        <input type="number" id="markerBloomDay" name="bloom_day" min="0" max="100" required>
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="markerType">Event-Typ:</label>
                                        <select id="markerType" name="event_type" required>
                                            <option value="">Event-Typ wählen...</option>
                                            <option value="stretch_end">Stretch beendet</option>
                                            <option value="trichome_milky">Milchige Trichome</option>
                                            <option value="trichome_amber">Bernstein Trichome</option>
                                            <option value="flush_start">Flush gestartet</option>
                                            <option value="harvest_window">Ernte-Fenster</option>
                                            <option value="pistils_retracted">Pistillen zurückgezogen</option>
                                            <option value="custom">Benutzerdefiniert</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="markerTitle">Titel:</label>
                                        <input type="text" id="markerTitle" name="event_name" placeholder="Marker-Titel" required>
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="markerCategory">Kategorie:</label>
                                        <select id="markerCategory" name="category" required>
                                            <option value="">Kategorie wählen...</option>
                                            <option value="growth">Wachstum</option>
                                            <option value="maturity">Reifung</option>
                                            <option value="flush">Flush</option>
                                            <option value="stress">Stress</option>
                                            <option value="harvest">Ernte</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="markerImportance">Wichtigkeit:</label>
                                        <select id="markerImportance" name="importance" required>
                                            <option value="">Wichtigkeit wählen...</option>
                                            <option value="high">Hoch</option>
                                            <option value="medium">Mittel</option>
                                            <option value="low">Niedrig</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="markerDescription">Beschreibung:</label>
                                    <textarea id="markerDescription" name="notes" rows="3" placeholder="Detaillierte Beschreibung des Markers..."></textarea>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="button" class="btn btn-secondary" id="cancelMarkerForm">Abbrechen</button>
                                    <button type="submit" class="btn btn-primary" id="saveMarkerForm">Marker speichern</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- Marker-Filter -->
                    <div class="markers-filter">
                        <select id="categoryFilter" class="filter-select">
                            <option value="">Alle Kategorien</option>
                            <option value="growth">Wachstum</option>
                            <option value="maturity">Reifung</option>
                            <option value="flush">Flush</option>
                            <option value="stress">Stress</option>
                        </select>
                        <select id="importanceFilter" class="filter-select">
                            <option value="">Alle Wichtigkeiten</option>
                            <option value="high">Hoch</option>
                            <option value="medium">Mittel</option>
                            <option value="low">Niedrig</option>
                        </select>
                    </div>

                    <!-- Marker-Liste -->
                    <div class="markers-list" id="markersList">
                        <!-- Wird dynamisch gefüllt -->
                    </div>
                </div>
            </div>

            <!-- Flush-Trigger Tab -->
            <div class="tab-pane" id="flush-trigger">
                <div class="flush-trigger-container">
                    <div class="trigger-header">
                        <h3>🚿 Flush-Trigger-System</h3>
                        <div class="trigger-status" id="triggerStatus">
                            <span class="status-indicator" id="statusIndicator"></span>
                            <span class="status-text" id="statusText">Überwachung aktiv</span>
                        </div>
                    </div>

                    <!-- Trigger-Bedingungen -->
                    <div class="trigger-conditions">
                        <h4>🔍 Trigger-Bedingungen</h4>
                        <div class="conditions-grid">
                            <div class="condition-card" id="bloomProgressCondition">
                                <div class="condition-header">
                                    <span class="condition-icon">📊</span>
                                    <span class="condition-name">Blütefortschritt</span>
                                    <span class="condition-status" id="bloomProgressStatus">❌</span>
                                </div>
                                <div class="condition-details">
                                    <div class="condition-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill" id="bloomProgressBar" style="width: 54.7%"></div>
                                        </div>
                                        <span class="progress-text" id="bloomProgressText">Tag 41/75 (54.7%)</span>
                                    </div>
                                    <div class="condition-threshold">Schwelle: ≥85% (Tag 64)</div>
                                </div>
                            </div>

                            <div class="condition-card" id="trichomeCondition">
                                <div class="condition-header">
                                    <span class="condition-icon">🔬</span>
                                    <span class="condition-name">Trichome-Status</span>
                                    <span class="condition-status" id="trichomeStatus">❌</span>
                                </div>
                                <div class="condition-details">
                                    <div class="trichome-stats">
                                        <div class="trichome-stat">
                                            <span class="stat-label">Milchig:</span>
                                            <span class="stat-value" id="milkyCount">1 Marker</span>
                                        </div>
                                        <div class="trichome-stat">
                                            <span class="stat-label">Bernstein:</span>
                                            <span class="stat-value" id="amberCount">0 Marker</span>
                                        </div>
                                    </div>
                                    <div class="condition-threshold">Schwelle: ≥70% milchig</div>
                                </div>
                            </div>

                            <div class="condition-card" id="pistilCondition">
                                <div class="condition-header">
                                    <span class="condition-icon">🌸</span>
                                    <span class="condition-name">Pistillen-Status</span>
                                    <span class="condition-status" id="pistilStatus">❌</span>
                                </div>
                                <div class="condition-details">
                                    <div class="pistil-stats">
                                        <span class="stat-value" id="pistilCount">0 Marker</span>
                                    </div>
                                    <div class="condition-threshold">Schwelle: Pistillen zurückgezogen</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Manueller Trigger -->
                    <div class="manual-trigger">
                        <h4>🎛️ Manueller Trigger</h4>
                        <div class="manual-trigger-form">
                            <div class="form-group">
                                <label for="manualTriggerReason">Grund:</label>
                                <input type="text" id="manualTriggerReason" placeholder="z.B. Trichome-Kamera defekt">
                            </div>
                            <div class="form-group">
                                <label for="manualTriggerDay">Flush-Start-Tag:</label>
                                <input type="number" id="manualTriggerDay" value="41" min="1" max="100">
                            </div>
                            <button class="btn btn-warning" id="manualTriggerBtn">Flush manuell auslösen</button>
                        </div>
                    </div>

                    <!-- Flush-Status-Details -->
                    <div class="flush-status-details" id="flushStatusDetails">
                        <h4>📋 Flush-Status-Details</h4>
                        <div class="status-details-grid">
                            <div class="status-detail">
                                <span class="detail-label">Status:</span>
                                <span class="detail-value" id="flushStatusDetail">Nicht ausgelöst</span>
                            </div>
                            <div class="status-detail">
                                <span class="detail-label">Empfohlener Start:</span>
                                <span class="detail-value" id="flushStartDay">Tag 61</span>
                            </div>
                            <div class="status-detail">
                                <span class="detail-label">Flush-Dauer:</span>
                                <span class="detail-value" id="flushDuration">12 Tage</span>
                            </div>
                            <div class="status-detail">
                                <span class="detail-label">Ziel-Erntetag:</span>
                                <span class="detail-value" id="flushTargetDay">Tag 73</span>
                            </div>
                        </div>
                    </div>

                    <!-- Flush-Guidelines -->
                    <div class="flush-guidelines">
                        <h4>📚 Flush-Guidelines</h4>
                        <div class="guidelines-tabs">
                            <button class="guideline-tab active" data-tab="startbedingungen">Startbedingungen</button>
                            <button class="guideline-tab" data-tab="methoden">Methoden</button>
                            <button class="guideline-tab" data-tab="fehlerquellen">Fehlerquellen</button>
                            <button class="guideline-tab" data-tab="faustregeln">Faustregeln</button>
                        </div>
                        
                        <div class="guidelines-content">
                            <div class="guideline-panel active" id="flushStartbedingungenContent">
                                <!-- Wird dynamisch gefüllt -->
                            </div>
                            <div class="guideline-panel" id="flushMethodenContent">
                                <!-- Wird dynamisch gefüllt -->
                            </div>
                            <div class="guideline-panel" id="flushFehlerquellenContent">
                                <!-- Wird dynamisch gefüllt -->
                            </div>
                            <div class="guideline-panel" id="flushFaustregelnContent">
                                <!-- Wird dynamisch gefüllt -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prognose Tab -->
            <div class="tab-pane" id="prediction">
                <div class="prediction-container">
                    <div class="prediction-header">
                        <h3>🔮 Blüte-Prognose</h3>
                        <div class="prediction-confidence">
                            <span class="confidence-label">Konfidenz:</span>
                            <span class="confidence-value" id="predictionConfidence">Hoch</span>
                        </div>
                    </div>

                    <!-- Zeitliche Prognosen -->
                    <div class="prediction-timeline">
                        <div class="timeline-event flush-event">
                            <div class="event-icon">🚿</div>
                            <div class="event-content">
                                <div class="event-title">Flush-Start</div>
                                <div class="event-details">
                                    <div class="event-date" id="flushStartDate">20. Juli 2025</div>
                                    <div class="event-countdown" id="flushStartCountdown">20 Tage verbleibend</div>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-event harvest-event">
                            <div class="event-icon">✂️</div>
                            <div class="event-content">
                                <div class="event-title">Optimaler Erntezeitpunkt</div>
                                <div class="event-details">
                                    <div class="event-date" id="harvestOptimalDate">25. Juli 2025</div>
                                    <div class="event-countdown" id="harvestOptimalCountdown">25 Tage verbleibend</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Empfehlungen -->
                    <div class="recommendations">
                        <h4>💡 Empfehlungen</h4>
                        <div class="recommendations-list" id="recommendationsList">
                            <!-- Wird dynamisch gefüllt -->
                        </div>
                    </div>

                    <!-- Risikofaktoren -->
                    <div class="risk-factors">
                        <h4>⚠️ Risikofaktoren</h4>
                        <div class="risk-categories">
                            <div class="risk-category high" id="highRisks">
                                <h5>🔴 Hohe Risiken</h5>
                                <ul class="risk-list">
                                    <!-- Wird dynamisch gefüllt -->
                                </ul>
                            </div>
                            <div class="risk-category medium" id="mediumRisks">
                                <h5>🟡 Mittlere Risiken</h5>
                                <ul class="risk-list">
                                    <!-- Wird dynamisch gefüllt -->
                                </ul>
                            </div>
                            <div class="risk-category low" id="lowRisks">
                                <h5>🟢 Niedrige Risiken</h5>
                                <ul class="risk-list">
                                    <!-- Wird dynamisch gefüllt -->
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Guidelines Modal -->
<div class="modal fade" id="lightingGuidelinesModal" tabindex="-1" aria-labelledby="lightingGuidelinesModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="lightingGuidelinesModalLabel">
                    <i class="fa-solid fa-book me-2"></i>Vollständige Beleuchtungs-Richtlinien
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="guidelines-container">
                    <!-- Phase-spezifische Guidelines -->
                    <div class="guidelines-section" id="phaseGuidelines">
                        <h4>🌱 Phase-spezifische Beleuchtungsrichtlinien</h4>
                        <div class="guidelines-content" id="phaseGuidelinesContent">
                            <!-- Wird dynamisch gefüllt -->
                        </div>
                    </div>
                    
                    <!-- Technische Guidelines -->
                    <div class="guidelines-section">
                        <h4>⚙️ Technische Beleuchtungsrichtlinien</h4>
                        <div class="guidelines-grid">
                            <div class="guideline-card">
                                <h5>💡 PPFD (Photosynthetic Photon Flux Density)</h5>
                                <div class="guideline-content">
                                    <p><strong>Was ist PPFD?</strong> PPFD misst die Anzahl der Photosynthese-aktiven Photonen, die pro Sekunde auf eine Fläche von 1 m² treffen.</p>
                                    <div class="guideline-table">
                                        <div class="table-header">
                                            <span>Phase</span>
                                            <span>PPFD-Bereich</span>
                                            <span>Empfehlung</span>
                                        </div>
                                        <div class="table-row">
                                            <span>Vegetation</span>
                                            <span>250-600 μmol/m²/s</span>
                                            <span>Kompaktes Wachstum</span>
                                        </div>
                                        <div class="table-row">
                                            <span>Blüte</span>
                                            <span>600-1000 μmol/m²/s</span>
                                            <span>Maximale Blütenbildung</span>
                                        </div>
                                        <div class="table-row">
                                            <span>Flush</span>
                                            <span>450-750 μmol/m²/s</span>
                                            <span>Schonende Trichom-Entwicklung</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="guideline-card">
                                <h5>📊 DLI (Daily Light Integral)</h5>
                                <div class="guideline-content">
                                    <p><strong>Was ist DLI?</strong> DLI misst die Gesamtmenge an Photosynthese-aktiven Photonen pro Tag und Quadratmeter.</p>
                                    <div class="guideline-table">
                                        <div class="table-header">
                                            <span>Phase</span>
                                            <span>DLI-Bereich</span>
                                            <span>Berechnung</span>
                                        </div>
                                        <div class="table-row">
                                            <span>Vegetation</span>
                                            <span>12-30 mol/m²/Tag</span>
                                            <span>PPFD × Stunden × 0.0036</span>
                                        </div>
                                        <div class="table-row">
                                            <span>Blüte</span>
                                            <span>30-45 mol/m²/Tag</span>
                                            <span>PPFD × Stunden × 0.0036</span>
                                        </div>
                                        <div class="table-row">
                                            <span>Flush</span>
                                            <span>22-35 mol/m²/Tag</span>
                                            <span>PPFD × Stunden × 0.0036</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Spektrum-Optimierung -->
                    <div class="guidelines-section">
                        <h4>🌈 Spektrum-Optimierung</h4>
                        <div class="guidelines-grid">
                            <div class="guideline-card">
                                <h5>🔵 Blau-Licht (400-500nm)</h5>
                                <div class="guideline-content">
                                    <ul class="guideline-list">
                                        <li><strong>Vegetation:</strong> 20-30% für kompaktes Wachstum</li>
                                        <li><strong>Blüte:</strong> 10-15% für Blütenbildung</li>
                                        <li><strong>Wirkung:</strong> Kompakte Internodien, starke Struktur</li>
                                        <li><strong>Farbtemperatur:</strong> 4000-6500K</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="guideline-card">
                                <h5>🔴 Rot-Licht (600-700nm)</h5>
                                <div class="guideline-content">
                                    <ul class="guideline-list">
                                        <li><strong>Vegetation:</strong> 40-50% für Wachstum</li>
                                        <li><strong>Blüte:</strong> 60-70% für Blütenbildung</li>
                                        <li><strong>Wirkung:</strong> Stretch, Blüteninduktion</li>
                                        <li><strong>Farbtemperatur:</strong> 2700-3500K</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="guideline-card">
                                <h5>🟣 Far-Red (700-800nm)</h5>
                                <div class="guideline-content">
                                    <ul class="guideline-list">
                                        <li><strong>Vegetation:</strong> 5-10% für Stretch-Kontrolle</li>
                                        <li><strong>Blüte:</strong> 10-15% für Blütenbildung</li>
                                        <li><strong>Wirkung:</strong> Photomorphogenese, Blüteninduktion</li>
                                        <li><strong>Zusatz-LEDs:</strong> 730nm für Blütenqualität</li>
                                    </ul>
                                </div>
                            </div>
                            
                            <div class="guideline-card">
                                <h5>🟡 UV-Licht (280-400nm)</h5>
                                <div class="guideline-content">
                                    <ul class="guideline-list">
                                        <li><strong>Vegetation:</strong> 2-5% für Stress-Resistenz</li>
                                        <li><strong>Blüte:</strong> 5-10% für Trichom-Entwicklung</li>
                                        <li><strong>Wirkung:</strong> Sekundärmetaboliten, Trichome</li>
                                        <li><strong>Vorsicht:</strong> Langsam steigern, Stress vermeiden</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Praktische Tipps -->
                    <div class="guidelines-section">
                        <h4>💡 Praktische Beleuchtungs-Tipps</h4>
                        <div class="tips-grid">
                            <div class="tip-card">
                                <div class="tip-icon">🌱</div>
                                <h5>Vegetations-Phase</h5>
                                <ul class="tip-list">
                                    <li>Höhere Blau-Anteile für kompaktes Wachstum</li>
                                    <li>Lampenabstand regelmäßig anpassen</li>
                                    <li>18h Photoperiode für Photoperiods</li>
                                    <li>Vorsichtig mit UV-Licht beginnen</li>
                                </ul>
                            </div>
                            
                            <div class="tip-card">
                                <div class="tip-icon">🌸</div>
                                <h5>Blüte-Phase</h5>
                                <ul class="tip-list">
                                    <li>Höhere Rot-Anteile für Blütenbildung</li>
                                    <li>PPFD schrittweise erhöhen</li>
                                    <li>12h Photoperiode für Photoperiods</li>
                                    <li>Far-Red für bessere Blütenqualität</li>
                                </ul>
                            </div>
                            
                            <div class="tip-card">
                                <div class="tip-icon">🚰</div>
                                <h5>Flush-Phase</h5>
                                <ul class="tip-list">
                                    <li>PPFD um 20-30% reduzieren</li>
                                    <li>Wärmere Farbtemperatur für Trichome</li>
                                    <li>Beleuchtungsstunden reduzieren</li>
                                    <li>Langsame, schonende Anpassung</li>
                                </ul>
                            </div>
                            
                            <div class="tip-card">
                                <div class="tip-icon">⚡</div>
                                <h5>Allgemeine Tipps</h5>
                                <ul class="tip-list">
                                    <li>Langsame Anpassungen über 3-5 Tage</li>
                                    <li>Pflanzenreaktionen beobachten</li>
                                    <li>Stress-Symptome vermeiden</li>
                                    <li>Regelmäßige Messungen durchführen</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
                <button type="button" class="btn btn-primary" id="printGuidelinesBtn">
                    <i class="fa-solid fa-print"></i> Drucken
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Core Scripts -->
<script src="{{ url_for('static', filename='scripts/core/lighting-manager.js') }}"></script>
<script src="{{ url_for('static', filename='scripts/core/ai-lighting-advisor.js') }}"></script>

<!-- Phase 5: Smart Scheduling & Predictive Analytics Scripts -->
<script src="{{ url_for('static', filename='scripts/core/smart-scheduler.js') }}"></script>
<script src="{{ url_for('static', filename='scripts/core/predictive-analytics.js') }}"></script>

<!-- Phase 6: Advanced ML & IoT Integration -->
<script src="{{ url_for('static', filename='scripts/core/advanced-ml-system.js') }}"></script>
<script src="{{ url_for('static', filename='scripts/core/iot-sensor-integration.js') }}"></script>

<!-- Modularisierung: TrichomeManager Modul -->
<script src="{{ url_for('static', filename='scripts/widgets/flowering/trichome-manager.js') }}"></script>

<!-- Modularisierung: TimelineManager Modul -->
<script src="{{ url_for('static', filename='scripts/widgets/flowering/timeline-manager.js') }}"></script>

<!-- Widget wird in plant_detail.html initialisiert -->