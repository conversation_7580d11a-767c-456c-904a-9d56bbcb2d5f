# 🌱 Grow-Tagebuch

**Index:** 04 - README  
**Datum:** 11.07.2025  
**Zweck:** Hauptdokumentation des Grow-Tagebuch Projekts  
**Status:** Aktive Dokumentation

---

# 🌱 Grow-Diary-Basic

**Einfach. Modular. Erweiterbar.**

<PERSON><PERSON> schlanke, benutzerfreundliche Basis-Version des Grow-Tagebuchs für Anfänger und erfahrene Gärtner.

## 📋 Übersicht

Grow-Diary-Basic ist eine minimale, aber vollständige Version des Grow-Tagebuchs, die sich auf die Kernfunktionen konzentriert:

- ✅ **Pflanzenverwaltung**: Pflanzen erstellen, bearbeiten und verwalten
- ✅ **Einträge**: Notizen, Beobachtungen und Pflegeeinträge hinzufügen
- ✅ **Modulare Architektur**: Einfache Erweiterbarkeit durch Feature-Flags
- ✅ **Einfache Installation**: Keine komplexen Abhängigkeiten
- ✅ **SQLite-Datenbank**: Portabel und wartungsfrei

## 🚀 Schnellstart

### Voraussetzungen

- Python 3.8 oder höher
- pip (Python Package Manager)

### Installation

1. **Repository klonen oder herunterladen**
   ```bash
   cd grow-diary-basic
   ```

2. **Virtuelle Umgebung erstellen (empfohlen)**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

3. **Abhängigkeiten installieren**
   ```bash
   pip install -r requirements.txt
   ```
   
   **Wichtig**: Falls die Installation fehlschlägt, installiere Flask manuell:
   ```bash
   pip install flask==2.3.3
   ```

4. **Anwendung starten**
   ```bash
   python app_basic.py
   ```

5. **Im Browser öffnen**
   ```
   http://localhost:5002
   ```

## 📁 Projektstruktur

```
grow-diary-basic/
├── app_basic.py              # Haupt-Flask-Anwendung
├── static/                   # Statische Dateien
│   ├── styles/
│   │   └── base.css         # Basis-Styles
│   ├── scripts/
│   │   └── app.js           # Haupt-JavaScript
│   └── favicon.svg          # Favicon
├── templates/               # HTML-Templates
│   ├── index.html          # Hauptseite
│   ├── config.html         # Konfigurationsseite
│   ├── plant_detail.html   # Pflanzen-Detailseite
│   ├── edit_plant.html     # Pflanzen-Bearbeitung
│   ├── edit_entry.html     # Eintrags-Bearbeitung
│   ├── 404.html           # 404-Fehlerseite
│   └── 500.html           # 500-Fehlerseite
├── uploads/                # Upload-Ordner (wird automatisch erstellt)
├── grow_diary_basic.db    # SQLite-Datenbank (wird automatisch erstellt)
└── README.md              # Diese Datei
```

## 🎯 Kernfunktionen

### Pflanzenverwaltung

- **Neue Pflanze erstellen**: Name, Sorte, Startdatum und Notizen
- **Pflanzen bearbeiten**: Alle Informationen nachträglich ändern
- **Pflanzen löschen**: Soft-Delete (Pflanze wird als "gelöscht" markiert)
- **Pflanzenübersicht**: Alle aktiven Pflanzen mit Statistiken

### Einträge

- **Verschiedene Eintragstypen**:
  - 📝 Notiz: Allgemeine Notizen
  - 👁️ Beobachtung: Beobachtungen zum Wachstum
  - 💧 Pflege: Bewässerung, Düngung, etc.
  - ⚠️ Problem: Probleme und deren Lösungen
  - 🎯 Meilenstein: Wichtige Entwicklungsschritte

- **Einträge bearbeiten**: Titel, Typ, Datum und Inhalt ändern
- **Einträge löschen**: Unwichtige Einträge entfernen

### Konfiguration

- **Feature-Flags**: Module ein- und ausschalten
- **Lokale Speicherung**: Konfiguration wird im Browser gespeichert
- **Modulare Erweiterung**: Neue Features können einfach hinzugefügt werden

## 🔧 Konfiguration

### Feature-Flags

Die Basis-Version unterstützt modulare Features, die über die Konfigurationsseite aktiviert werden können:

- **Messwerte**: Temperatur, Luftfeuchtigkeit, pH-Wert
- **Bewässerung**: Automatische Bewässerungsprotokollierung
- **Dünger**: Düngerplanung und -verfolgung
- **Sensoren**: Integration von Bluetooth-Sensoren
- **Analysen**: Datenanalyse und Visualisierung
- **ChatGPT**: KI-gestützte Empfehlungen

### Datenbank

Die Anwendung verwendet SQLite als Datenbank:

- **Automatische Initialisierung**: Tabellen werden beim ersten Start erstellt
- **Portabel**: Datenbankdatei kann einfach gesichert/verschoben werden
- **Wartungsfrei**: Keine separate Datenbankserver-Installation nötig

## 🎨 Design

### Design-Prinzipien

- **Minimalistisch**: Fokus auf Funktionalität
- **Responsive**: Funktioniert auf Desktop und Mobile
- **Barrierefrei**: Gute Lesbarkeit und Navigation
- **Konsistent**: Einheitliches Design-System

### Farbpalette

- **Primär**: Grün (#2E7D32) - Wachstum und Natur
- **Sekundär**: Orange (#FF9800) - Wärme und Energie
- **Akzent**: Blau (#2196F3) - Vertrauen und Stabilität
- **Neutral**: Grautöne für Text und Hintergründe

## 🔌 API-Endpunkte

### Pflanzen

- `GET /api/plants` - Alle Pflanzen abrufen
- `POST /api/plants` - Neue Pflanze erstellen
- `GET /plant/<id>` - Pflanzendetails anzeigen
- `GET /plant/<id>/edit` - Pflanze bearbeiten
- `POST /plant/<id>/delete` - Pflanze löschen

### Einträge

- `GET /api/entries` - Alle Einträge abrufen
- `POST /api/entries` - Neuen Eintrag erstellen
- `GET /entry/<id>/edit` - Eintrag bearbeiten
- `POST /entry/<id>/delete` - Eintrag löschen

### Konfiguration

- `GET /config` - Konfigurationsseite anzeigen
- `POST /api/config` - Konfiguration speichern

### Statistiken

- `GET /api/stats` - Anwendungsstatistiken abrufen

## 🛠️ Entwicklung

### Lokale Entwicklung

1. **Repository klonen**
   ```bash
   git clone <repository-url>
   cd grow-diary-basic
   ```

2. **Virtuelle Umgebung aktivieren**
   ```bash
   source venv/bin/activate  # macOS/Linux
   # oder
   venv\Scripts\activate     # Windows
   ```

3. **Development Server starten**
   ```bash
   python app_basic.py
   ```

4. **Debug-Modus**: Die Anwendung läuft automatisch im Debug-Modus

### Neue Features hinzufügen

1. **Feature-Flag definieren**
   ```python
   # In app_basic.py
   features = {
       'your_feature': False
   }
   ```

2. **Template-Logik hinzufügen**
   ```html
   <!-- In Template -->
   <div data-feature="your_feature" class="d-none">
       <!-- Feature-Inhalt -->
   </div>
   ```

3. **JavaScript-Funktionalität**
   ```javascript
   // In app.js
   if (this.config.features.your_feature) {
       // Feature-Code
   }
   ```

## 📦 Deployment

### Produktionsumgebung

Für die Produktionsumgebung wird empfohlen:

1. **WSGI-Server verwenden**
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 app_basic:app
   ```

2. **Umgebungsvariablen setzen**
   ```bash
   export FLASK_ENV=production
   export SECRET_KEY=your-secret-key
   ```

3. **Reverse Proxy (nginx)**
   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       location / {
           proxy_pass http://127.0.0.1:5000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### Docker (optional)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app_basic:app"]
```

## 🔒 Sicherheit

### Empfohlene Maßnahmen

- **Secret Key ändern**: Standard-Secret-Key in `app_basic.py` ändern
- **HTTPS verwenden**: Für Produktionsumgebungen SSL/TLS aktivieren
- **Firewall konfigurieren**: Nur notwendige Ports öffnen
- **Regelmäßige Backups**: Datenbank regelmäßig sichern

### Backup

```bash
# Datenbank sichern
cp grow_diary_basic.db backup_$(date +%Y%m%d_%H%M%S).db

# Vollständiges Backup
tar -czf grow-diary-basic-backup-$(date +%Y%m%d_%H%M%S).tar.gz .
```

## 🤝 Beitragen

### Entwicklung

1. Fork des Repositories erstellen
2. Feature-Branch erstellen (`git checkout -b feature/amazing-feature`)
3. Änderungen committen (`git commit -m 'Add amazing feature'`)
4. Branch pushen (`git push origin feature/amazing-feature`)
5. Pull Request erstellen

### Richtlinien

- **Code-Style**: PEP 8 für Python, Standard-JavaScript
- **Dokumentation**: Neue Features dokumentieren
- **Tests**: Tests für neue Funktionen schreiben
- **Kompatibilität**: Rückwärtskompatibilität wahren

## 📄 Lizenz

Dieses Projekt steht unter der MIT-Lizenz. Siehe [LICENSE](LICENSE) für Details.

## 🙏 Danksagungen

- **Flask**: Web-Framework
- **SQLite**: Datenbank
- **Community**: Feedback und Verbesserungsvorschläge

## 📞 Support

Bei Fragen oder Problemen:

1. **Issues**: GitHub Issues verwenden
2. **Dokumentation**: Diese README und Code-Kommentare
3. **Community**: Diskussionsforum (falls verfügbar)

---

**🌱 Grow-Diary-Basic** - Dein einfacher Weg zum erfolgreichen Pflanzenwachstum! 