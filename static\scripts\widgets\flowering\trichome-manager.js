/**
 * TrichomeManager - Verwaltet alle trichom-bezogenen Funktionen des Flowering Widgets
 * 
 * Verantwortlichkeiten:
 * - <PERSON><PERSON> und Verwal<PERSON> von Trichom-Daten (Status, Trigger, Empfehlungen, Fortschritt)
 * - Aktualisierung der Trichom-UI-Komponenten
 * - Verwaltun<PERSON> von Trichom-Beobachtungen (CRUD-Operationen)
 * - Event-Handling für trichom-bezogene Interaktionen
 * 
 * <AUTHOR> Agent
 * @date 2025-01-14
 */
class FloweringTrichomeManager {
    constructor(widget) {
        this.widget = widget;
        
        // Datenstrukturen
        this.trichomeData = null;
        this.trichomeTriggerData = null;
        this.trichomeRecommendationData = null;
        this.trichomeProgressData = null;
        
        // UI-Zustand
        this.currentEditIndex = null;
        this.currentDeleteIndex = null;
        
        // Event-Listener Setup
        this.setupEventListeners();
    }

    // ===== ÖFFENTLICHE API =====

    /**
     * <PERSON>ädt alle Trichom-Daten parallel
     */
    async loadTrichomeData() {
        try {
            await Promise.all([
                this.loadTrichomeStatus(),
                this.loadTrichomeTrigger(),
                this.loadTrichomeRecommendation(),
                this.loadTrichomeProgress(),
                this.loadTrichomeGuidelines()
            ]);
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden der Trichom-Daten:', error);
        }
    }

    /**
     * Lädt Trichom-Status von der API
     */
    async loadTrichomeStatus() {
        try {
            const response = await fetch(`/flowering/trichome-status/${this.widget.currentPlantId}`);
            if (response.ok) {
                const data = await response.json();
                this.trichomeData = data;
                
                if (data && data.has_data) {
                    this.updateTrichomeStatus(data);
                    this.updateTrichomeBadge(data);
                    this.updateTrichomeSegments(data);
                    this.updateFlushAlert(data);
                    this.updateFlushProgress(data.flush_progress || 0);
                    
                    if (data.observations) {
                        this.updateObservationList(data.observations);
                    } else {
                        this.updateObservationList([]);
                    }
                } else {
                    this.updateTrichomeNoData();
                }
            } else {
                console.error('🌺 TrichomeManager: Fehler beim Laden des Trichom-Status');
                this.updateTrichomeNoData();
            }
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden des Trichom-Status:', error);
            this.updateTrichomeNoData();
        }
    }

    /**
     * Lädt Trichom-Trigger-Daten
     */
    async loadTrichomeTrigger() {
        try {
            const response = await fetch(`/flowering/trichome-trigger/${this.widget.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeTriggerData = data;
            this.updateTrichomeTrigger(data);
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden des Trichom-Triggers:', error);
        }
    }

    /**
     * Lädt Trichom-Empfehlungen
     */
    async loadTrichomeRecommendation() {
        try {
            const response = await fetch(`/flowering/trichome-recommendation/${this.widget.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeRecommendationData = data;
            this.updateTrichomeRecommendation(data);
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden der Trichom-Empfehlung:', error);
        }
    }

    /**
     * Lädt Trichom-Fortschritt
     */
    async loadTrichomeProgress() {
        try {
            const response = await fetch(`/flowering/trichome-progress/${this.widget.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeProgressData = data;
            this.updateTrichomeProgress(data);
        } catch (error) {
            console.error('🌺 TrichomeManager: Fehler beim Laden des Trichom-Fortschritts:', error);
        }
    }

    /**
     * Lädt Trichom-Richtlinien
     */
    async loadTrichomeGuidelines() {
        // Diese Methode wird vom Haupt-Widget implementiert
        // da sie Teil der Guidelines-Funktionalität ist
        return this.widget.loadTrichomeGuidelines();
    }

    /**
     * Speichert eine neue Trichom-Beobachtung
     */
    async submitObservation() {
        const formData = new FormData(document.getElementById('trichomeObservationForm'));
        
        const observationData = {
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            clear_percentage: parseInt(formData.get('clear_percentage')),
            milky_percentage: parseInt(formData.get('milky_percentage')),
            amber_percentage: parseInt(formData.get('amber_percentage')),
            notes: formData.get('notes') || ''
        };

        try {
            const response = await fetch(`/flowering/trichome-observation/${this.widget.currentPlantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(observationData)
            });
            
            if (response.ok) {
                this.widget.hideObservationForm();
                
                // Alle Trichom-Daten neu laden
                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);
                
                this.widget.showSuccess('Beobachtung erfolgreich gespeichert!');
            } else {
                this.widget.showError('Fehler beim Speichern der Beobachtung');
            }
        } catch (error) {
            console.error('🌺 TrichomeManager: Netzwerkfehler beim Speichern:', error);
            this.widget.showError('Netzwerkfehler beim Speichern der Beobachtung');
        }
    }

    /**
     * Alternative Speicher-Methode für Modal
     */
    async saveTrichomeObservation() {
        return this.submitObservation();
    }

    /**
     * Bearbeitet eine Beobachtung
     */
    editObservation(index) {
        this.showEditForm(index);
    }

    /**
     * Löscht eine Beobachtung
     */
    deleteObservation(index) {
        this.showDeleteForm(index);
    }

    // ===== UI UPDATE METHODEN =====

    /**
     * Aktualisiert den Trichom-Status in der UI
     */
    updateTrichomeStatus(data) {
        if (!data || !data.has_data) {
            this.updateTrichomeNoData();
            return;
        }

        this.updateTrichomeBadge(data);

        const lastUpdate = this.widget.getElementById('lastTrichomeUpdate');
        if (lastUpdate && data.latest_entry) {
            const date = new Date(data.latest_entry.date).toLocaleDateString('de-DE');
            lastUpdate.textContent = `Letzte Analyse: ${date}`;
        }

        this.updateTrichomeSegments(data);
        this.updateFlushAlert(data);
        
        // Urgency-Anzeige
        const urgencyElement = this.widget.getElementById('trichomeUrgency');
        if (urgencyElement && data.urgency) {
            urgencyElement.textContent = data.urgency;
            urgencyElement.className = `urgency-indicator ${data.urgency}`;
        }
        
        if (data.observations) {
            this.updateObservationList(data.observations);
        }
    }

    /**
     * Aktualisiert das Status-Badge
     */
    updateTrichomeBadge(data) {
        const badge = this.widget.getElementById('trichomeStatusBadge');
        const badgeIcon = this.widget.getElementById('badgeIcon');
        const badgeText = this.widget.getElementById('badgeText');

        if (badge && badgeIcon && badgeText) {
            // Badge-Klassen zurücksetzen
            badge.className = 'status-badge';
            
            // Status-spezifische Klasse hinzufügen
            if (data.status) {
                badge.classList.add(`status-${data.status}`);
            }
            
            // Icon setzen
            badgeIcon.textContent = data.icon || '🔍';
            
            // Text setzen
            badgeText.textContent = data.recommendation;
        }
    }

    /**
     * Aktualisiert die Trichom-Segmente (Balken)
     */
    updateTrichomeSegments(data) {
        const currentValues = data.current_values || {};

        // Klare Trichome
        const clearSegment = this.widget.getElementById('clearSegmentBar');
        const clearPercentage = this.widget.getElementById('clearPercentage');
        if (clearSegment && clearPercentage) {
            const clearValue = currentValues.clear_percentage || 0;
            clearSegment.style.width = `${clearValue}%`;
            clearPercentage.textContent = `${clearValue}%`;
        }

        // Milchige Trichome
        const milkySegment = this.widget.getElementById('milkySegmentBar');
        const milkyPercentage = this.widget.getElementById('milkyPercentage');
        if (milkySegment && milkyPercentage) {
            const milkyValue = currentValues.milky_percentage || 0;
            milkySegment.style.width = `${milkyValue}%`;
            milkyPercentage.textContent = `${milkyValue}%`;
        }

        // Bernstein Trichome
        const amberSegment = this.widget.getElementById('amberSegmentBar');
        const amberPercentage = this.widget.getElementById('amberPercentage');
        if (amberSegment && amberPercentage) {
            const amberValue = currentValues.amber_percentage || 0;
            amberSegment.style.width = `${amberValue}%`;
            amberPercentage.textContent = `${amberValue}%`;
        }

        // Status-Text im Header aktualisieren
        const statusText = this.widget.getElementById('trichomeStatusText');
        if (statusText && data.current_values) {
            const klar = data.current_values.clear_percentage || 0;
            const milchig = data.current_values.milky_percentage || 0;
            const bernstein = data.current_values.amber_percentage || 0;
            statusText.textContent = `Klar: ${klar}%, Milchig: ${milchig}%, Bernstein: ${bernstein}%`;
        } else if (statusText) {
            statusText.textContent = "Keine Trichom-Daten vorhanden";
        }
    }

    /**
     * Aktualisiert die Trichom-Empfehlungen
     */
    updateTrichomeRecommendation(data) {
        const recommendationMessage = this.widget.getElementById('recommendationMessage');
        const actionText = this.widget.getElementById('actionText');
        const urgencyText = this.widget.getElementById('urgencyText');
        const nextCheckText = this.widget.getElementById('nextCheckText');

        if (data && data.recommendation) {
            if (recommendationMessage) {
                recommendationMessage.textContent = data.recommendation;
                recommendationMessage.className = `recommendation-message ${data.status || 'info'}`;
            }

            if (actionText) {
                actionText.textContent = data.action || 'Weiter beobachten';
            }

            if (nextCheckText) {
                nextCheckText.textContent = data.next_check || 'In 2-3 Tagen';
            }

            if (urgencyText) {
                urgencyText.textContent = data.urgency || 'Niedrig';
            }
        }
    }

    /**
     * Aktualisiert die Trichom-Trigger
     */
    updateTrichomeTrigger(data) {
        const triggerMessage = this.widget.getElementById('triggerMessage');
        const triggerText = this.widget.getElementById('triggerText');
        const triggerType = this.widget.getElementById('triggerType');
        const triggerAction = this.widget.getElementById('triggerAction');
        const manualFlushBtn = this.widget.getElementById('manualFlushBtn');

        if (data && data.trigger) {
            const trigger = data.trigger;

            if (triggerMessage) {
                triggerMessage.textContent = trigger.message || 'Kein Trigger aktiv';
                triggerMessage.className = `trigger-message ${trigger.triggered ? 'active' : 'inactive'}`;
            }

            if (triggerText) {
                triggerText.textContent = trigger.description || '';
            }

            if (triggerType) {
                triggerType.textContent = trigger.type || 'Automatisch';
            }

            if (triggerAction) {
                triggerAction.textContent = trigger.action || 'Weiter beobachten';
            }

            if (manualFlushBtn) {
                manualFlushBtn.textContent = trigger.triggered ? 'Flush starten' : 'Noch nicht bereit';
                manualFlushBtn.className = trigger.triggered ? 'btn btn-warning' : 'btn btn-secondary disabled';
            }
        }
    }

    /**
     * Zeigt "Keine Daten" Zustand
     */
    updateTrichomeNoData() {
        const noDataEl = this.widget.getElementById('trichomeNoData');
        if (!noDataEl) return;

        noDataEl.classList.add('active');
    }

    /**
     * Aktualisiert den Trichom-Fortschritt
     */
    updateTrichomeProgress(data) {
        // Beobachtungsanzahl
        const entriesCount = this.widget.getElementById('entriesCount');
        if (entriesCount && data.observations_count !== undefined) {
            entriesCount.textContent = `${data.observations_count} Beobachtungen`;
        }

        // Fortschrittsbalken
        const progressBar = this.widget.getElementById('trichomeProgressBar');
        if (progressBar && data.progress_percentage !== undefined) {
            progressBar.style.width = `${data.progress_percentage}%`;
            progressBar.setAttribute('aria-valuenow', data.progress_percentage);
        }

        // Fortschrittstext
        const progressText = this.widget.getElementById('trichomeProgressText');
        if (progressText && data.progress_text) {
            progressText.textContent = data.progress_text;
        }

        // Nächster Meilenstein
        const nextMilestone = this.widget.getElementById('nextMilestone');
        if (nextMilestone && data.next_milestone) {
            nextMilestone.textContent = data.next_milestone;
        }
    }

    /**
     * Aktualisiert Flush-Alert
     */
    updateFlushAlert(data) {
        const flushAlert = this.widget.getElementById('flushAlert');
        if (!flushAlert) return;

        if (data.flush_recommended) {
            flushAlert.style.display = 'block';
            flushAlert.className = 'alert alert-warning';
            flushAlert.textContent = data.flush_message || 'Flush wird empfohlen';
        } else {
            flushAlert.style.display = 'none';
        }
    }

    /**
     * Aktualisiert Flush-Fortschritt
     */
    updateFlushProgress(progress) {
        const flushProgressBar = this.widget.getElementById('flushProgressBar');
        const flushProgressText = this.widget.getElementById('flushProgressText');
        const flushProgressCircle = this.widget.getElementById('flushProgressCircle');

        if (flushProgressBar) {
            flushProgressBar.style.width = `${progress}%`;
            flushProgressBar.setAttribute('aria-valuenow', progress);
        }

        if (flushProgressText) {
            flushProgressText.textContent = `${progress}%`;
        }

        if (flushProgressCircle) {
            const circumference = 2 * Math.PI * 45; // r=45
            const offset = circumference - (progress / 100) * circumference;
            flushProgressCircle.setAttribute('stroke-dasharray', circumference);
            flushProgressCircle.setAttribute('stroke-dashoffset', offset);
        }
    }

    // ===== PRIVATE HILFSMETHODEN =====

    /**
     * Initialisiert Event-Listener für trichom-bezogene Elemente
     */
    setupEventListeners() {
        // Trichom-Tab Event-Listener
        const trichomeTab = this.widget.getElementById('trichomeTab');
        if (trichomeTab) {
            trichomeTab.addEventListener('click', async () => {
                await this.widget.switchTab('trichome');
            });
        }

        // Modal Save Button
        const trichomeModalSave = document.getElementById('trichomeObservationModalSave');
        if (trichomeModalSave) {
            trichomeModalSave.addEventListener('click', (e) => {
                e.preventDefault();
                this.saveTrichomeObservation();
            });
        }

        // Observation Form Setup
        this.setupObservationForm();
    }

    /**
     * Initialisiert das Beobachtungsformular
     */
    setupObservationForm() {
        const addBtn = document.getElementById('addObservationBtn');
        const formContainer = document.getElementById('observationFormContainer');
        const cancelBtn = document.getElementById('cancelObservationBtn');
        const form = document.getElementById('trichomeObservationForm');
        
        if (addBtn && formContainer) {
            addBtn.addEventListener('click', () => {
                formContainer.style.display = formContainer.style.display === 'none' ? 'block' : 'none';
            });
        }
        
        if (cancelBtn && formContainer) {
            cancelBtn.addEventListener('click', () => {
                formContainer.style.display = 'none';
            });
        }
        
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitObservation();
            });
        }
        
        // Schieberegler Event-Listener für Live-Updates
        this.setupSliderListeners();
    }

    /**
     * Aktualisiert die Beobachtungsliste
     */
    updateObservationList(observations) {
        const listEl = this.widget.getElementById('observationList');
        if (!listEl) return;

        listEl.innerHTML = '';
        if (!observations || observations.length === 0) {
            listEl.innerHTML = '<div class="observation-placeholder"><span class="placeholder-text">Keine Beobachtungen verfügbar</span></div>';
            return;
        }

        observations.forEach((obs, index) => {
            const date = new Date(obs.date);
            const formattedDate = date.toLocaleDateString('de-DE');

            const item = document.createElement('div');
            item.className = 'observation-item';
            item.setAttribute('data-observation-index', index);

            item.innerHTML = `
                <div class="observation-header">
                    <span class="observation-date">${formattedDate}</span>
                    <span class="observation-day">Tag ${obs.bloom_day}</span>
                    <div class="observation-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="floweringWidget.trichomeManager.editObservation(${index})">
                            ✏️
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="floweringWidget.trichomeManager.deleteObservation(${index})">
                            🗑️
                        </button>
                    </div>
                </div>
                <div class="observation-content">
                    <div class="trichome-percentages">
                        <span class="clear">Klar: ${obs.clear_percentage}%</span>
                        <span class="milky">Milchig: ${obs.milky_percentage}%</span>
                        <span class="amber">Bernstein: ${obs.amber_percentage}%</span>
                    </div>
                    ${obs.notes ? `<div class="observation-notes">${obs.notes}</div>` : ''}
                </div>
            `;

            listEl.appendChild(item);
        });
    }

    /**
     * Zeigt das Bearbeitungsformular für eine Beobachtung
     */
    showEditForm(index) {
        this.widget.hideAllForms();

        this.currentEditIndex = index;
        this.currentDeleteIndex = null;
        this.clearObservationIndicators();

        const container = document.getElementById('observationActionFormContainer');
        if (!container) return;

        const item = document.querySelector(`.observation-item[data-observation-index="${index}"]`);
        if (item) {
            item.classList.add('is-editing');
        }

        container.innerHTML = '';
        const currentObservations = this.getCurrentObservations();
        if (!currentObservations || !currentObservations[index]) return;

        const obs = currentObservations[index];

        container.innerHTML = `
            <div class="edit-form">
                <div class="form-card compact">
                    <h5>✏️ Beobachtung bearbeiten</h5>
                    <form id="editObservationForm">
                        <div class="form-row compact">
                            <div class="form-group">
                                <label>Datum:</label>
                                <input type="date" name="date" value="${obs.date}" required>
                            </div>
                            <div class="form-group">
                                <label>Blüte-Tag:</label>
                                <input type="number" name="bloom_day" min="1" max="100" value="${obs.bloom_day}" required>
                            </div>
                        </div>
                        <div class="form-row compact">
                            <div class="form-group">
                                <label>Klar (%):</label>
                                <input type="number" name="clear_percentage" min="0" max="100" value="${obs.clear_percentage}" required>
                            </div>
                            <div class="form-group">
                                <label>Milchig (%):</label>
                                <input type="number" name="milky_percentage" min="0" max="100" value="${obs.milky_percentage}" required>
                            </div>
                            <div class="form-group">
                                <label>Bernstein (%):</label>
                                <input type="number" name="amber_percentage" min="0" max="100" value="${obs.amber_percentage}" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>Notizen:</label>
                            <textarea name="notes" rows="2">${obs.notes || ''}</textarea>
                        </div>
                        <div class="form-actions compact">
                            <button type="submit" class="btn btn-primary btn-sm">Speichern</button>
                            <button type="button" class="btn btn-secondary btn-sm btn-cancel">Abbrechen</button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        container.style.display = 'block';

        // Event-Listener für das Bearbeitungsformular
        const editForm = document.getElementById('editObservationForm');
        if (editForm) {
            editForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performEdit(index);
            });
        }

        const cancelBtn = container.querySelector('.btn-cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.widget.hideAllForms();
            });
        }
    }

    /**
     * Zeigt das Löschformular für eine Beobachtung
     */
    showDeleteForm(index) {
        this.widget.hideAllForms();

        this.currentDeleteIndex = index;
        this.currentEditIndex = null;
        this.clearObservationIndicators();

        const container = document.getElementById('observationActionFormContainer');
        if (!container) return;

        const item = document.querySelector(`.observation-item[data-observation-index="${index}"]`);
        if (item) {
            item.classList.add('is-deleting');
        }

        container.innerHTML = '';
        const currentObservations = this.getCurrentObservations();
        if (!currentObservations || !currentObservations[index]) return;

        const obs = currentObservations[index];
        const date = new Date(obs.date);
        const formattedDate = date.toLocaleDateString('de-DE');

        container.innerHTML = `
            <div class="delete-form">
                <div class="form-card compact delete-confirmation">
                    <h5>🗑️ Beobachtung löschen</h5>
                    <div class="confirm-text">
                        Möchtest du die Beobachtung vom ${formattedDate} (Blüte-Tag ${obs.bloom_day}) wirklich löschen?
                    </div>
                    <div class="form-actions compact">
                        <button type="button" class="btn btn-danger btn-sm btn-delete-confirm">Löschen</button>
                        <button type="button" class="btn btn-secondary btn-sm btn-cancel">Abbrechen</button>
                    </div>
                </div>
            </div>
        `;

        container.style.display = 'block';

        // Event-Listener für Löschbestätigung
        const deleteBtn = container.querySelector('.btn-delete-confirm');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                this.performDelete(index);
            });
        }

        const cancelBtn = container.querySelector('.btn-cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.widget.hideAllForms();
            });
        }
    }

    /**
     * Führt die Bearbeitung einer Beobachtung durch
     */
    async performEdit(index) {
        const observations = this.getCurrentObservations();
        if (!observations || !observations[index]) {
            this.widget.showError('Beobachtung nicht gefunden');
            return;
        }

        const observation = observations[index];
        const observationId = observation.id;

        const formData = new FormData(document.getElementById('editObservationForm'));
        const updatedData = {
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            clear_percentage: parseInt(formData.get('clear_percentage')),
            milky_percentage: parseInt(formData.get('milky_percentage')),
            amber_percentage: parseInt(formData.get('amber_percentage')),
            notes: formData.get('notes') || ''
        };

        try {
            const response = await fetch(`/flowering/trichome-observation/${this.widget.currentPlantId}?id=${observationId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedData)
            });

            if (response.ok) {
                this.widget.showSuccess('Beobachtung erfolgreich aktualisiert');
                this.widget.hideAllForms();

                // Alle Trichom-Daten vollständig neu laden
                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);
            } else {
                this.widget.showError('Fehler beim Bearbeiten der Beobachtung');
            }
        } catch (error) {
            console.error('🌺 TrichomeManager: Netzwerkfehler beim Bearbeiten:', error);
            this.widget.showError('Netzwerkfehler beim Bearbeiten der Beobachtung');
        }
    }

    /**
     * Führt die Löschung einer Beobachtung durch
     */
    async performDelete(index) {
        const observations = this.getCurrentObservations();
        if (!observations || !observations[index]) {
            this.widget.showError('Beobachtung nicht gefunden');
            return;
        }

        const observation = observations[index];
        const observationId = observation.id;

        try {
            const response = await fetch(`/flowering/trichome-observation/${this.widget.currentPlantId}?id=${observationId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.widget.showSuccess('Beobachtung erfolgreich gelöscht');
                this.widget.hideAllForms();

                // Alle Trichom-Daten vollständig neu laden
                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);
            } else {
                this.widget.showError('Fehler beim Löschen der Beobachtung');
            }
        } catch (error) {
            console.error('🌺 TrichomeManager: Netzwerkfehler beim Löschen:', error);
            this.widget.showError('Netzwerkfehler beim Löschen der Beobachtung');
        }
    }

    /**
     * Hilfsmethoden
     */
    getCurrentObservations() {
        return this.trichomeData?.observations || [];
    }

    clearObservationIndicators() {
        document.querySelectorAll('.observation-item').forEach(item => {
            item.classList.remove('is-editing', 'is-deleting');
        });
    }

    setupSliderListeners() {
        // Implementierung für Schieberegler-Updates
        const sliders = ['clear_percentage', 'milky_percentage', 'amber_percentage'];
        sliders.forEach(sliderId => {
            const slider = document.getElementById(sliderId);
            const display = document.getElementById(`${sliderId}_display`);

            if (slider && display) {
                slider.addEventListener('input', (e) => {
                    display.textContent = `${e.target.value}%`;
                });
            }
        });
    }

    /**
     * Hilfsmethode für DOM-Zugriff über das Haupt-Widget
     */
    getElementById(id) {
        return this.widget.getElementById(id);
    }
}

// Export für Module-System
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FloweringTrichomeManager;
}
