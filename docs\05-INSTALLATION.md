# 📦 Installation - Grow-Tagebuch

**Index:** 05 - Installation  
**Datum:** 11.07.2025  
**Zweck:** Installationsanleitung für das Grow-Tagebuch  
**Status:** Installationsdokumentation

---

# 🌱 Grow-Diary-Basic Installation

**Einfache Installation in 5 Schritten**

## 📋 Voraussetzungen
- Python 3.8 oder höher installiert
- PowerShell oder Kommandozeile

## 🚀 Installation

### Schritt 1: Zum Projektordner wechseln
```powershell
cd E:\001-Grow-Tagebuch
```

### Schritt 2: Virtuelle Umgebung erstellen
```powershell
python -m venv venv
```

### Schritt 3: Virtuelle Umgebung aktivieren
```powershell
venv\Scripts\activate
```

### Schritt 4: Flask installieren
```powershell
pip install flask==2.3.3
```

### Schritt 5: App starten
```powershell
python app_basic.py
```

## 🌐 App öffnen
Öffne deinen Browser und gehe zu:
```
http://localhost:5002
```

## 🔄 Für zu<PERSON> Starts
```powershell
cd E:\001-Grow-Tagebuch
venv\Scripts\activate
python app_basic.py
```

## ❓ Hilfe
- Falls Flask nicht gefunden wird: `pip install flask==2.3.3`
- Falls virtuelle Umgebung nicht funktioniert: `python -m venv venv --clear`
- App stoppen: `Ctrl+C` im Terminal

## 📁 Projektstruktur
```
E:\001-Grow-Tagebuch\
├── app_basic.py              # Haupt-App
├── requirements.txt          # Abhängigkeiten
├── static\                   # CSS, JS, Bilder
├── templates\               # HTML-Templates
├── venv\                    # Virtuelle Umgebung
└── grow_diary_basic.db     # Datenbank (wird erstellt)
```

**Viel Spaß mit deinem Grow-Tagebuch! 🌱** 