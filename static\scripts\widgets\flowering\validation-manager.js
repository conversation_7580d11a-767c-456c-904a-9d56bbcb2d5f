/**
 * FloweringValidationManager - Verwaltet alle Validierungsoperationen für das Flowering Widget
 * 
 * Zuständigkeiten:
 * - Daten<PERSON>idierung (Trichome, Marker, Formulare)
 * - Geschäftslogik-Validierung
 * - Eingabeprüfungen (Datum, Zahlen, Prozentsätze)
 * - Grenzwert- und Limit-Prüfungen
 * - Datenintegrität und -konsistenz
 * - Fehlermeldungen und Validierungsregeln
 */
class FloweringValidationManager {
    constructor(widget) {
        this.widget = widget;
        this.validationRules = this.initializeValidationRules();
        this.errorMessages = this.initializeErrorMessages();
        
        console.log('✅ FloweringValidationManager: Initialisiert');
    }

    /**
     * Initialisiert Validierungsregeln
     */
    initializeValidationRules() {
        return {
            trichome: {
                minPercentage: 0,
                maxPercentage: 100,
                minTotal: 90,
                maxTotal: 110,
                strictTotal: 100
            },
            bloomDay: {
                min: 1,
                max: 200
            },
            date: {
                maxFutureDays: 0, // Keine <PERSON>ukunftsdaten
                maxPastDays: 365 // Max 1 Jahr zurück
            },
            marker: {
                requiredFields: ['event_type', 'event_name', 'bloom_day'],
                maxNameLength: 100,
                maxDescriptionLength: 500
            },
            plant: {
                requiredFields: ['phase', 'current_day'],
                validPhases: ['flowering_early', 'flowering_middle', 'flowering_late', 'flush', 'harvest'],
                validStrainTypes: ['photoperiodic', 'autoflower']
            }
        };
    }

    /**
     * Initialisiert Fehlermeldungen
     */
    initializeErrorMessages() {
        return {
            trichome: {
                invalidPercentage: 'Prozentsätze müssen zwischen 0 und 100 liegen',
                invalidTotal: 'Die Summe der Trichom-Prozentsätze sollte zwischen 90-110% liegen',
                strictTotal: 'Die Summe der Trichom-Prozentsätze muss genau 100% betragen',
                negativeValues: 'Negative Werte sind nicht erlaubt',
                missingData: 'Mindestens ein Trichom-Typ muss angegeben werden'
            },
            date: {
                futureDate: 'Das Datum darf nicht in der Zukunft liegen',
                tooOld: 'Das Datum liegt zu weit in der Vergangenheit',
                invalidFormat: 'Ungültiges Datumsformat'
            },
            bloomDay: {
                tooLow: 'Der Blütetag muss mindestens 1 sein',
                tooHigh: 'Der Blütetag ist unrealistisch hoch',
                notNumber: 'Der Blütetag muss eine Zahl sein'
            },
            marker: {
                missingFields: 'Erforderliche Felder fehlen',
                nameTooLong: 'Der Name ist zu lang',
                descriptionTooLong: 'Die Beschreibung ist zu lang'
            },
            plant: {
                missingFields: 'Erforderliche Pflanzendaten fehlen',
                invalidPhase: 'Ungültige Blütephase',
                invalidStrainType: 'Ungültiger Strain-Typ'
            }
        };
    }

    /**
     * Validiert Trichom-Daten
     */
    validateTrichomeData(data, strict = false) {
        const result = {
            valid: true,
            errors: [],
            warnings: []
        };

        // Daten normalisieren
        const clear = this.normalizeNumber(data.clear_percentage || data.clear);
        const milky = this.normalizeNumber(data.milky_percentage || data.milky);
        const amber = this.normalizeNumber(data.amber_percentage || data.amber);

        // Null/Undefined prüfen
        if (clear === null || milky === null || amber === null) {
            result.valid = false;
            result.errors.push(this.errorMessages.trichome.missingData);
            return result;
        }

        // Negative Werte prüfen
        if (clear < 0 || milky < 0 || amber < 0) {
            result.valid = false;
            result.errors.push(this.errorMessages.trichome.negativeValues);
            return result;
        }

        // Einzelne Prozentsätze prüfen
        const percentages = [clear, milky, amber];
        const rules = this.validationRules.trichome;
        
        for (const percentage of percentages) {
            if (percentage < rules.minPercentage || percentage > rules.maxPercentage) {
                result.valid = false;
                result.errors.push(this.errorMessages.trichome.invalidPercentage);
                break;
            }
        }

        // Summe prüfen
        const total = clear + milky + amber;
        
        if (strict) {
            // Strenge Validierung: Genau 100%
            if (total !== rules.strictTotal) {
                result.valid = false;
                result.errors.push(this.errorMessages.trichome.strictTotal);
            }
        } else {
            // Flexible Validierung: 90-110%
            if (total < rules.minTotal || total > rules.maxTotal) {
                result.valid = false;
                result.errors.push(
                    `${this.errorMessages.trichome.invalidTotal} (aktuell: ${total}%)`
                );
            } else if (total !== rules.strictTotal) {
                result.warnings.push(
                    `Trichom-Summe ist ${total}% (Schätzungen sind normal)`
                );
            }
        }

        return result;
    }

    /**
     * Validiert Datumseingaben
     */
    validateDate(dateString, allowFuture = false) {
        const result = {
            valid: true,
            errors: []
        };

        if (!dateString) {
            result.valid = false;
            result.errors.push('Datum ist erforderlich');
            return result;
        }

        let date;
        try {
            date = new Date(dateString);
            if (isNaN(date.getTime())) {
                throw new Error('Invalid date');
            }
        } catch (error) {
            result.valid = false;
            result.errors.push(this.errorMessages.date.invalidFormat);
            return result;
        }

        const now = new Date();
        // Setze Zeit auf Mitternacht für korrekte Tagesvergleiche
        now.setHours(0, 0, 0, 0);
        date.setHours(0, 0, 0, 0);

        const diffDays = Math.floor((date - now) / (1000 * 60 * 60 * 24));

        // Zukunftsprüfung
        if (!allowFuture && diffDays > this.validationRules.date.maxFutureDays) {
            result.valid = false;
            result.errors.push(this.errorMessages.date.futureDate);
        }

        // Vergangenheitsprüfung
        if (diffDays < -this.validationRules.date.maxPastDays) {
            result.valid = false;
            result.errors.push(this.errorMessages.date.tooOld);
        }

        return result;
    }

    /**
     * Validiert Blütetag
     */
    validateBloomDay(bloomDay) {
        const result = {
            valid: true,
            errors: []
        };

        const day = this.normalizeNumber(bloomDay);
        
        if (day === null) {
            result.valid = false;
            result.errors.push(this.errorMessages.bloomDay.notNumber);
            return result;
        }

        const rules = this.validationRules.bloomDay;
        
        if (day < rules.min) {
            result.valid = false;
            result.errors.push(this.errorMessages.bloomDay.tooLow);
        }

        if (day > rules.max) {
            result.valid = false;
            result.errors.push(this.errorMessages.bloomDay.tooHigh);
        }

        return result;
    }

    /**
     * Validiert Marker-Daten
     */
    validateMarkerData(markerData) {
        const result = {
            valid: true,
            errors: []
        };

        const rules = this.validationRules.marker;

        // Erforderliche Felder prüfen
        const missingFields = rules.requiredFields.filter(field => !markerData[field]);
        if (missingFields.length > 0) {
            result.valid = false;
            result.errors.push(`${this.errorMessages.marker.missingFields}: ${missingFields.join(', ')}`);
        }

        // Name-Länge prüfen
        if (markerData.event_name && markerData.event_name.length > rules.maxNameLength) {
            result.valid = false;
            result.errors.push(this.errorMessages.marker.nameTooLong);
        }

        // Beschreibung-Länge prüfen
        if (markerData.description && markerData.description.length > rules.maxDescriptionLength) {
            result.valid = false;
            result.errors.push(this.errorMessages.marker.descriptionTooLong);
        }

        // Blütetag validieren
        if (markerData.bloom_day !== undefined) {
            const bloomDayResult = this.validateBloomDay(markerData.bloom_day);
            if (!bloomDayResult.valid) {
                result.valid = false;
                result.errors.push(...bloomDayResult.errors);
            }
        }

        return result;
    }

    /**
     * Validiert Pflanzendaten
     */
    validatePlantData(plantData) {
        const result = {
            valid: true,
            errors: []
        };

        const rules = this.validationRules.plant;

        // Erforderliche Felder prüfen
        const missingFields = rules.requiredFields.filter(field => !plantData[field]);
        if (missingFields.length > 0) {
            result.valid = false;
            result.errors.push(`${this.errorMessages.plant.missingFields}: ${missingFields.join(', ')}`);
        }

        // Phase validieren
        if (plantData.phase && !rules.validPhases.includes(plantData.phase)) {
            result.valid = false;
            result.errors.push(this.errorMessages.plant.invalidPhase);
        }

        // Strain-Typ validieren
        if (plantData.strain_type && !rules.validStrainTypes.includes(plantData.strain_type)) {
            result.valid = false;
            result.errors.push(this.errorMessages.plant.invalidStrainType);
        }

        // Current Day validieren
        if (plantData.current_day !== undefined) {
            const bloomDayResult = this.validateBloomDay(plantData.current_day);
            if (!bloomDayResult.valid) {
                result.valid = false;
                result.errors.push(...bloomDayResult.errors);
            }
        }

        return result;
    }

    /**
     * Validiert Beobachtungsdaten (kombiniert)
     */
    validateObservationData(observationData, strict = false) {
        const result = {
            valid: true,
            errors: [],
            warnings: []
        };

        // Trichom-Daten validieren
        const trichomeResult = this.validateTrichomeData(observationData, strict);
        if (!trichomeResult.valid) {
            result.valid = false;
            result.errors.push(...trichomeResult.errors);
        }
        result.warnings.push(...trichomeResult.warnings);

        // Datum validieren
        if (observationData.date) {
            const dateResult = this.validateDate(observationData.date);
            if (!dateResult.valid) {
                result.valid = false;
                result.errors.push(...dateResult.errors);
            }
        }

        // Blütetag validieren
        if (observationData.bloom_day !== undefined) {
            const bloomDayResult = this.validateBloomDay(observationData.bloom_day);
            if (!bloomDayResult.valid) {
                result.valid = false;
                result.errors.push(...bloomDayResult.errors);
            }
        }

        return result;
    }

    /**
     * Hilfsmethoden
     */
    normalizeNumber(value) {
        if (value === null || value === undefined || value === '') {
            return null;
        }

        const num = Number(value);
        return isNaN(num) ? null : num;
    }

    /**
     * Prüft ob ein Wert in einem bestimmten Bereich liegt
     */
    isInRange(value, min, max) {
        const num = this.normalizeNumber(value);
        return num !== null && num >= min && num <= max;
    }

    /**
     * Validiert E-Mail-Format (falls benötigt)
     */
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * Validiert URL-Format (falls benötigt)
     */
    validateUrl(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Prüft Datenintegrität zwischen verschiedenen Werten
     */
    validateDataIntegrity(data) {
        const result = {
            valid: true,
            errors: [],
            warnings: []
        };

        // Prüfe Konsistenz zwischen Blütetag und Datum
        if (data.bloom_day && data.date && this.widget.floweringData?.flowering_status?.start_date) {
            const startDate = new Date(this.widget.floweringData.flowering_status.start_date);
            const observationDate = new Date(data.date);
            const expectedBloomDay = Math.floor((observationDate - startDate) / (1000 * 60 * 60 * 24)) + 1;

            const dayDifference = Math.abs(data.bloom_day - expectedBloomDay);
            if (dayDifference > 3) { // 3 Tage Toleranz
                result.warnings.push(
                    `Blütetag (${data.bloom_day}) stimmt möglicherweise nicht mit dem Datum überein (erwartet: ~${expectedBloomDay})`
                );
            }
        }

        // Prüfe realistische Trichom-Entwicklung
        if (data.clear_percentage !== undefined && data.amber_percentage !== undefined) {
            if (data.clear_percentage > 80 && data.amber_percentage > 20) {
                result.warnings.push(
                    'Ungewöhnliche Trichom-Verteilung: Hohe klare und bernsteinfarbene Anteile gleichzeitig'
                );
            }
        }

        return result;
    }

    /**
     * Validiert Formular-Daten basierend auf Formular-Typ
     */
    validateFormData(formData, formType) {
        const result = {
            valid: true,
            errors: [],
            warnings: []
        };

        switch (formType) {
            case 'trichome_observation':
                const observationResult = this.validateObservationData(formData);
                result.valid = observationResult.valid;
                result.errors.push(...observationResult.errors);
                result.warnings.push(...observationResult.warnings);

                // Zusätzliche Integritätsprüfung
                const integrityResult = this.validateDataIntegrity(formData);
                result.warnings.push(...integrityResult.warnings);
                break;

            case 'marker':
                const markerResult = this.validateMarkerData(formData);
                result.valid = markerResult.valid;
                result.errors.push(...markerResult.errors);
                break;

            case 'plant_data':
                const plantResult = this.validatePlantData(formData);
                result.valid = plantResult.valid;
                result.errors.push(...plantResult.errors);
                break;

            default:
                result.warnings.push(`Unbekannter Formular-Typ: ${formType}`);
        }

        return result;
    }

    /**
     * Validiert API-Response-Daten
     */
    validateApiResponse(responseData, expectedStructure) {
        const result = {
            valid: true,
            errors: []
        };

        if (!responseData) {
            result.valid = false;
            result.errors.push('Keine Antwortdaten erhalten');
            return result;
        }

        // Prüfe erwartete Struktur
        if (expectedStructure) {
            for (const field of expectedStructure) {
                if (!(field in responseData)) {
                    result.valid = false;
                    result.errors.push(`Erwartetes Feld fehlt: ${field}`);
                }
            }
        }

        return result;
    }

    /**
     * Validiert Batch-Operationen
     */
    validateBatchData(dataArray, validationFunction) {
        const results = {
            valid: true,
            totalItems: dataArray.length,
            validItems: 0,
            invalidItems: 0,
            errors: [],
            itemResults: []
        };

        dataArray.forEach((item, index) => {
            const itemResult = validationFunction.call(this, item);
            results.itemResults.push({
                index,
                valid: itemResult.valid,
                errors: itemResult.errors || [],
                warnings: itemResult.warnings || []
            });

            if (itemResult.valid) {
                results.validItems++;
            } else {
                results.invalidItems++;
                results.valid = false;
                results.errors.push(`Item ${index + 1}: ${itemResult.errors.join(', ')}`);
            }
        });

        return results;
    }

    /**
     * Erstellt benutzerfreundliche Fehlermeldungen
     */
    formatErrorMessage(errors, warnings = []) {
        let message = '';

        if (errors.length > 0) {
            message += '❌ Fehler:\n' + errors.map(error => `• ${error}`).join('\n');
        }

        if (warnings.length > 0) {
            if (message) message += '\n\n';
            message += '⚠️ Warnungen:\n' + warnings.map(warning => `• ${warning}`).join('\n');
        }

        return message;
    }

    /**
     * Zeigt Validierungsergebnisse in der UI an
     */
    displayValidationResult(result, targetElement = null) {
        const message = this.formatErrorMessage(result.errors, result.warnings);

        if (targetElement) {
            targetElement.innerHTML = message.replace(/\n/g, '<br>');
            targetElement.className = result.valid ? 'validation-success' : 'validation-error';
        } else if (this.widget.showError && this.widget.showSuccess) {
            if (result.valid) {
                if (result.warnings.length > 0) {
                    this.widget.showInfo(this.formatErrorMessage([], result.warnings));
                } else {
                    this.widget.showSuccess('Validierung erfolgreich!');
                }
            } else {
                this.widget.showError(message);
            }
        } else {
            // Fallback: Alert
            if (result.valid) {
                if (result.warnings.length > 0) {
                    alert('Warnung:\n' + result.warnings.join('\n'));
                }
            } else {
                alert('Validierungsfehler:\n' + result.errors.join('\n'));
            }
        }
    }

    /**
     * Aktualisiert Validierungsregeln zur Laufzeit
     */
    updateValidationRules(newRules) {
        this.validationRules = { ...this.validationRules, ...newRules };
        console.log('✅ ValidationManager: Validierungsregeln aktualisiert');
    }

    /**
     * Cleanup-Methode
     */
    cleanup() {
        // Keine spezielle Cleanup-Logik erforderlich
        console.log('✅ ValidationManager: Cleanup durchgeführt');
    }
}

// Export für Modulverwendung
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FloweringValidationManager;
}
