/**
 * Flowering Widget - Erweitert um Marker-Verwaltung und Flush-Trigger
 * Hauptfunktionalität für das Blüte-Zeitmanagement
 */

class FloweringWidget {
    constructor() {
        this.currentPlantId = this.getPlantIdFromDOM();
        this.floweringData = null;
        this.trichomeData = null; // Trichom-Daten initialisieren
        this.markers = [];
        this.currentTab = 'overview'; // Standard-Tab ist Übersicht
        this.trichomeGuidelinesLoaded = false;
        this.widgetId = `flowering-widget-${this.currentPlantId}`;

        // Widget-Container finden und speichern
        this.element = document.querySelector('.flowering-widget-container');
        if (!this.element) {
            console.error('🌺 FloweringWidget: Widget-Container nicht gefunden');
            return;
        }

        // Module initialisieren
        this.trichomeManager = null; // Wird in init() initialisiert
        this.timelineManager = null; // Wird in init() initialisiert
        this.uiRenderer = null; // Wird in init() initialisiert

        this.init();
    }

    getPlantIdFromDOM() {
        // Delegiere an DataManager falls verfügbar
        if (this.dataManager) {
            return this.dataManager.getPlantIdFromDOM();
        }

        // Fallback: Originale Implementierung
        const plantIdElement = document.querySelector('[data-plant-id]');
        if (plantIdElement) {
            return plantIdElement.getAttribute('data-plant-id');
        }

        const urlParams = new URLSearchParams(window.location.search);
        const plantId = urlParams.get('plant_id');
        if (plantId) {
            return plantId;
        }

        const strainTypeContainer = document.getElementById('strainTypeContainer');
        if (strainTypeContainer) {
            return strainTypeContainer.getAttribute('data-plant-id');
        }

        return 'GG25L';
    }

    // Hilfsmethode für DOM-Abfragen innerhalb des Widgets
    getElement(selector) {
        if (this.element) {
            return this.element.querySelector(selector);
        }
        return document.querySelector(selector);
    }

    // Hilfsmethode für getElementById innerhalb des Widgets
    getElementById(id) {
        if (this.element) {
            return this.element.querySelector(`#${id}`);
        }
        return document.getElementById(id);
    }

    init() {
        // Module initialisieren
        this.initializeModules();

        this.setupEventListeners();
        this.loadInitialData();
        this.setupProgressCircle();
        this.setupLightingManager();
        this.setupSmartScheduling();
        this.setupPredictiveAnalytics();
        this.setupAdvancedML();
        this.setupIoTSensors();
    }

    /**
     * Initialisiert alle Module
     */
    initializeModules() {
        // DataManager initialisieren
        if (typeof FloweringDataManager !== 'undefined') {
            this.dataManager = new FloweringDataManager(this);
            console.log('🌺 FloweringWidget: DataManager erfolgreich initialisiert');
        } else {
            console.warn('🌺 FloweringWidget: DataManager nicht verfügbar - Fallback auf interne Methoden');
        }

        // TrichomeManager initialisieren
        if (typeof FloweringTrichomeManager !== 'undefined') {
            this.trichomeManager = new FloweringTrichomeManager(this);
            console.log('🌺 FloweringWidget: TrichomeManager erfolgreich initialisiert');
        } else {
            console.warn('🌺 FloweringWidget: TrichomeManager nicht verfügbar - Fallback auf interne Methoden');
        }

        // TimelineManager initialisieren
        if (typeof FloweringTimelineManager !== 'undefined') {
            this.timelineManager = new FloweringTimelineManager(this);
            console.log('🌺 FloweringWidget: TimelineManager erfolgreich initialisiert');
        } else {
            console.warn('🌺 FloweringWidget: TimelineManager nicht verfügbar - Fallback auf interne Methoden');
        }

        // UIRenderer initialisieren
        if (typeof FloweringUIRenderer !== 'undefined') {
            this.uiRenderer = new FloweringUIRenderer(this);
            console.log('🌺 FloweringWidget: UIRenderer erfolgreich initialisiert');
        } else {
            console.warn('🌺 FloweringWidget: UIRenderer nicht verfügbar - Fallback auf interne Methoden');
        }
    }

    setupEventListeners() {
        // Tab-Navigation (nur innerhalb des Widgets)
        this.element.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                // console.log('🌺 FloweringWidget: Tab-Button geklickt:', e.target.dataset.tab);
                await this.switchTab(e.target.dataset.tab);
            });
        });

        // Trichom-Tab Event-Listener
        // Wird jetzt vom TrichomeManager übernommen, falls verfügbar
        if (!this.trichomeManager) {
            const trichomeTab = this.getElementById('trichomeTab');
            if (trichomeTab) {
                trichomeTab.addEventListener('click', async () => {
                    await this.switchTab('trichome');
                });
            }
        }

        // Trichom-Tab spezifische Event-Listener
        // Der addObservationBtn wird jetzt in setupObservationForm() behandelt

        // Modal Event-Listener
        this.setupModalEventListeners();

        // Marker Event-Listener
        // Wird jetzt vom TimelineManager übernommen, falls verfügbar
        if (!this.timelineManager) {
            this.setupMarkerEventListeners();
        }

        // Flush-Trigger Event-Listener
        this.setupFlushTriggerEventListeners();

        // Event-Listener für den "Neuer Eintrag" Button
        // Wird jetzt vom TrichomeManager übernommen, falls verfügbar
        if (!this.trichomeManager) {
            this.setupObservationForm();
        }
        

        
        // Event-Listener für das Marker-Formular
        // Wird jetzt vom TimelineManager übernommen, falls verfügbar
        if (!this.timelineManager) {
            this.setupMarkerForm();
        }
        
        // Guidelines-Tabs Event-Listener
        this.setupGuidelinesTabListeners();
        
        // Marker-Filter Event-Listener
        // Wird jetzt vom TimelineManager übernommen, falls verfügbar
        if (!this.timelineManager) {
            this.setupMarkerFilterListeners();
        }
    }
    
    setupGuidelinesTabListeners() {
        const guidelineTabs = document.querySelectorAll('.guideline-tab');
        guidelineTabs.forEach(tab => {
            tab.addEventListener('click', () => this.switchGuidelineTab(tab.dataset.tab));
        });
    }
    
    setupMarkerFilterListeners() {
        // Kategorie-Filter
        const categoryFilter = this.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', () => {
                this.filterMarkers();
            });
        }

        // Wichtigkeit-Filter
        const importanceFilter = this.getElementById('importanceFilter');
        if (importanceFilter) {
            importanceFilter.addEventListener('change', () => {
                this.filterMarkers();
            });
        }
    }
    
    switchGuidelineTab(tabName) {
        // Finde den Container, in dem der geklickte Tab ist
        const clickedTab = event.target;
        const guidelinesContainer = clickedTab.closest('.flush-guidelines, .trichome-guidelines');
        
        if (!guidelinesContainer) return;
        
        // Alle Tabs und Panels in diesem Container deaktivieren
        guidelinesContainer.querySelectorAll('.guideline-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        guidelinesContainer.querySelectorAll('.guideline-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // Gewählten Tab aktivieren
        clickedTab.classList.add('active');
        
        // Entsprechendes Panel aktivieren
        const panelId = `${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Content`;
        const activePanel = guidelinesContainer.querySelector(`#flush${panelId}, #trichome${panelId}`);
        
        if (activePanel) activePanel.classList.add('active');
    }

    setupModalEventListeners() {
        // Trichom-Beobachtungs-Modal
        const trichomeModal = document.getElementById('trichomeObservationModal');
        const trichomeModalSave = document.getElementById('trichomeObservationModalSave');

        // Bootstrap 5 Modals schließen sich automatisch mit data-bs-dismiss="modal"
        // Keine separaten Event-Listener für Close/Cancel nötig
        // Wird jetzt vom TrichomeManager übernommen, falls verfügbar
        if (!this.trichomeManager && trichomeModalSave) {
            trichomeModalSave.addEventListener('click', (e) => {
                e.preventDefault();
                this.saveTrichomeObservation();
            });
        }

        // Trichom-Empfehlungs-Modal
        const recommendationModal = document.getElementById('trichomeRecommendationModal');
        // Bootstrap 5 Modals schließen sich automatisch mit data-bs-dismiss="modal"

        // Lösch-Bestätigungs-Modal
        const confirmDeleteModal = document.getElementById('confirmDeleteModal');
        // Bootstrap 5 Modals schließen sich automatisch mit data-bs-dismiss="modal"
        
        // Event-Listener für Lösch-Bestätigung
        const confirmDeleteBtn = document.querySelector('.btn-confirm-delete');
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', async () => {
                const modal = bootstrap.Modal.getInstance(confirmDeleteModal);
                if (modal) {
                    modal.hide();
                }
                
                // Löschvorgang durchführen
                const index = confirmDeleteBtn.getAttribute('data-delete-index');
                if (index !== null) {
                    await this.performDelete(parseInt(index));
                }
            });
        }

        // ESC-Taste zum Schließen
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const modal = bootstrap.Modal.getInstance(openModal);
                    if (modal) {
                        modal.hide();
                    }
                }
            }
        });
    }

    setupMarkerEventListeners() {
        // Add Marker Button
        const addMarkerBtn = this.getElementById('addMarkerBtn');
        if (addMarkerBtn) {
            addMarkerBtn.addEventListener('click', () => {
                this.openMarkerModal();
            });
        }

        // Edit Marker Button
        const editMarkerBtn = this.getElementById('editMarkerBtn');
        if (editMarkerBtn) {
            editMarkerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.editMarker(e.target.getAttribute('data-marker-id'));
            });
        }

        // Delete Marker Button
        const deleteMarkerBtn = this.getElementById('deleteMarkerBtn');
        if (deleteMarkerBtn) {
            deleteMarkerBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.deleteMarker(e.target.getAttribute('data-marker-id'));
            });
        }
    }

    setupFlushTriggerEventListeners() {
        // Manual Trigger Button
        const manualTriggerBtn = this.getElementById('manualTriggerBtn');
        if (manualTriggerBtn) {
            manualTriggerBtn.addEventListener('click', () => {
                this.triggerFlushManual();
            });
        }

        // Manual Flush Button
        const manualFlushBtn = this.getElementById('manualFlushBtn');
        if (manualFlushBtn) {
            manualFlushBtn.addEventListener('click', () => {
                this.triggerManualFlush();
            });
        }


    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.loadFloweringStatus(),
                this.loadTrichomeData(),
                this.loadMarkers(),
                this.loadFlushTriggerStatus(),
                this.loadPrediction()
            ]);
            
            // Direkte Trigger-Indicator-Aktualisierung nach dem Laden
            this.updateTriggerIndicator();
            
            // Phase 6 Features nach dem Laden der Daten anzeigen (mit Verzögerung für bessere UX)
            setTimeout(() => {
                this.showPhase6Features();
            }, 2000);
            
        } catch (error) {
            console.error('Fehler beim Laden der Daten:', error);
            this.showError('Fehler beim Laden der Daten');
        }
    }
    
    /**
     * Phase 6 Features anzeigen
     */
    showPhase6Features() {
        // Prüfen ob Beleuchtungs-Tab aktiv ist
        const lightingTab = this.element.querySelector('#lighting');
        const lightingOverview = this.element.querySelector('#lightingOverview');
        
        // Muster-Erkennung anzeigen
        this.startPatternRecognition();
        
        // Anomalie-Erkennung anzeigen
        this.startAnomalyDetection();
        
        // IoT Status anzeigen
        this.showIoTStatus();
    }

    async loadFloweringStatus() {
        // Delegiere an DataManager falls verfügbar
        if (this.dataManager) {
            try {
                const data = await this.dataManager.loadFloweringStatus();
                this.floweringData = data;
                this.updateOverview(data);
                this.updateProgressCircle(data.flowering_status.bloom_progress_percentage || data.flowering_status.progress_percentage);
            } catch (error) {
                console.error('🌺 FloweringWidget: Fehler beim Laden:', error);
                const phasePercentageEl = document.getElementById('phasePercentage');
                if (phasePercentageEl) {
                    phasePercentageEl.textContent = `(Fehler: ${error.message})`;
                }
            }
            return;
        }

        // Fallback: Originale Implementierung
        try {
            const response = await fetch(`/flowering/status/${this.currentPlantId}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            this.floweringData = data;
            this.updateOverview(data);
            this.updateProgressCircle(data.flowering_status.bloom_progress_percentage || data.flowering_status.progress_percentage);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden:', error);
            const phasePercentageEl = document.getElementById('phasePercentage');
            if (phasePercentageEl) {
                phasePercentageEl.textContent = `(Fehler: ${error.message})`;
            }
        }
    }

    async loadMarkers() {
        // Delegiere an TimelineManager falls verfügbar
        if (this.timelineManager) {
            return await this.timelineManager.loadMarkers();
        }

        // Fallback: Originale Implementierung
        const response = await fetch(`/flowering/markers/${this.currentPlantId}`);
        const data = await response.json();

        this.markers = data.markers;
        this.updateMarkersList();
        this.updateTriggerConditions();
    }

    async loadFlushTriggerStatus() {
        // Delegiere an DataManager falls verfügbar
        if (this.dataManager) {
            const data = await this.dataManager.loadFlushTriggerStatus();
            this.updateFlushTriggerStatus(data);
            return;
        }

        // Fallback: Originale Implementierung
        const response = await fetch(`/flowering/flush-trigger/${this.currentPlantId}`);
        const data = await response.json();

        this.updateFlushTriggerStatus(data);
    }

    async loadPrediction() {
        // Delegiere an DataManager falls verfügbar
        if (this.dataManager) {
            const data = await this.dataManager.loadPrediction();
            this.updatePrediction(data);
            return;
        }

        // Fallback: Originale Implementierung
        const response = await fetch(`/flowering/prediction/${this.currentPlantId}`);
        const data = await response.json();

        this.updatePrediction(data);
    }

    async loadTrichomeData() {
        // Delegiere an TrichomeManager falls verfügbar
        if (this.trichomeManager) {
            return await this.trichomeManager.loadTrichomeData();
        }

        // Fallback: Originale Implementierung
        try {
            await Promise.all([
                this.loadTrichomeStatus(),
                this.loadTrichomeTrigger(),
                this.loadTrichomeRecommendation(),
                this.loadTrichomeProgress(),
                this.loadTrichomeGuidelines()
            ]);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden der Trichom-Daten:', error);
        }
    }

    async loadTrichomeStatus() {
        // Delegiere an DataManager falls verfügbar
        if (this.dataManager) {
            try {
                const data = await this.dataManager.loadTrichomeStatus();
                this.trichomeData = data;

                if (data && data.has_data) {
                    this.updateTrichomeStatus(data);
                    this.updateTrichomeBadge(data);
                    this.updateTrichomeSegments(data);
                    this.updateFlushAlert(data);
                    this.updateFlushProgress(data.flush_progress || 0);

                    if (data.observations) {
                        this.updateObservationList(data.observations);
                    } else {
                        this.updateObservationList([]);
                    }
                } else {
                    this.updateTrichomeNoData();
                }
            } catch (error) {
                console.error('🌺 FloweringWidget: Fehler beim Laden der Trichom-Daten:', error);
                this.updateTrichomeNoData();
            }
            return;
        }

        // Fallback: Originale Implementierung
        try {
            const response = await fetch(`/flowering/trichome-status/${this.currentPlantId}`);
            if (response.ok) {
                const data = await response.json();

                this.trichomeData = data;

                if (data && data.has_data) {
                    this.updateTrichomeStatus(data);
                    this.updateTrichomeBadge(data);
                    this.updateTrichomeSegments(data);
                    this.updateFlushAlert(data);
                    this.updateFlushProgress(data.flush_progress || 0);

                    if (data.observations) {
                        this.updateObservationList(data.observations);
                    } else {
                        this.updateObservationList([]);
                    }
                } else {
                    this.updateTrichomeNoData();
                }
            } else {
                console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Status');
                this.updateTrichomeNoData();
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Status:', error);
            this.updateTrichomeNoData();
        }
    }

    async loadTrichomeTrigger() {
        try {
            const response = await fetch(`/flowering/trichome-trigger/${this.currentPlantId}`);
            const data = await response.json();
            
            this.trichomeTriggerData = data; // Daten speichern
            this.updateTrichomeTrigger(data);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Triggers:', error);
        }
    }

    async loadTrichomeRecommendation() {
        // Delegiere an DataManager falls verfügbar
        if (this.dataManager) {
            try {
                const data = await this.dataManager.loadTrichomeRecommendation();
                this.trichomeRecommendationData = data;
                this.updateTrichomeRecommendation(data);
            } catch (error) {
                console.error('🌺 FloweringWidget: Fehler beim Laden der Trichom-Empfehlung:', error);
            }
            return;
        }

        // Fallback: Originale Implementierung
        try {
            const response = await fetch(`/flowering/trichome-recommendation/${this.currentPlantId}`);
            const data = await response.json();

            this.trichomeRecommendationData = data;
            this.updateTrichomeRecommendation(data);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden der Trichom-Empfehlung:', error);
        }
    }

    async loadTrichomeProgress() {
        // Delegiere an DataManager falls verfügbar
        if (this.dataManager) {
            try {
                const data = await this.dataManager.loadTrichomeProgress();
                this.trichomeProgressData = data;
                this.updateTrichomeProgress(data);
            } catch (error) {
                console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Fortschritts:', error);
            }
            return;
        }

        // Fallback: Originale Implementierung
        try {
            const response = await fetch(`/flowering/trichome-progress/${this.currentPlantId}`);
            const data = await response.json();

            this.trichomeProgressData = data;
            this.updateTrichomeProgress(data);
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden des Trichom-Fortschritts:', error);
        }
    }

    async loadTrichomeGuidelines() {
        try {
            const response = await fetch(`/flowering/trichome-guidelines/${this.currentPlantId}`);
            const data = await response.json();
            
            if (data && data.trichome_guidelines) {
                this.loadTrichomeGuidelinesForTab(data.trichome_guidelines, data.strain_type);
            } else {
                console.error('🌺 FloweringWidget: Keine Trichome-Guidelines in der Antwort gefunden');
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Laden der Trichome-Guidelines:', error);
        }
    }

    async switchTab(tabName) {
        // Alle Tab-Buttons deaktivieren
        this.element.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Alle Tab-Panes ausblenden
        this.element.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        
        // Gewählten Tab aktivieren
        const activeTab = this.element.querySelector(`[data-tab="${tabName}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }
        
        // Entsprechendes Tab-Pane aktivieren
        const activePane = this.element.querySelector(`#${tabName}`);
        if (activePane) {
            activePane.classList.add('active');
        }
        
        this.currentTab = tabName;
        
        // Tab-spezifische Daten laden
        switch (tabName) {
            case 'overview':
                await this.updateOverview(this.floweringData);
                break;
            case 'trichome':
                await this.loadTrichomeData();
                break;
            case 'lighting':
                await this.loadLightingData();
                break;
            case 'markers':
                this.updateMarkersList();
                break;
            case 'flush-trigger':
                await this.loadFlushTriggerStatus();
                break;
            case 'prediction':
                await this.loadPrediction();
                break;
        }
    }

        updateOverview(data) {
        // Delegiere an UIRenderer falls verfügbar
        if (this.uiRenderer) {
            return this.uiRenderer.updateOverview(data);
        }

        // Fallback: Originale Implementierung
        const status = data.flowering_status;
        const strain = data.strain_profile;

        // Grunddaten
        const strainNameEl = document.getElementById('strainName');
        if (strainNameEl) strainNameEl.textContent = strain.strain || strain.name || 'Sorte nicht angegeben';

        const currentDayEl = document.getElementById('currentDay');
        if (currentDayEl) currentDayEl.textContent = status.current_day;

        const currentPhaseEl = document.getElementById('currentPhase');
        if (currentPhaseEl) currentPhaseEl.textContent = this.getPhaseName(status.phase);
        
        // Phasen-Beschreibung aktualisieren
        const phaseDescriptionEl = document.getElementById('phaseDescription');
        if (phaseDescriptionEl) {
            const phaseName = this.getPhaseName(status.phase);
            const currentDay = status.current_day;
            const totalDays = strain.flowering_duration_days ? strain.flowering_duration_days[1] : 75;
            const daysRemaining = totalDays - currentDay;
            
            phaseDescriptionEl.textContent = `${phaseName} - Tag ${currentDay} von ${totalDays} (${daysRemaining} Tage verbleibend)`;
        }
        
        // Beide Fortschrittswerte verwenden - konsistent aus Backend
        const growProgress = status.grow_progress_percentage || 0;
        const bloomProgress = status.bloom_progress_percentage || status.progress_percentage || 0;
        
        // Header mit beiden Werten aktualisieren
        const phasePercentageEl = document.getElementById('phasePercentage');
        if (phasePercentageEl) {
            phasePercentageEl.textContent = `(Grow: ${growProgress.toFixed(1)}% | Blüte: ${bloomProgress.toFixed(1)}%)`;
            phasePercentageEl.removeAttribute('title'); // Tooltip entfernen
        }
        
        // Tooltip von der gesamten Phase-Card entfernen
        const phaseCard = document.querySelector('.phase-card');
        if (phaseCard) {
            phaseCard.removeAttribute('title');
            
            // Tooltip von allen Elementen in der Phase-Card entfernen
            const phaseCardElements = phaseCard.querySelectorAll('*');
            phaseCardElements.forEach(element => {
                element.removeAttribute('title');
            });
        }
        
        // Fortschrittsbalken und Label mit Blüte-Fortschritt
        const phaseProgressTextEl = document.getElementById('phaseProgressText');
        if (phaseProgressTextEl) {
            phaseProgressTextEl.textContent = `${bloomProgress.toFixed(1)}%`;
            phaseProgressTextEl.removeAttribute('title'); // Tooltip entfernen
        }
        
        const phaseProgressEl = document.getElementById('phaseProgress');
        if (phaseProgressEl) phaseProgressEl.style.width = `${bloomProgress}%`;
        
        // Progress Circle im Header aktualisieren
        const progressTextEl = document.getElementById('progressText');
        if (progressTextEl) {
            progressTextEl.textContent = `${bloomProgress.toFixed(1)}%`;
            progressTextEl.removeAttribute('title'); // Tooltip entfernen
        }
        
        // Flush-Status
        const flushStatus = data.flush_status;
        const flushCard = document.getElementById('flushCard');
        const flushStatusEl = document.getElementById('flushStatus');
        const flushCountdown = document.getElementById('flushCountdown');
        const flushReason = document.getElementById('flushReason');

        if (flushStatus.triggered) {
            if (flushCard) flushCard.classList.add('triggered');
            if (flushStatusEl) {
                flushStatusEl.textContent = 'Flush empfohlen';
                flushStatusEl.style.color = '#ef4444';
            }
            if (flushCountdown) {
                flushCountdown.innerHTML = `
                    <span class="days-remaining">${flushStatus.flush_day_today}</span>
                    <span class="countdown-label">Flush-Tag</span>
                `;
            }
        } else {
            if (flushCard) flushCard.classList.remove('triggered');
            if (flushStatusEl) {
                flushStatusEl.textContent = 'Noch nicht empfohlen';
                flushStatusEl.style.color = '#f59e0b';
            }
            if (flushCountdown) {
                const daysUntilFlush = flushStatus.start_recommended - status.current_day;
                flushCountdown.innerHTML = `
                    <span class="days-remaining">${daysUntilFlush}</span>
                    <span class="countdown-label">Tage bis Flush</span>
                `;
            }
        }

        if (flushReason) flushReason.textContent = flushStatus.status_message;

        // Ernte-Prognose
        const recommendation = data.recommendation;
        const harvestEarlyEl = document.getElementById('harvestEarly');
        if (harvestEarlyEl) harvestEarlyEl.textContent = `Tag ${recommendation.harvest_early[0]}-${recommendation.harvest_early[1]}`;
        
        const harvestOptimalEl = document.getElementById('harvestOptimal');
        if (harvestOptimalEl) harvestOptimalEl.textContent = `Tag ${recommendation.harvest_optimal[0]}-${recommendation.harvest_optimal[1]}`;
        
        const harvestLateEl = document.getElementById('harvestLate');
        if (harvestLateEl) harvestLateEl.textContent = `Tag ${recommendation.harvest_late[0]}-${recommendation.harvest_late[1]}`;

        // Meilensteine
        this.updateMilestones(data);
    }

    updateMilestones(data) {
        // Delegiere an UIRenderer falls verfügbar
        if (this.uiRenderer) {
            return this.uiRenderer.updateMilestones(data);
        }

        // Fallback: Originale Implementierung
        const milestonesList = document.getElementById('milestonesList');
        if (!milestonesList) return;

        const currentDay = data.flowering_status.current_day;
        const milestones = [];

        // Flush-Meilenstein
        if (data.flush_status && data.flush_status.start_recommended > currentDay) {
            const daysUntilFlush = data.flush_status.start_recommended - currentDay;
            milestones.push({
                icon: '🚿',
                title: 'Flush-Start',
                details: `Tag ${data.flush_status.start_recommended}`,
                countdown: `${daysUntilFlush} Tage verbleibend`
            });
        }

        // Ernte-Meilenstein
        if (data.harvest_recommendation) {
            const optimalHarvest = data.harvest_recommendation.harvest_optimal[0];
            if (optimalHarvest > currentDay) {
                const daysUntilHarvest = optimalHarvest - currentDay;
                milestones.push({
                    icon: '✂️',
                    title: 'Optimaler Erntezeitpunkt',
                    details: `Tag ${optimalHarvest}`,
                    countdown: `${daysUntilHarvest} Tage verbleibend`
                });
            }
        }

        // Meilensteine rendern
        milestonesList.innerHTML = milestones.map(milestone => `
            <div class="milestone-item">
                <div class="milestone-icon">${milestone.icon}</div>
                <div class="milestone-content">
                    <div class="milestone-title">${milestone.title}</div>
                    <div class="milestone-details">${milestone.details}</div>
                </div>
                <div class="milestone-countdown">${milestone.countdown}</div>
            </div>
        `).join('');
    }



    updateMarkersList() {
        // Delegiere an TimelineManager falls verfügbar
        if (this.timelineManager) {
            return this.timelineManager.updateMarkersList();
        }

        // Fallback: Originale Implementierung
        const markersList = document.getElementById('markersList');
        if (!markersList) return;

        markersList.innerHTML = this.markers.map(marker => `
            <div class="marker-item" data-marker-id="${marker.id}" data-category="${marker.category}" data-importance="${marker.importance}">
                <div class="marker-header">
                    <div class="marker-info">
                        <div class="marker-title">${marker.event_name}</div>
                        <div class="marker-meta">
                            <span class="marker-category">${this.getCategoryName(marker.category)}</span>
                            <span class="marker-importance ${marker.importance}">${marker.importance}</span>
                            <span>Tag ${marker.bloom_day}</span>
                            <span>${this.formatDate(marker.date)}</span>
                        </div>
                    </div>
                    <div class="marker-actions">
                        <button class="marker-btn edit" data-marker-id="${marker.id}">
                            ✏️ Bearbeiten
                        </button>
                        <button class="marker-btn delete" data-marker-id="${marker.id}">
                            🗑️ Löschen
                        </button>
                    </div>
                </div>
                ${marker.notes ? `<div class="marker-notes">${marker.notes}</div>` : ''}
            </div>
        `).join('');

        // Event-Listener für die neuen Buttons hinzufügen
        this.setupMarkerActionListeners();
    }

    updateTriggerConditions() {
        const currentDay = this.floweringData?.flowering_status?.current_day || 0;
        const floweringRange = this.floweringData?.strain_profile?.flowering_duration_days || [60, 75];
        
        // Blütefortschritt
        const bloomProgress = (currentDay / floweringRange[1]) * 100;
        const bloomProgressBar = document.getElementById('bloomProgressBar');
        const bloomProgressText = document.getElementById('bloomProgressText');
        const bloomProgressStatus = document.getElementById('bloomProgressStatus');
        
        if (bloomProgressBar) bloomProgressBar.style.width = `${bloomProgress}%`;
        if (bloomProgressText) bloomProgressText.textContent = `Tag ${currentDay}/${floweringRange[1]} (${bloomProgress.toFixed(1)}%)`;
        
        const bloomThreshold = 0.85 * floweringRange[1];
        if (bloomProgressStatus) {
            if (currentDay >= bloomThreshold) {
                bloomProgressStatus.textContent = '✅';
                bloomProgressStatus.className = 'condition-status met';
            } else {
                bloomProgressStatus.textContent = '❌';
                bloomProgressStatus.className = 'condition-status not-met';
            }
        }

        // Trichome-Status
        const trichomeMarkers = this.markers.filter(m => 
            m.event_type === 'trichome_milky' || m.event_type === 'trichome_amber'
        );
        
        const milkyCount = trichomeMarkers.filter(m => m.event_type === 'trichome_milky').length;
        const amberCount = trichomeMarkers.filter(m => m.event_type === 'trichome_amber').length;
        
        const milkyCountEl = document.getElementById('milkyCount');
        if (milkyCountEl) milkyCountEl.textContent = `${milkyCount} Marker`;
        
        const amberCountEl = document.getElementById('amberCount');
        if (amberCountEl) amberCountEl.textContent = `${amberCount} Marker`;
        
        const trichomeStatus = document.getElementById('trichomeStatus');
        if (trichomeStatus) {
            if (milkyCount >= 1 && currentDay >= 45) {
                trichomeStatus.textContent = '✅';
                trichomeStatus.className = 'condition-status met';
            } else {
                trichomeStatus.textContent = '❌';
                trichomeStatus.className = 'condition-status not-met';
            }
        }

        // Pistillen-Status
        const pistilMarkers = this.markers.filter(m => m.event_type === 'pistils_retracted');
        const pistilCountEl = document.getElementById('pistilCount');
        if (pistilCountEl) pistilCountEl.textContent = `${pistilMarkers.length} Marker`;
        
        const pistilStatus = document.getElementById('pistilStatus');
        if (pistilStatus) {
            if (pistilMarkers.length > 0) {
                pistilStatus.textContent = '✅';
                pistilStatus.className = 'condition-status met';
            } else {
                pistilStatus.textContent = '❌';
                pistilStatus.className = 'condition-status not-met';
            }
        }
    }

    updateFlushTriggerStatus(data) {
        // Delegiere an UIRenderer falls verfügbar
        if (this.uiRenderer) {
            return this.uiRenderer.updateFlushTriggerStatus(data);
        }

        // Fallback: Originale Implementierung
        const flushStatus = data.flush_status;
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');

        // Konsistente Logik mit updateTriggerIndicator
        if (flushStatus.triggered) {
            if (statusIndicator) statusIndicator.classList.add('triggered');
            if (statusText) statusText.textContent = 'Flush aktiv';
        } else if (data.flowering_status && data.flowering_status.current_day >= flushStatus.start_recommended) {
            if (statusIndicator) statusIndicator.classList.add('triggered');
            if (statusText) statusText.textContent = 'Flush empfohlen';
        } else {
            if (statusIndicator) statusIndicator.classList.remove('triggered');
            if (statusText) statusText.textContent = 'Überwachung aktiv';
        }

        // Trigger-Indicator direkt aktualisieren
        this.updateTriggerIndicator(data);

        // Flush-Status-Details
        const flushStatusDetailEl = document.getElementById('flushStatusDetail');
        if (flushStatusDetailEl) {
            flushStatusDetailEl.textContent = flushStatus.triggered ? 'Ausgelöst' : 'Nicht ausgelöst';
        }
        
        const flushStartDayEl = document.getElementById('flushStartDay');
        if (flushStartDayEl) flushStartDayEl.textContent = `Tag ${flushStatus.start_recommended}`;
        
        const flushDurationEl = document.getElementById('flushDuration');
        if (flushDurationEl) flushDurationEl.textContent = `${flushStatus.flush_duration_days} Tage`;
        
        const flushTargetDayEl = document.getElementById('flushTargetDay');
        if (flushTargetDayEl) flushTargetDayEl.textContent = `Tag ${flushStatus.flush_target_day}`;
        
        // Manual Flush Button Status aktualisieren
        const manualFlushBtn = document.getElementById('manualFlushBtn');
        if (manualFlushBtn) {
            if (flushStatus.triggered) {
                manualFlushBtn.disabled = true;
                manualFlushBtn.textContent = 'Flush bereits aktiv';
            } else {
                // Button aktivieren wenn Trichom-Daten vorhanden sind
                const hasTrichomeData = data.trichome_status && data.trichome_status.current_values;
                manualFlushBtn.disabled = !hasTrichomeData;
                manualFlushBtn.textContent = hasTrichomeData ? 'Flush manuell starten' : 'Keine Trichom-Daten';
            }
        }

        // Trigger-Indicator Status aktualisieren
        const triggerIndicator = this.getElementById('triggerIndicator');
        const triggerDot = this.getElementById('triggerDot');
        const triggerText = this.getElementById('triggerText');
        const triggerMessage = this.getElementById('triggerMessage');
        
        if (triggerIndicator && triggerDot && triggerText && triggerMessage) {

            
            if (flushStatus.triggered) {
                // Flush ist aktiv - ROT
                triggerDot.className = 'trigger-dot active';
                triggerText.textContent = 'Flush aktiv';
                triggerMessage.textContent = `Flush läuft seit ${flushStatus.flush_day_today} Tagen`;
                triggerMessage.className = 'trigger-message high';
            } else if (data.flowering_status && data.flowering_status.current_day >= flushStatus.start_recommended) {
                // Flush wird empfohlen - ORANGE (aktueller Tag >= empfohlener Start-Tag)
                triggerDot.className = 'trigger-dot active';
                triggerText.textContent = 'Flush empfohlen';
                triggerMessage.textContent = `Flush kann gestartet werden (Tag ${flushStatus.start_recommended})`;
                triggerMessage.className = 'trigger-message medium';
            } else {
                // Flush noch nicht empfohlen - GRÜN
                triggerDot.className = 'trigger-dot inactive';
                triggerText.textContent = 'Überwachung aktiv';
                triggerMessage.textContent = `70% milchige, 0% bernstein Trichome`;
                triggerMessage.className = 'trigger-message low';
            }
        }

        // Guidelines in separate Container aufteilen
        
        // Prüfe ob Guidelines-Daten vorhanden sind und lade sie für beide Tabs
        if (data && data.flush_guidelines) {
            const guidelines = data.flush_guidelines;
            const strainType = data.strain_type; // Verwende strain_type aus den API-Daten
            
            // Lade Guidelines für Flush-Trigger-Tab
            this.loadGuidelinesForTab(guidelines, 'flush', strainType);
            
            // Lade Guidelines für Trichome-Tab
            this.loadGuidelinesForTab(guidelines, 'trichome', strainType);
            
            // Richte Guidelines-Tab-Event-Listener ein
            this.setupGuidelinesTabListeners();
        }
    }
    
    loadGuidelinesForTab(guidelines, tabType, strainType) {
        const prefix = tabType === 'flush' ? 'flush' : 'trichome';
        
        // Startbedingungen - nur für den relevanten Strain-Typ
        const startbedingungenContent = this.getElementById(`${prefix}StartbedingungenContent`);
        if (startbedingungenContent && guidelines.startbedingungen) {
            let startbedingungenHtml = '';
            
            // Zeige nur die Guidelines für den relevanten Strain-Typ
            if (guidelines.startbedingungen[strainType]) {
                const strainGuidelines = guidelines.startbedingungen[strainType];
                const strainName = strainType === 'autoflower' ? 'Autoflower' : 'Photoperiod';
                
                startbedingungenHtml += '<div class="strain-type-section">';
                startbedingungenHtml += `<h6>🎯 ${strainName}-Startbedingungen:</h6>`;
                startbedingungenHtml += '<ul>';
                strainGuidelines.indikatoren.forEach(indikator => {
                    startbedingungenHtml += `<li>${indikator}</li>`;
                });
                startbedingungenHtml += '</ul>';
                startbedingungenHtml += `<p><strong>Zeitpunkt:</strong> ${strainGuidelines.zeitpunktTageVorErnte} Tage vor Ernte</p>`;
                startbedingungenHtml += '</div>';
                

            } else {
                startbedingungenHtml += '<div class="no-guidelines">';
                startbedingungenHtml += `<p>Keine spezifischen Guidelines für ${strainType} verfügbar.</p>`;
                startbedingungenHtml += '</div>';
            }
            
            startbedingungenContent.innerHTML = startbedingungenHtml;
        }
        
        // Methoden
        const methodenContent = this.getElementById(`${prefix}MethodenContent`);
        if (methodenContent && guidelines.methoden) {
            let methodenHtml = '';
            guidelines.methoden.forEach((methode, index) => {
                methodenHtml += '<div class="method-item">';
                methodenHtml += `<h6>${methode.typ}</h6>`;
                methodenHtml += `<p>${methode.beschreibung}</p>`;
                
                if (methode.vorteile || methode.nachteile) {
                    methodenHtml += '<div class="method-pros-cons">';
                    if (methode.vorteile) {
                        methodenHtml += '<div class="pros">';
                        methodenHtml += '<strong>✅ Vorteile:</strong>';
                        methodenHtml += '<ul>';
                        methode.vorteile.forEach(vorteil => {
                            methodenHtml += `<li>${vorteil}</li>`;
                        });
                        methodenHtml += '</ul>';
                        methodenHtml += '</div>';
                    }
                    if (methode.nachteile) {
                        methodenHtml += '<div class="cons">';
                        methodenHtml += '<strong>❌ Nachteile:</strong>';
                        methodenHtml += '<ul>';
                        methode.nachteile.forEach(nachteil => {
                            methodenHtml += `<li>${nachteil}</li>`;
                        });
                        methodenHtml += '</ul>';
                        methodenHtml += '</div>';
                    }
                    methodenHtml += '</div>';
                }
                methodenHtml += '</div>';
            });
            methodenContent.innerHTML = methodenHtml;
        }
        
        // Fehlerquellen
        const fehlerquellenContent = this.getElementById(`${prefix}FehlerquellenContent`);
        if (fehlerquellenContent && guidelines.fehlerquellen) {
            let fehlerquellenHtml = '<ul class="error-list">';
            guidelines.fehlerquellen.forEach(fehler => {
                fehlerquellenHtml += `<li>${fehler}</li>`;
            });
            fehlerquellenHtml += '</ul>';
            fehlerquellenContent.innerHTML = fehlerquellenHtml;
        }
        
        // Faustregeln
        const faustregelnContent = this.getElementById(`${prefix}FaustregelnContent`);
        if (faustregelnContent && guidelines.faustregeln) {
            let faustregelnHtml = '<ul class="rule-list">';
            guidelines.faustregeln.forEach(regel => {
                faustregelnHtml += `<li>${regel}</li>`;
            });
            faustregelnHtml += '</ul>';
            faustregelnContent.innerHTML = faustregelnHtml;
        }
    }

    loadTrichomeGuidelinesForTab(guidelines, strainType) {
        
        // Event-Marker
        const startbedingungenContent = this.getElementById('trichomeStartbedingungenContent');
        if (startbedingungenContent && guidelines.eventMarker) {
            let startbedingungenHtml = '<div class="event-markers-section">';
            startbedingungenHtml += '<h6>🎯 Trichome-Event-Marker:</h6>';
            startbedingungenHtml += '<ul>';
            guidelines.eventMarker.forEach(marker => {
                // Zeige alle Event-Marker, nicht nur trichome-spezifische
                startbedingungenHtml += `<li><strong>${marker.beschreibung}</strong>`;
                startbedingungenHtml += `<br><small>Trigger: ${marker.trigger.join(', ')}</small>`;
                startbedingungenHtml += `<br><small>Bedeutung: ${marker.bedeutung}</small></li>`;
            });
            startbedingungenHtml += '</ul></div>';
            startbedingungenContent.innerHTML = startbedingungenHtml;
        }
        
        // Harvest-Empfehlungen
        const methodenContent = this.getElementById('trichomeMethodenContent');
        if (methodenContent && guidelines.harvestEmpfehlungen) {
            let methodenHtml = '<div class="harvest-recommendations-section">';
            methodenHtml += '<h6>🌾 Ernte-Empfehlungen:</h6>';
            
            Object.entries(guidelines.harvestEmpfehlungen).forEach(([type, data]) => {
                if (type !== 'meta') {
                    methodenHtml += `<div class="harvest-type">`;
                    methodenHtml += `<h7>${type.charAt(0).toUpperCase() + type.slice(1)}:</h7>`;
                    methodenHtml += `<p><strong>Wirkung:</strong> ${data.wirkung}</p>`;
                    if (data.trichome) {
                        methodenHtml += `<p><strong>Trichome:</strong> ${data.trichome.milchig} milchig, ${data.trichome.bernstein} bernstein</p>`;
                    }
                    if (data.tageNachBluete) {
                        methodenHtml += `<p><strong>Zeitraum:</strong> ${data.tageNachBluete.join('-')} Tage nach Blüte</p>`;
                    }
                    methodenHtml += '</div>';
                }
            });
            methodenHtml += '</div>';
            methodenContent.innerHTML = methodenHtml;
        }
        
        // Strain-Abhängigkeit
        const fehlerquellenContent = this.getElementById('trichomeFehlerquellenContent');
        if (fehlerquellenContent && guidelines.strainAbhaengigkeit) {
            let fehlerquellenHtml = '<div class="strain-dependency-section">';
            fehlerquellenHtml += '<h6>🌱 Strain-Abhängigkeit:</h6>';
            fehlerquellenHtml += '<ul>';
            
            if (guidelines.strainAbhaengigkeit.flushVorErnteTage) {
                Object.entries(guidelines.strainAbhaengigkeit.flushVorErnteTage).forEach(([type, days]) => {
                    fehlerquellenHtml += `<li><strong>${type}:</strong> ${days} Tage Flush vor Ernte</li>`;
                });
            }
            
            if (guidelines.strainAbhaengigkeit.beispiele) {
                fehlerquellenHtml += '</ul><h6>🌱 Beispiele:</h6><ul>';
                Object.entries(guidelines.strainAbhaengigkeit.beispiele).forEach(([strain, data]) => {
                    fehlerquellenHtml += `<li><strong>${strain}:</strong> ${data.gesamtBlüteTage.join('-')} Tage Blüte, Flush ab Tag ${data.flushEmpfehlungAb}</li>`;
                });
            }
            
            fehlerquellenHtml += '</ul>';
            fehlerquellenHtml += '</div>';
            fehlerquellenContent.innerHTML = fehlerquellenHtml;
        }
        
        // Faustregeln
        const faustregelnContent = this.getElementById('trichomeFaustregelnContent');
        if (faustregelnContent && guidelines.faustregeln) {
            let faustregelnHtml = '<ul class="rule-list">';
            guidelines.faustregeln.forEach(regel => {
                faustregelnHtml += `<li>${regel}</li>`;
            });
            faustregelnHtml += '</ul>';
            faustregelnContent.innerHTML = faustregelnHtml;
        }
    }

    updateTriggerIndicator(data = null) {
        // Verwende gespeicherte Daten oder übergebene Daten
        const flushData = data || this.floweringData;
        if (!flushData) {
    
            return;
        }

        const flushStatus = flushData.flush_status;
        const currentDay = flushData.flowering_status?.current_day;



        const triggerDot = this.getElementById('triggerDot');
        const triggerText = this.getElementById('triggerText');
        const triggerMessage = this.getElementById('triggerMessage');

        if (triggerDot && triggerText && triggerMessage) {
            if (flushStatus.triggered) {
                // Flush ist aktiv - ROT
                triggerDot.className = 'trigger-dot active';
                triggerText.textContent = '🚿 Flush aktiv';
                triggerMessage.textContent = `Flush läuft seit ${flushStatus.flush_day_today} Tagen`;
                triggerMessage.className = 'trigger-message high';

            } else if (currentDay >= flushStatus.start_recommended) {
                // Flush wird empfohlen - ORANGE
                triggerDot.className = 'trigger-dot active';
                triggerText.textContent = '🚿 Flush empfohlen';
                triggerMessage.textContent = `Flush kann gestartet werden (Tag ${flushStatus.start_recommended})`;
                triggerMessage.className = 'trigger-message medium';

            } else {
                // Flush noch nicht empfohlen - GRÜN
                triggerDot.className = 'trigger-dot inactive';
                triggerText.textContent = '🚿 Flush-Trigger';
                triggerMessage.textContent = `Überwachung aktiv`;
                triggerMessage.className = 'trigger-message low';

            }
        }
    }

    updatePrediction(data) {
        // Delegiere an UIRenderer falls verfügbar
        if (this.uiRenderer) {
            return this.uiRenderer.updatePrediction(data);
        }

        // Fallback: Originale Implementierung
        const predictions = data.predictions;

        // Flush-Start
        const flushStart = predictions.flush_start;
        const flushStartDateEl = document.getElementById('flushStartDate');
        if (flushStartDateEl) flushStartDateEl.textContent = this.formatDate(flushStart.date);

        const flushStartCountdownEl = document.getElementById('flushStartCountdown');
        if (flushStartCountdownEl) {
            flushStartCountdownEl.textContent = `${flushStart.days_remaining} Tage verbleibend`;
        }

        // Ernte optimal
        const harvestOptimal = predictions.harvest_optimal;
        const harvestOptimalDateEl = document.getElementById('harvestOptimalDate');
        if (harvestOptimalDateEl) harvestOptimalDateEl.textContent = this.formatDate(harvestOptimal.date);
        
        const harvestOptimalCountdownEl = document.getElementById('harvestOptimalCountdown');
        if (harvestOptimalCountdownEl) {
            harvestOptimalCountdownEl.textContent = `${harvestOptimal.days_remaining} Tage verbleibend`;
        }

        // Empfehlungen
        const recommendationsList = document.getElementById('recommendationsList');
        if (recommendationsList) {
            recommendationsList.innerHTML = data.recommendations.immediate.map(rec => `
                <div class="recommendation-item">
                    <div class="recommendation-icon">💡</div>
                    <div class="recommendation-text">${rec}</div>
                </div>
            `).join('');
        }

        // Risikofaktoren
        this.updateRiskFactors(data.risk_factors);
    }

    updateRiskFactors(riskFactors) {
        // Hohe Risiken
        const highRisks = document.getElementById('highRisks');
        if (highRisks) {
            const highRisksList = highRisks.querySelector('.risk-list');
            if (highRisksList) {
                highRisksList.innerHTML = riskFactors.high.map(risk => 
                    `<li>${risk}</li>`
                ).join('');
            }
        }

        // Mittlere Risiken
        const mediumRisks = document.getElementById('mediumRisks');
        if (mediumRisks) {
            const mediumRisksList = mediumRisks.querySelector('.risk-list');
            if (mediumRisksList) {
                mediumRisksList.innerHTML = riskFactors.medium.map(risk => 
                    `<li>${risk}</li>`
                ).join('');
            }
        }

        // Niedrige Risiken
        const lowRisks = document.getElementById('lowRisks');
        if (lowRisks) {
            const lowRisksList = lowRisks.querySelector('.risk-list');
            if (lowRisksList) {
                lowRisksList.innerHTML = riskFactors.low.map(risk => 
                    `<li>${risk}</li>`
                ).join('');
            }
        }
    }

    // Modal-Funktionen
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'flex';
            modal.classList.add('show');
            document.body.classList.add('modal-open');
            
            // Focus auf erstes Input-Feld setzen
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        }
    }

    closeModal(modal) {
        if (typeof modal === 'string') {
            modal = document.getElementById(modal);
        }
        if (modal) {
            modal.style.display = 'none';
            modal.classList.remove('show');
            document.body.classList.remove('modal-open');
        }
    }

    // Marker-Methoden
    openMarkerModal(markerId = null) {
        const modal = document.getElementById('markerModal');
        const title = document.getElementById('markerModalTitle');
        
        if (markerId) {
            title.textContent = 'Marker bearbeiten';
            this.fillMarkerForm(markerId);
        } else {
            title.textContent = 'Neuer Marker';
            this.clearMarkerForm();
        }
        
        this.openModal('markerModal');
    }

    fillMarkerForm(markerId) {
        // Marker-Daten laden und Formular füllen
    }

    clearMarkerForm() {
        const form = document.getElementById('markerForm');
        if (form) {
            form.reset();
        }
    }

    async saveMarker() {
        // Delegiere an TimelineManager falls verfügbar
        if (this.timelineManager) {
            return await this.timelineManager.saveMarker();
        }

        // Fallback: Marker-Speicherlogik implementieren
        console.warn('🌺 FloweringWidget: saveMarker Fallback - Implementierung erforderlich');
    }

    async editMarker(markerId) {
        // Delegiere an TimelineManager falls verfügbar
        if (this.timelineManager) {
            return await this.timelineManager.editMarker(markerId);
        }

        // Fallback: Originale Implementierung
        this.openMarkerModal(markerId);
    }

    async deleteMarker(markerId) {
        // Delegiere an TimelineManager falls verfügbar
        if (this.timelineManager) {
            return await this.timelineManager.deleteMarker(markerId);
        }

        // Fallback: Marker-Löschlogik implementieren
        console.warn('🌺 FloweringWidget: deleteMarker Fallback - Implementierung erforderlich');
    }

    filterMarkers() {
        // Delegiere an TimelineManager falls verfügbar
        if (this.timelineManager) {
            return this.timelineManager.filterMarkers();
        }

        // Fallback: Originale Implementierung
        const categoryFilter = this.getElementById('categoryFilter');
        const importanceFilter = this.getElementById('importanceFilter');
        const markersList = this.getElementById('markersList');

        if (!categoryFilter || !importanceFilter || !markersList) {
            console.error('🌺 FloweringWidget: Filter-Elemente nicht gefunden');
            return;
        }

        const selectedCategory = categoryFilter.value;
        const selectedImportance = importanceFilter.value;

        const markerItems = markersList.querySelectorAll('.marker-item');

        markerItems.forEach(markerItem => {
            const category = markerItem.dataset.category;
            const importance = markerItem.dataset.importance;

            let showMarker = true;

            if (selectedCategory && category !== selectedCategory) {
                showMarker = false;
            }

            if (selectedImportance && importance !== selectedImportance) {
                showMarker = false;
            }

            if (showMarker) {
                markerItem.style.display = 'block';
                markerItem.classList.remove('filtered-out');
            } else {
                markerItem.style.display = 'none';
                markerItem.classList.add('filtered-out');
            }
        });
    }

    // Flush-Trigger-Methoden
    async triggerFlushManual() {
        // Manueller Flush-Trigger-Logik implementieren
    }

    triggerManualFlush() {
        // Manueller Flush-Logik implementieren
    }

    // Hilfsfunktionen
    setupProgressCircle() {
        const circle = document.getElementById('progressCircle');
        if (!circle) return;
        
        const radius = circle.r.baseVal.value;
        const circumference = 2 * Math.PI * radius;
        
        circle.style.strokeDasharray = circumference;
        circle.style.strokeDashoffset = circumference;
    }

    updateProgressCircle(percentage) {
        // Delegiere an UIRenderer falls verfügbar
        if (this.uiRenderer) {
            return this.uiRenderer.updateProgressCircle(percentage);
        }

        // Fallback: Originale Implementierung
        const circle = document.getElementById('progressCircle');
        const progressText = document.getElementById('progressText');

        if (!circle || !progressText) return;

        const radius = circle.r.baseVal.value;
        const circumference = 2 * Math.PI * radius;

        const offset = circumference - (percentage / 100) * circumference;
        circle.style.strokeDashoffset = offset;

        progressText.textContent = `${percentage.toFixed(1)}%`;
        progressText.removeAttribute('title');
    }

    getPhaseName(phaseKey) {
        const phaseNames = {
            'preflower': 'Vorblüte',
            'stretch': 'Stretch-Phase',
            'flowering_middle': 'Hauptblüte',
            'flowering_late': 'Spätblüte',
            'flush': 'Flush-Phase'
        };
        return phaseNames[phaseKey] || phaseKey;
    }

    getCategoryName(category) {
        const categoryNames = {
            'growth': 'Wachstum',
            'maturity': 'Reifung',
            'flush': 'Flush',
            'stress': 'Stress'
        };
        return categoryNames[category] || category;
    }

    getMarkerIcon(eventType) {
        const icons = {
            'stretch_completed': '📈',
            'trichome_milky': '🔬',
            'trichome_amber': '🔬',
            'pistils_retracted': '🌸',
            'flush_started': '🚿',
            'flowering_delay': '⚠️'
        };
        return icons[eventType] || '📍';
    }

    getEventMarkerClass(event) {
        const currentDay = this.floweringData?.flowering_status?.current_day || 0;
        
        if (event.day === currentDay) return 'current';
        if (event.day < currentDay) return 'past';
        return 'future';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('de-DE');
    }

    showSuccess(message) {
        // Delegiere an UIRenderer falls verfügbar
        if (this.uiRenderer) {
            return this.uiRenderer.showSuccess(message);
        }

        // Fallback: Originale Implementierung
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #10b981;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 1001;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    showError(message) {
        // Delegiere an UIRenderer falls verfügbar
        if (this.uiRenderer) {
            return this.uiRenderer.showError(message);
        }

        // Fallback: Originale Implementierung
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ff4444;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        `;

        document.body.appendChild(errorDiv);

        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    // ===== TRICHOM UPDATE FUNCTIONS =====

    updateTrichomeStatus(data) {
        // Prüfe ob die Daten die erwartete Struktur haben
        if (!data || !data.has_data) {
            this.updateTrichomeNoData();
            return;
        }

        // Status-Badge mit erweiterten Daten
        this.updateTrichomeBadge(data);

        // Letzte Aktualisierung
        const lastUpdate = this.getElementById('lastTrichomeUpdate');
        if (lastUpdate && data.latest_entry) {
            const date = new Date(data.latest_entry.date).toLocaleDateString('de-DE');
            lastUpdate.textContent = `Letzte Analyse: ${date}`;
        }

        // Verbesserte Trichom-Balken mit neuen Daten
        this.updateTrichomeSegments(data);

        // Flush-Alert mit erweiterten Daten
        this.updateFlushAlert(data);

        // Reifegrad mit erweiterten Daten
        const maturityLevel = this.getElementById('maturityLevelValue');
        if (maturityLevel) {
            maturityLevel.textContent = this.getMaturityLevelText(data.maturity_level);
            maturityLevel.className = `level-value ${data.maturity_level}`;
        }

        // Flush-Fortschritt mit neuen Daten
        this.updateFlushProgress(data.flush_progress);
        
        // Ernte-Fortschritt (neu)
        const harvestProgress = this.getElementById('harvestProgress');
        if (harvestProgress && data.harvest_progress !== undefined) {
            harvestProgress.style.width = `${data.harvest_progress}%`;
            harvestProgress.setAttribute('title', `${data.harvest_progress}% Ernte-Fortschritt`);
        }
        
        // Trend-Anzeige (neu)
        const trendElement = this.getElementById('trichomeTrend');
        if (trendElement && data.trend) {
            trendElement.textContent = data.trend;
            trendElement.className = `trend-indicator ${data.trend.replace(' ', '-')}`;
        }
        
        // Urgency-Anzeige (neu)
        const urgencyElement = this.getElementById('trichomeUrgency');
        if (urgencyElement && data.urgency) {
            urgencyElement.textContent = data.urgency;
            urgencyElement.className = `urgency-indicator ${data.urgency}`;
        }
        
        // Beobachtungslog aktualisieren
        if (data.observations) {
            this.updateObservationList(data.observations);
        }
        

    }

    updateTrichomeBadge(data) {
        const badge = this.getElementById('trichomeStatusBadge');
        const badgeIcon = this.getElementById('badgeIcon');
        const badgeText = this.getElementById('badgeText');

        if (badge && badgeIcon && badgeText) {
            // Badge-Klasse setzen
            badge.className = `trichome-status-badge ${data.maturity_level}`;
            
            // Icon basierend auf Reifegrad
            const icons = {
                'early': '🌱',
                'developing': '🌿',
                'flush_approaching': '🚰',
                'flush_ready': '🚰',
                'harvest_approaching': '✂️',
                'harvest_ready': '✂️'
            };
            badgeIcon.textContent = icons[data.maturity_level] || '🔬';
            
            // Text setzen
            badgeText.textContent = data.recommendation;
        }
    }

    updateTrichomeSegments(data) {
        // Verwende current_values aus den Daten
        const currentValues = data.current_values || {};
        
        // Klare Trichome
        const clearSegment = this.getElementById('clearSegmentBar');
        const clearValue = this.getElementById('clearValueBar');
        if (clearSegment && clearValue) {
            const clearPercentage = currentValues.clear_percentage || 0;
            clearSegment.style.width = `${clearPercentage}%`;
            clearValue.textContent = `${clearPercentage}%`;
        }

        // Milchige Trichome
        const milkySegment = this.getElementById('milkySegmentBar');
        const milkyValue = this.getElementById('milkyValueBar');
        if (milkySegment && milkyValue) {
            const milkyPercentage = currentValues.milky_percentage || 0;
            milkySegment.style.width = `${milkyPercentage}%`;
            milkyValue.textContent = `${milkyPercentage}%`;
        }

        // Bernstein Trichome
        const amberSegment = this.getElementById('amberSegmentBar');
        const amberValue = this.getElementById('amberValueBar');
        if (amberSegment && amberValue) {
            const amberPercentage = currentValues.amber_percentage || 0;
            amberSegment.style.width = `${amberPercentage}%`;
            amberValue.textContent = `${amberPercentage}%`;
        }
    }

    updateFlushAlert(data) {
        const flushAlert = this.getElementById('flushAlert');
        if (!flushAlert) return;

        // Erweiterte Flush-Alert-Logik basierend auf neuen Daten
        if (data.flush_progress >= 80) {
            flushAlert.classList.add('active', 'high');
            flushAlert.textContent = 'Flush-Fortschritt hoch - Flush starten!';
        } else if (data.flush_progress >= 60) {
            flushAlert.classList.add('active', 'medium');
            flushAlert.textContent = 'Flush-Fortschritt mittel - Überwachung erhöhen';
        } else if (data.flush_progress >= 40) {
            flushAlert.classList.add('active', 'low');
            flushAlert.textContent = 'Flush-Fortschritt niedrig - Weiter beobachten';
        } else {
            flushAlert.classList.remove('active', 'high', 'medium', 'low');
            flushAlert.textContent = '';
        }
    }

    updateFlushProgress(progress) {
        const flushProgressEl = this.getElementById('flushProgress');
        if (!flushProgressEl) return;

        flushProgressEl.style.width = `${progress}%`;
        flushProgressEl.setAttribute('title', `${progress}% Flush-Fortschritt`);
    }

    getMaturityLevelText(level) {
        const levels = {
            'early': 'Frühreif',
            'developing': 'Entwicklungsphase',
            'flush_approaching': 'Flush nähert sich',
            'flush_ready': 'Flush-Bereit',
            'harvest_approaching': 'Ernte nähert sich',
            'harvest_ready': 'Erntebereit'
        };
        return levels[level] || level;
    }

    getTrendClass(trendText) {
        if (trendText.includes('↑') || trendText.includes('zunehmend') || trendText.includes('increasing')) {
            return 'increasing';
        } else if (trendText.includes('↓') || trendText.includes('abnehmend') || trendText.includes('decreasing')) {
            return 'decreasing';
        } else {
            return 'stable';
        }
    }

    updateTrichomeRecommendation(data) {
        
        const recommendationMessage = this.getElementById('recommendationMessage');
        const actionText = this.getElementById('actionText');
        const urgencyText = this.getElementById('urgencyText');
        const nextCheckText = this.getElementById('nextCheckText');
        const tipsList = this.getElementById('tipsList');
        const trendAnalysis = this.getElementById('trendAnalysis');
        
        // Empfehlung aus dem recommendation-Objekt extrahieren
        if (data && data.recommendation) {
            const recommendation = data.recommendation;
            
            if (recommendationMessage) {
                recommendationMessage.textContent = recommendation.message || 'Empfehlung wird geladen...';
            }
            
            if (actionText) {
                actionText.textContent = recommendation.action || '--';
            }
            
            if (urgencyText) {
                urgencyText.textContent = recommendation.urgency || 'Niedrig';
            }
            
            if (nextCheckText) {
                nextCheckText.textContent = recommendation.next_check || '--';
            }
            
            if (tipsList && recommendation.tips) {
                tipsList.innerHTML = '';
                recommendation.tips.forEach(tip => {
                    const li = document.createElement('li');
                    li.textContent = tip;
                    tipsList.appendChild(li);
                });
            }
            
            if (trendAnalysis) {
                trendAnalysis.textContent = recommendation.trend_analysis || 'Keine Trend-Daten';
            }
        } else {
            // Fallback für alte Datenstruktur
            if (recommendationMessage && data) {
                recommendationMessage.textContent = data.message || data.recommendation || 'Empfehlung wird geladen...';
            }
            
            if (actionText && data) {
                actionText.textContent = data.action || '--';
            }
            
            if (urgencyText && data) {
                urgencyText.textContent = data.urgency || 'Niedrig';
            }
        }
    }

    updateTrichomeTrigger(data) {
        const triggerMessage = this.getElementById('triggerMessage');
        const triggerText = this.getElementById('triggerText');
        const triggerType = this.getElementById('triggerType');
        const triggerAction = this.getElementById('triggerAction');
        const manualFlushBtn = this.getElementById('manualFlushBtn');
        
        if (data && data.trigger_status) {
            const trigger = data.trigger_status;
            
            if (triggerMessage) {
                triggerMessage.textContent = trigger.message || 'Trigger-Status wird geladen...';
            }
            
            if (triggerText) {
                triggerText.textContent = trigger.triggered ? 'Trigger aktiv' : 'Überwachung aktiv';
            }
            
            if (triggerType) {
                triggerType.textContent = trigger.trigger_type || 'none';
                triggerType.className = `trigger-type ${trigger.trigger_type || 'none'}`;
            }
            
            if (triggerAction) {
                triggerAction.textContent = trigger.action || 'monitor';
                triggerAction.className = `trigger-action ${trigger.action || 'monitor'}`;
            }
            
            if (manualFlushBtn) {
                manualFlushBtn.disabled = !trigger.triggered;
                manualFlushBtn.className = trigger.triggered ? 'btn btn-warning' : 'btn btn-secondary disabled';
            }
        }
    }

    updateTrichomeNoData() {
        const noDataEl = this.getElementById('trichomeNoData');
        if (!noDataEl) return;

        noDataEl.classList.add('active');
    }

    updateTrichomeProgress(data) {
        
        // Beobachtungsanzahl
        const entriesCount = this.getElementById('entriesCount');
        if (entriesCount && data.observations_count !== undefined) {
            entriesCount.textContent = `${data.observations_count} Beobachtungen`;
        }
        
        // Fortschritts-Balken
        const developmentProgress = this.getElementById('developmentProgress');
        const maturityProgress = this.getElementById('maturityProgress');
        const flushProgress = this.getElementById('flushProgress');
        const overallProgress = this.getElementById('overallProgress');
        
        if (data.progress) {
            if (developmentProgress) {
                developmentProgress.style.width = `${data.progress.development_progress}%`;
                developmentProgress.setAttribute('title', `Entwicklung: ${data.progress.development_progress}%`);
            }
            
            if (maturityProgress) {
                maturityProgress.style.width = `${data.progress.maturity_progress}%`;
                maturityProgress.setAttribute('title', `Reife: ${data.progress.maturity_progress}%`);
            }
            
            if (flushProgress) {
                flushProgress.style.width = `${data.progress.flush_progress}%`;
                flushProgress.setAttribute('title', `Flush: ${data.progress.flush_progress}%`);
            }
            
            if (overallProgress) {
                overallProgress.style.width = `${data.progress.overall_progress}%`;
                overallProgress.setAttribute('title', `Gesamt: ${data.progress.overall_progress}%`);
            }
        }
        
        // Neue Spalten-Daten
        const clearTrendValue = this.getElementById('clearTrendValue');
        const milkyTrendValue = this.getElementById('milkyTrendValue');
        const amberTrendValue = this.getElementById('amberTrendValue');
        const clearTrend = this.getElementById('clearTrend');
        const milkyTrend = this.getElementById('milkyTrend');
        const amberTrend = this.getElementById('amberTrend');
        
        // Aktuelle Werte in Spalten anzeigen
        if (data.current_values) {
            if (clearTrendValue) {
                clearTrendValue.textContent = `${data.current_values.clear_percentage || 0}%`;
            }
            if (milkyTrendValue) {
                milkyTrendValue.textContent = `${data.current_values.milky_percentage || 0}%`;
            }
            if (amberTrendValue) {
                amberTrendValue.textContent = `${data.current_values.amber_percentage || 0}%`;
            }
        }
        
        // Trend-Pfeile
        if (data.trend) {
            if (clearTrend) {
                const clearTrendText = data.trend.clear_trend || '→';
                clearTrend.textContent = clearTrendText;
                clearTrend.className = `trend-arrow ${this.getTrendClass(clearTrendText)}`;
            }
            
            if (milkyTrend) {
                const milkyTrendText = data.trend.milky_trend || '→';
                milkyTrend.textContent = milkyTrendText;
                milkyTrend.className = `trend-arrow ${this.getTrendClass(milkyTrendText)}`;
            }
            
            if (amberTrend) {
                const amberTrendText = data.trend.amber_trend || '→';
                amberTrend.textContent = amberTrendText;
                amberTrend.className = `trend-arrow ${this.getTrendClass(amberTrendText)}`;
            }
        }
        
        // Phasen-Fortschritt
        if (data.phases) {
            Object.keys(data.phases).forEach(phaseKey => {
                const phase = data.phases[phaseKey];
                const phaseElement = this.getElementById(`${phaseKey}Progress`);
                if (phaseElement) {
                    phaseElement.style.width = `${phase.current_progress}%`;
                    phaseElement.setAttribute('title', `${phase.name}: ${phase.current_progress}%`);
                    phaseElement.className = `phase-progress ${phase.completed ? 'completed' : 'in-progress'}`;
                }
            });
        }
        
        // Aktuelle Werte
        const currentMilky = this.getElementById('currentMilky');
        const currentAmber = this.getElementById('currentAmber');
        const currentClear = this.getElementById('currentClear');
        
        if (data.current_values) {
            if (currentMilky) {
                currentMilky.textContent = `${data.current_values.milky_percentage}%`;
            }
            if (currentAmber) {
                currentAmber.textContent = `${data.current_values.amber_percentage}%`;
            }
            if (currentClear) {
                currentClear.textContent = `${data.current_values.clear_percentage}%`;
            }
        }
        
        // Durchschnittswerte
        const avgMilky = this.getElementById('avgMilky');
        const avgAmber = this.getElementById('avgAmber');
        const avgClear = this.getElementById('avgClear');
        
        if (data.averages) {
            if (avgMilky) {
                avgMilky.textContent = `${data.averages.milky_percentage}%`;
            }
            if (avgAmber) {
                avgAmber.textContent = `${data.averages.amber_percentage}%`;
            }
            if (avgClear) {
                avgClear.textContent = `${data.averages.clear_percentage}%`;
            }
        }
        // Status-Text im Header aktualisieren
        const statusText = this.getElementById('trichomeStatusText');
        if (statusText && data.current_values) {
            const klar = data.current_values.clear_percentage || 0;
            const milchig = data.current_values.milky_percentage || 0;
            const bernstein = data.current_values.amber_percentage || 0;
            statusText.textContent = `Klar: ${klar}%, Milchig: ${milchig}%, Bernstein: ${bernstein}%`;
        } else if (statusText) {
            statusText.textContent = "Keine Trichom-Daten vorhanden";
        }
        // Flush-Fortschritt aktualisieren
        const flushProgressText = this.getElementById('flushProgressText');
        const flushProgressCircle = this.getElementById('flushProgressCircle');
        if (flushProgressText && data.progress && data.progress.flush_progress !== undefined) {
            const percent = Math.round(data.progress.flush_progress);
            flushProgressText.textContent = `${percent}%`;
            // SVG-Kreis animieren
            if (flushProgressCircle) {
                const radius = 15;
                const circumference = 2 * Math.PI * radius;
                const offset = circumference - (percent / 100) * circumference;
                flushProgressCircle.setAttribute('stroke-dasharray', circumference);
                flushProgressCircle.setAttribute('stroke-dashoffset', offset);
            }
        }
        // ... bestehender Code ...
        this.updateTrichomeSegments(data);
    }

    // Neue Methode zum Rendern des Beobachtungslogs
    updateObservationList(observations) {
        
        const listEl = this.getElementById('observationList');
        if (!listEl) return;
        listEl.innerHTML = '';
        if (!observations || observations.length === 0) {
            listEl.innerHTML = '<div class="observation-placeholder"><span class="placeholder-text">Keine Beobachtungen verfügbar</span></div>';
            return;
        }
        observations.sort((a, b) => new Date(b.date) - new Date(a.date));
        observations.forEach((obs, index) => {
            const item = document.createElement('div');
            item.className = 'observation-item';
            item.setAttribute('data-observation-index', index);
            const date = new Date(obs.date);
            const formattedDate = date.toLocaleDateString('de-DE');
            let timeDisplay = '';
            if (obs.created_at) {
                const createdDate = new Date(obs.created_at);
                if (!isNaN(createdDate.getTime())) {
                    timeDisplay = createdDate.toLocaleTimeString('de-DE', { hour: '2-digit', minute: '2-digit' });
                }
            }
            item.innerHTML = `
                <div class="observation-header">
                    <span class="observation-date">${formattedDate}${timeDisplay ? ` ${timeDisplay}` : ''}</span>
                    <span class="observation-day">Blüte-Tag ${obs.bloom_day}</span>
                </div>
                <div class="observation-values">
                    <span class="value clear">Klar: ${obs.clear_percentage}%</span>
                    <span class="value milky">Milchig: ${obs.milky_percentage}%</span>
                    <span class="value amber">Bernstein: ${obs.amber_percentage}%</span>
                </div>
                ${obs.location ? `<div class="observation-location">Ort: ${obs.location}</div>` : ''}
                ${obs.notes ? `<div class="observation-notes">Notizen: ${obs.notes}</div>` : ''}
                <div class="observation-actions">
                    <button class="btn-edit" data-index="${index}">Bearbeiten</button>
                    <button class="btn-delete" data-index="${index}">Löschen</button>
                </div>
            `;
            const editBtn = item.querySelector('.btn-edit');
            const deleteBtn = item.querySelector('.btn-delete');
            editBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.editObservation(index);
            });
            deleteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.deleteObservation(index);
            });
            listEl.appendChild(item);
        });
        // Nach dem Rendern: Hervorhebung setzen
        if (this.currentEditIndex !== null) {
            const item = document.querySelector(`.observation-item[data-observation-index="${this.currentEditIndex}"]`);
            if (item) item.classList.add('is-editing');
        }
        if (this.currentDeleteIndex !== null) {
            const item = document.querySelector(`.observation-item[data-observation-index="${this.currentDeleteIndex}"]`);
            if (item) item.classList.add('is-deleting');
        }
    }

    // Hilfsmethoden für das zentrale Formular-Rendering
    showEditForm(index) {
        // Alle anderen Formulare schließen
        this.hideAllForms();
        
        this.currentEditIndex = index;
        this.currentDeleteIndex = null;
        this.clearObservationIndicators();
        const container = document.getElementById('observationActionFormContainer');
        if (!container) return;
        
        // Markiere den aktuellen Eintrag
        const item = document.querySelector(`.observation-item[data-observation-index="${index}"]`);
        if (item) {
            item.classList.add('is-editing');
        }
        
        // Container leeren (ohne hideActionForm zu verwenden)
        container.innerHTML = '';
        const currentObservations = this.getCurrentObservations();
        if (!currentObservations || !currentObservations[index]) return;
        const obs = currentObservations[index];
        // Formular-HTML erzeugen
        container.innerHTML = `
            <div class="edit-form">
                <div class="form-card compact">
                    <h5>✏️ Beobachtung bearbeiten</h5>
                    <form id="editObservationForm">
                        <div class="form-row compact">
                            <div class="form-group">
                                <label>Datum:</label>
                                <input type="date" name="date" value="${obs.date}" required>
                            </div>
                            <div class="form-group">
                                <label>Blüte-Tag:</label>
                                <input type="number" name="bloom_day" min="1" max="100" value="${obs.bloom_day}" required>
                            </div>
                        </div>
                        
                        <!-- Location als Radio-Buttons -->
                        <div class="form-group">
                            <label>Ort:</label>
                            <div class="radio-group">
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Top Buds" ${obs.location === 'Top Buds' ? 'checked' : ''} required>
                                    <span class="radio-label">Top Buds</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Side Buds" ${obs.location === 'Side Buds' ? 'checked' : ''}>
                                    <span class="radio-label">Side Buds</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Lower Buds" ${obs.location === 'Lower Buds' ? 'checked' : ''}>
                                    <span class="radio-label">Lower Buds</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Sugar Leaves" ${obs.location === 'Sugar Leaves' ? 'checked' : ''}>
                                    <span class="radio-label">Sugar Leaves</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Fan Leaves" ${obs.location === 'Fan Leaves' ? 'checked' : ''}>
                                    <span class="radio-label">Fan Leaves</span>
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="location" value="Mixed" ${obs.location === 'Mixed' ? 'checked' : ''}>
                                    <span class="radio-label">Mixed</span>
                                </label>
                            </div>
                        </div>
                        
                        <!-- Trichom-Werte als Schieberegler -->
                        <div class="form-group">
                            <label>Trichom-Verteilung:</label>
                            <div class="slider-group">
                                <div class="slider-item">
                                    <label for="editClearPercentage">Klar: <span id="editClearPercentageDisplay">${obs.clear_percentage}</span>%</label>
                                    <input type="range" id="editClearPercentage" name="clear_percentage" min="0" max="100" value="${obs.clear_percentage}" class="trichome-slider clear-slider">
                                </div>
                                <div class="slider-item">
                                    <label for="editMilkyPercentage">Milchig: <span id="editMilkyPercentageDisplay">${obs.milky_percentage}</span>%</label>
                                    <input type="range" id="editMilkyPercentage" name="milky_percentage" min="0" max="100" value="${obs.milky_percentage}" class="trichome-slider milky-slider">
                                </div>
                                <div class="slider-item">
                                    <label for="editAmberPercentage">Bernstein: <span id="editAmberPercentageDisplay">${obs.amber_percentage}</span>%</label>
                                    <input type="range" id="editAmberPercentage" name="amber_percentage" min="0" max="100" value="${obs.amber_percentage}" class="trichome-slider amber-slider">
                                </div>
                            </div>
                            
                            <!-- Live-Summenanzeige -->
                            <div class="percentage-summary">
                                <div class="sum-display" id="editSumDisplay">
                                    <span class="sum-label">Summe:</span>
                                    <span class="sum-value" id="editSumValue">${obs.clear_percentage + obs.milky_percentage + obs.amber_percentage}%</span>
                                </div>
                                <div class="sum-warning" id="editSumWarning" style="display: none;">
                                    <span class="warning-icon">⚠️</span>
                                    <span class="warning-text">Summe über 100%!</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Notizen:</label>
                            <textarea name="notes" rows="2">${obs.notes || ''}</textarea>
                        </div>
                        <div class="form-actions compact">
                            <button type="submit" class="btn btn-primary btn-sm">Aktualisieren</button>
                            <button type="button" class="btn btn-secondary btn-sm btn-cancel">Abbrechen</button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        // Event-Listener für das Formular
        const form = document.getElementById('editObservationForm');
        if (form) {
            form.onsubmit = (e) => {
                e.preventDefault();
                this.submitEditObservation(index, form);
            };
        }
        const cancelBtn = container.querySelector('.btn-cancel');
        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideActionForm();
        }
        
        // Schieberegler Event-Listener für das Bearbeiten-Formular
        this.setupEditSliderListeners();
    }

    showDeleteForm(index) {
        // Alle anderen Formulare schließen
        this.hideAllForms();
        
        this.currentDeleteIndex = index;
        this.currentEditIndex = null;
        this.clearObservationIndicators();
        const container = document.getElementById('observationActionFormContainer');
        if (!container) return;
        
        // Markiere den aktuellen Eintrag
        const item = document.querySelector(`.observation-item[data-observation-index="${index}"]`);
        if (item) {
            item.classList.add('is-deleting');
        }
        
        // Container leeren (ohne hideActionForm zu verwenden)
        container.innerHTML = '';
        const currentObservations = this.getCurrentObservations();
        if (!currentObservations || !currentObservations[index]) return;
        const obs = currentObservations[index];
        const date = new Date(obs.date);
        const formattedDate = date.toLocaleDateString('de-DE');
        container.innerHTML = `
            <div class="delete-form">
                <div class="form-card compact delete-confirmation">
                    <h5>🗑️ Beobachtung löschen</h5>
                    <div class="confirm-text">
                        Möchtest du die Beobachtung vom ${formattedDate} (Blüte-Tag ${obs.bloom_day}) wirklich löschen?
                    </div>
                    <div class="form-actions compact">
                        <button type="button" class="btn btn-danger btn-sm btn-delete-confirm">Löschen</button>
                        <button type="button" class="btn btn-secondary btn-sm btn-cancel">Abbrechen</button>
                    </div>
                </div>
            </div>
        `;
        const deleteBtn = container.querySelector('.btn-delete-confirm');
        if (deleteBtn) {
            deleteBtn.onclick = () => this.performDelete(index);
        }
        const cancelBtn = container.querySelector('.btn-cancel');
        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideActionForm();
        }
    }

    hideActionForm() {
        // Verwende die zentrale Methode
        this.hideAllForms();
    }

    clearObservationIndicators() {
        document.querySelectorAll('.observation-item.is-editing').forEach(el => el.classList.remove('is-editing'));
        document.querySelectorAll('.observation-item.is-deleting').forEach(el => el.classList.remove('is-deleting'));
    }

    // editObservation und deleteObservation - Delegiert an TrichomeManager
    editObservation(index) {
        // Delegiere an TrichomeManager falls verfügbar
        if (this.trichomeManager) {
            return this.trichomeManager.editObservation(index);
        }

        // Fallback: Originale Implementierung
        this.showEditForm(index);
    }

    deleteObservation(index) {
        // Delegiere an TrichomeManager falls verfügbar
        if (this.trichomeManager) {
            return this.trichomeManager.deleteObservation(index);
        }

        // Fallback: Originale Implementierung
        this.showDeleteForm(index);
    }
    
    // Fehlende performDelete-Funktion hinzufügen
    async performDelete(index) {
        
        // Hole die aktuelle Beobachtung, um die ID zu bekommen
        const observations = this.getCurrentObservations();
        if (!observations || !observations[index]) {
            this.showError('Beobachtung nicht gefunden');
            return;
        }
        
        const observation = observations[index];
        const observationId = observation.id;
        
        try {
            const response = await fetch(`/flowering/trichome-observation/${this.currentPlantId}?id=${observationId}`, {
                method: 'DELETE'
            });
            
            if (response.ok) {
                const result = await response.json();
                this.showSuccess('Beobachtung erfolgreich gelöscht');
                
                // Formular verstecken
                this.hideActionForm();
                
                // Alle Trichom-Daten vollständig neu laden
                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);
                
                // Zusätzlich: UI sofort aktualisieren, falls keine Beobachtungen mehr vorhanden sind
                const currentObservations = this.getCurrentObservations();
                if (!currentObservations || currentObservations.length === 0) {
                    this.updateObservationList([]);
                }
            } else {
                const errorData = await response.json();
                
                // Spezielle Behandlung für "nicht gefunden" Fehler
                if (errorData.error && errorData.error.includes('nicht gefunden')) {
                    this.showSuccess('Beobachtung wurde bereits gelöscht');
                    this.hideActionForm();
                    
                    // UI sofort aktualisieren
                    await Promise.all([
                        this.loadTrichomeStatus(),
                        this.loadTrichomeTrigger(),
                        this.loadTrichomeRecommendation(),
                        this.loadTrichomeProgress()
                    ]);
                } else {
                    this.showError(`Fehler beim Löschen: ${errorData.error || 'Unbekannter Fehler'}`);
                }
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Netzwerkfehler beim Löschen:', error);
            this.showError('Netzwerkfehler beim Löschen der Beobachtung');
        }
    }

    // Methode zum Bearbeiten einer Beobachtung
    async submitEditObservation(index, form) {
        // Formulardaten sammeln
        const formData = new FormData(form);
        
        const observationData = {
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            location: formData.get('location'),
            clear_percentage: parseInt(formData.get('clear_percentage')),
            milky_percentage: parseInt(formData.get('milky_percentage')),
            amber_percentage: parseInt(formData.get('amber_percentage')),
            notes: formData.get('notes') || ''
        };
        
        // Flexiblere Validierung für Trichom-Schätzungen
        const total = observationData.clear_percentage + observationData.milky_percentage + observationData.amber_percentage;
        if (total < 90 || total > 110) {
            alert(`Die Summe der Prozentwerte sollte zwischen 90-110% liegen (aktuell: ${total}%). Trichom-Analysen sind oft Schätzungen!`);
            return;
        }
        
        try {
            // Hole die aktuelle Beobachtung, um die ID zu bekommen
            const observations = this.getCurrentObservations();
            if (!observations || !observations[index]) {
                this.showError('Beobachtung nicht gefunden');
                return;
            }
            
            const observation = observations[index];
            const observationId = observation.id;
            
            // ID zur Beobachtung hinzufügen
            observationData.id = observationId;
            
            // Backend-Aufruf zum Bearbeiten
            const response = await fetch(`/flowering/trichome-observation/${this.currentPlantId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(observationData)
            });
            
            if (response.ok) {
                this.showSuccess('Beobachtung erfolgreich aktualisiert');
                
                // Alle Formulare verstecken (zentrale Methode)
                this.hideAllForms();
                
                // Alle Trichom-Daten vollständig neu laden
                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);
            } else {
                this.showError('Fehler beim Bearbeiten der Beobachtung');
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Netzwerkfehler beim Bearbeiten:', error);
            this.showError('Netzwerkfehler beim Bearbeiten der Beobachtung');
        }
    }

    // Hilfsmethode zum Abrufen der aktuellen Beobachtungen
    getCurrentObservations() {
        // Versuche die Beobachtungen aus verschiedenen möglichen Strukturen zu holen
        if (this.floweringData && this.floweringData.trichome_status && this.floweringData.trichome_status.observations) {
            return this.floweringData.trichome_status.observations;
        }
        
        // Alternative: Direkt aus floweringData.observations
        if (this.floweringData && this.floweringData.observations) {
            return this.floweringData.observations;
        }
        
        // Neue Alternative: Aus den geladenen Trichom-Daten
        if (this.trichomeData && this.trichomeData.observations) {
            return this.trichomeData.observations;
        }
        
        // Fallback: Aus der globalen Variable (falls vorhanden)
        if (typeof SAVED_TRICHOME_OBSERVATIONS !== 'undefined' && SAVED_TRICHOME_OBSERVATIONS[this.currentPlantId]) {
            return SAVED_TRICHOME_OBSERVATIONS[this.currentPlantId];
        }
        
        return [];
    }

    // Event-Listener für den "Neuer Eintrag" Button
    setupObservationForm() {
        const addBtn = document.getElementById('addObservationBtn');
        const formContainer = document.getElementById('observationFormContainer');
        const cancelBtn = document.getElementById('cancelObservationBtn');
        const form = document.getElementById('trichomeObservationForm');
        
        if (addBtn) {
            addBtn.addEventListener('click', () => {
                // Formular anzeigen und zurücksetzen
                this.showObservationForm();
            });
        }
        
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideObservationForm();
            });
        }
        
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitObservation();
            });
        }
        
        // Schieberegler Event-Listener für Live-Updates
        this.setupSliderListeners();
    }
    
    setupSliderListeners() {
        const clearSlider = document.getElementById('clearPercentage');
        const milkySlider = document.getElementById('milkyPercentage');
        const amberSlider = document.getElementById('amberPercentage');
        
        const clearDisplay = document.getElementById('clearPercentageDisplay');
        const milkyDisplay = document.getElementById('milkyPercentageDisplay');
        const amberDisplay = document.getElementById('amberPercentageDisplay');
        
        const sumValue = document.getElementById('sumValue');
        const sumWarning = document.getElementById('sumWarning');
        
        const updateSum = () => {
            const clear = parseInt(clearSlider.value) || 0;
            const milky = parseInt(milkySlider.value) || 0;
            const amber = parseInt(amberSlider.value) || 0;
            
            const total = clear + milky + amber;
            
            // Display-Werte aktualisieren
            if (clearDisplay) clearDisplay.textContent = clear;
            if (milkyDisplay) milkyDisplay.textContent = milky;
            if (amberDisplay) amberDisplay.textContent = amber;
            
            // Summe anzeigen
            if (sumValue) {
                sumValue.textContent = total + '%';
                
                // Farbe basierend auf Summe
                if (total > 100) {
                    sumValue.style.color = '#dc2626'; // Rot
                } else if (total === 100) {
                    sumValue.style.color = '#059669'; // Grün
                } else {
                    sumValue.style.color = '#6b7280'; // Grau
                }
            }
            
            // Warnung anzeigen/verstecken
            if (sumWarning) {
                if (total > 100) {
                    sumWarning.style.display = 'flex';
                } else {
                    sumWarning.style.display = 'none';
                }
            }
        };
        
        // Event-Listener für alle Schieberegler
        [clearSlider, milkySlider, amberSlider].forEach(slider => {
            if (slider) {
                slider.addEventListener('input', updateSum);
            }
        });
        
        // Initiale Summe berechnen
        updateSum();
    }
    
    setupEditSliderListeners() {
        const clearSlider = document.getElementById('editClearPercentage');
        const milkySlider = document.getElementById('editMilkyPercentage');
        const amberSlider = document.getElementById('editAmberPercentage');
        
        const clearDisplay = document.getElementById('editClearPercentageDisplay');
        const milkyDisplay = document.getElementById('editMilkyPercentageDisplay');
        const amberDisplay = document.getElementById('editAmberPercentageDisplay');
        
        const sumValue = document.getElementById('editSumValue');
        const sumWarning = document.getElementById('editSumWarning');
        
        const updateSum = () => {
            const clear = parseInt(clearSlider.value) || 0;
            const milky = parseInt(milkySlider.value) || 0;
            const amber = parseInt(amberSlider.value) || 0;
            
            const total = clear + milky + amber;
            
            // Display-Werte aktualisieren
            if (clearDisplay) clearDisplay.textContent = clear;
            if (milkyDisplay) milkyDisplay.textContent = milky;
            if (amberDisplay) amberDisplay.textContent = amber;
            
            // Summe anzeigen
            if (sumValue) {
                sumValue.textContent = total + '%';
                
                // Farbe basierend auf Summe
                if (total > 100) {
                    sumValue.style.color = '#dc2626'; // Rot
                } else if (total === 100) {
                    sumValue.style.color = '#059669'; // Grün
                } else {
                    sumValue.style.color = '#6b7280'; // Grau
                }
            }
            
            // Warnung anzeigen/verstecken
            if (sumWarning) {
                if (total > 100) {
                    sumWarning.style.display = 'flex';
                } else {
                    sumWarning.style.display = 'none';
                }
            }
        };
        
        // Event-Listener für alle Schieberegler
        [clearSlider, milkySlider, amberSlider].forEach(slider => {
            if (slider) {
                slider.addEventListener('input', updateSum);
            }
        });
        
        // Initiale Summe berechnen
        updateSum();
    }

    // Formular zurücksetzen und mit aktuellen Daten füllen
    resetObservationForm() {
        
        const form = document.getElementById('trichomeObservationForm');
        if (form) {
            form.reset();
        }
        
        // Aktuelles Datum setzen
        const dateInput = document.getElementById('observationDate');
        if (dateInput) {
            const today = new Date().toISOString().split('T')[0];
            dateInput.value = today;
        }
        
        // Aktuellen Blüte-Tag setzen
        const bloomDayInput = document.getElementById('observationBloomDay');
        if (bloomDayInput && this.floweringData) {
            bloomDayInput.value = this.floweringData.flowering_status.current_day || 41;
        }
        
        // Prozentwerte auf 0 setzen (Schieberegler)
        const clearInput = document.getElementById('clearPercentage');
        const milkyInput = document.getElementById('milkyPercentage');
        const amberInput = document.getElementById('amberPercentage');
        
        if (clearInput) clearInput.value = 0;
        if (milkyInput) milkyInput.value = 0;
        if (amberInput) amberInput.value = 0;
        
        // Display-Werte zurücksetzen
        const clearDisplay = document.getElementById('clearPercentageDisplay');
        const milkyDisplay = document.getElementById('milkyPercentageDisplay');
        const amberDisplay = document.getElementById('amberPercentageDisplay');
        
        if (clearDisplay) clearDisplay.textContent = '0';
        if (milkyDisplay) milkyDisplay.textContent = '0';
        if (amberDisplay) amberDisplay.textContent = '0';
        
        // Summe zurücksetzen
        const sumValue = document.getElementById('sumValue');
        const sumWarning = document.getElementById('sumWarning');
        
        if (sumValue) {
            sumValue.textContent = '0%';
            sumValue.style.color = '#6b7280';
        }
        if (sumWarning) {
            sumWarning.style.display = 'none';
        }
        
        // Notizen leeren
        const notesInput = document.getElementById('observationNotes');
        if (notesInput) notesInput.value = '';
    }

    // Beobachtung absenden - Delegiert an TrichomeManager
    async submitObservation() {
        // Delegiere an TrichomeManager falls verfügbar
        if (this.trichomeManager) {
            return await this.trichomeManager.submitObservation();
        }

        // Fallback: Originale Implementierung
        const formData = new FormData(document.getElementById('trichomeObservationForm'));

        const observationData = {
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            location: formData.get('location'),
            clear_percentage: parseInt(formData.get('clear_percentage')),
            milky_percentage: parseInt(formData.get('milky_percentage')),
            amber_percentage: parseInt(formData.get('amber_percentage')),
            notes: formData.get('notes') || ''
        };

        try {
            const response = await fetch(`/flowering/trichome-observation/${this.currentPlantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(observationData)
            });

            if (response.ok) {
                this.hideObservationForm();

                await Promise.all([
                    this.loadTrichomeStatus(),
                    this.loadTrichomeTrigger(),
                    this.loadTrichomeRecommendation(),
                    this.loadTrichomeProgress()
                ]);

                this.showSuccess('Beobachtung erfolgreich gespeichert!');
            } else {
                console.error('🌺 FloweringWidget: Fehler beim Speichern der Beobachtung');
                const errorData = await response.json();
                this.showError('Fehler beim Speichern: ' + (errorData.error || 'Unbekannter Fehler'));
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Netzwerkfehler beim Speichern:', error);
            this.showError('Netzwerkfehler beim Speichern der Beobachtung');
        }
    }

    // Trichom-Beobachtung über Modal speichern - Delegiert an TrichomeManager
    async saveTrichomeObservation() {
        // Delegiere an TrichomeManager falls verfügbar
        if (this.trichomeManager) {
            return await this.trichomeManager.saveTrichomeObservation();
        }

        // Fallback: Verwende submitObservation
        return await this.submitObservation();
    }

    // Zentrale Methode zum Schließen aller Formulare
    hideAllForms() {
        
        // Neues Eintrag-Formular schließen
        const newFormContainer = document.getElementById('observationFormContainer');
        if (newFormContainer) {
            newFormContainer.style.display = 'none';
        }
        
        // Bearbeiten/Löschen-Formular schließen
        const actionFormContainer = document.getElementById('observationActionFormContainer');
        if (actionFormContainer) {
            actionFormContainer.innerHTML = '';
        }
        
        // Alle Hervorhebungen entfernen
        this.clearObservationIndicators();
        
        // Indizes zurücksetzen
        this.currentEditIndex = null;
        this.currentDeleteIndex = null;
    }

    // Formular anzeigen und zurücksetzen
    showObservationForm() {
        
        // Alle anderen Formulare schließen
        this.hideAllForms();
        
        const formContainer = document.getElementById('observationFormContainer');
        if (formContainer) {
            formContainer.style.display = 'block';
        }
        
        // Formular zurücksetzen
        this.resetObservationForm();
    }

    // Formular verstecken
    hideObservationForm() {
        
        const formContainer = document.getElementById('observationFormContainer');
        if (formContainer) {
            formContainer.style.display = 'none';
        }
        
        // Formular zurücksetzen
        this.resetObservationForm();
    }

    // Hilfsmethoden für Bearbeiten-Formulare
    hideAllEditForms() {
        const editForms = document.querySelectorAll('.edit-form');
        editForms.forEach(form => {
            form.style.display = 'none';
        });
    }

    hideEditForm(index) {
        const observationItem = document.querySelector(`[data-observation-index="${index}"]`);
        if (observationItem) {
            const editForm = observationItem.querySelector('.edit-form');
            if (editForm) {
                editForm.style.display = 'none';
            }
        }
    }

    // Hilfsmethoden für Löschen-Formulare
    hideAllDeleteForms() {
        const deleteForms = document.querySelectorAll('.delete-form');
        deleteForms.forEach(form => {
            form.style.display = 'none';
        });
    }

    hideDeleteForm(index) {
        const observationItem = document.querySelector(`[data-observation-index="${index}"]`);
        if (observationItem) {
            const deleteForm = observationItem.querySelector('.delete-form');
            if (deleteForm) {
                deleteForm.style.display = 'none';
            }
        }
    }

    // Timeline Event-Formular Funktionen


    // Marker-Formular Funktionen
    setupMarkerForm() {
        const addMarkerBtn = this.getElementById('addMarkerBtn');
        const closeMarkerForm = this.getElementById('closeMarkerForm');
        const cancelMarkerForm = this.getElementById('cancelMarkerForm');
        const markerForm = this.getElementById('markerForm');
        const markerFormContainer = this.getElementById('markerFormContainer');

        if (addMarkerBtn) {
            addMarkerBtn.addEventListener('click', () => {
                this.showMarkerForm();
            });
        }

        if (closeMarkerForm) {
            closeMarkerForm.addEventListener('click', () => {
                this.hideMarkerForm();
            });
        }

        if (cancelMarkerForm) {
            cancelMarkerForm.addEventListener('click', () => {
                this.hideMarkerForm();
            });
        }

        if (markerForm) {
            markerForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveMarker();
            });
        }

        // Marker-Type Änderung behandeln
        const markerTypeSelect = this.getElementById('markerType');
        if (markerTypeSelect) {
            markerTypeSelect.addEventListener('change', () => {
                this.handleMarkerTypeChange();
            });
        }

        // Datum automatisch setzen
        this.setDefaultMarkerDate();
    }

    showMarkerForm() {
        const markerFormContainer = this.getElementById('markerFormContainer');
        if (markerFormContainer) {
            markerFormContainer.style.display = 'block';
            this.setDefaultMarkerDate();
            this.setDefaultMarkerBloomDay();
        }
    }

    hideMarkerForm() {
        const markerFormContainer = this.getElementById('markerFormContainer');
        if (markerFormContainer) {
            markerFormContainer.style.display = 'none';
            this.resetMarkerForm();
        }
    }

    setDefaultMarkerDate() {
        const markerDate = this.getElementById('markerDate');
        if (markerDate) {
            const today = new Date().toISOString().split('T')[0];
            markerDate.value = today;
        }
    }

    setDefaultMarkerBloomDay() {
        const markerBloomDay = this.getElementById('markerBloomDay');
        if (markerBloomDay && this.floweringData?.flowering_status?.current_day) {
            markerBloomDay.value = this.floweringData.flowering_status.current_day;
        }
    }

    handleMarkerTypeChange() {
        const markerTypeSelect = this.getElementById('markerType');
        const markerTitle = this.getElementById('markerTitle');
        const markerCategory = this.getElementById('markerCategory');
        const markerImportance = this.getElementById('markerImportance');

        if (!markerTypeSelect || !markerTitle) return;

        const selectedType = markerTypeSelect.value;
        
        // Automatische Vorschläge basierend auf Event-Typ
        const suggestions = {
            'stretch_end': {
                title: 'Stretch-Phase beendet',
                category: 'growth',
                importance: 'medium'
            },
            'trichome_milky': {
                title: 'Milchige Trichome beobachtet',
                category: 'maturity',
                importance: 'high'
            },
            'trichome_amber': {
                title: 'Bernstein Trichome beobachtet',
                category: 'maturity',
                importance: 'high'
            },
            'flush_start': {
                title: 'Flush gestartet',
                category: 'flush',
                importance: 'high'
            },
            'harvest_window': {
                title: 'Ernte-Fenster erreicht',
                category: 'harvest',
                importance: 'high'
            },
            'pistils_retracted': {
                title: 'Pistillen zurückgezogen',
                category: 'maturity',
                importance: 'medium'
            }
        };

        if (suggestions[selectedType]) {
            const suggestion = suggestions[selectedType];
            markerTitle.value = suggestion.title;
            if (markerCategory) markerCategory.value = suggestion.category;
            if (markerImportance) markerImportance.value = suggestion.importance;
        } else {
            markerTitle.value = '';
            if (markerCategory) markerCategory.value = '';
            if (markerImportance) markerImportance.value = '';
        }
    }

    resetMarkerForm() {
        const markerForm = this.getElementById('markerForm');
        if (markerForm) {
            markerForm.reset();
        }
    }

    async saveMarker() {
        // Delegiere an TimelineManager falls verfügbar
        if (this.timelineManager) {
            return await this.timelineManager.saveMarker();
        }

        // Fallback: Originale Implementierung
        const markerForm = this.getElementById('markerForm');
        if (!markerForm) return;

        const formData = new FormData(markerForm);
        const markerData = {
            plant_id: this.currentPlantId,
            date: formData.get('date'),
            bloom_day: parseInt(formData.get('bloom_day')),
            event_type: formData.get('event_type'),
            event_name: formData.get('event_name'),
            category: formData.get('category'),
            importance: formData.get('importance'),
            notes: formData.get('notes')
        };

        try {
            const response = await fetch(`/flowering/marker/${this.currentPlantId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(markerData)
            });

            if (response.ok) {
                const result = await response.json();
                this.showSuccess('Marker erfolgreich gespeichert!');
                this.hideMarkerForm();

                // Daten neu laden
                await this.loadMarkers();
                this.updateMarkersList();
            } else {
                const error = await response.json();
                console.error('🌺 FloweringWidget: Fehler beim Speichern:', error);
                this.showError(`Fehler beim Speichern: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Speichern des Markers:', error);
            this.showError('Fehler beim Speichern des Markers');
        }
    }

    // Inline Marker-Bearbeitung und -Löschung
    showEditMarkerForm(markerId) {
        // Alle anderen Formulare schließen
        this.hideAllMarkerForms();
        
        const editForm = document.getElementById(`editMarkerForm${markerId}`);
        if (editForm) {
            editForm.style.display = 'block';
        }
    }

    hideEditMarkerForm(markerId) {
        const editForm = document.getElementById(`editMarkerForm${markerId}`);
        if (editForm) {
            editForm.style.display = 'none';
        }
    }

    showDeleteMarkerForm(markerId) {
        // Alle anderen Formulare schließen
        this.hideAllMarkerForms();
        
        const deleteForm = document.getElementById(`deleteMarkerForm${markerId}`);
        if (deleteForm) {
            deleteForm.style.display = 'block';
        }
    }

    hideDeleteMarkerForm(markerId) {
        const deleteForm = document.getElementById(`deleteMarkerForm${markerId}`);
        if (deleteForm) {
            deleteForm.style.display = 'none';
        }
    }

    hideAllMarkerForms() {
        // Alle Bearbeitungs- und Löschformulare schließen
        const editForms = document.querySelectorAll('.marker-edit-form');
        const deleteForms = document.querySelectorAll('.marker-delete-form');
        
        editForms.forEach(form => {
            form.style.display = 'none';
        });
        
        deleteForms.forEach(form => {
            form.style.display = 'none';
        });
    }

    async submitEditMarker(event, markerId) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        
        // Finde den ursprünglichen Marker, um event_type zu beibehalten
        const originalMarker = this.markers.find(m => m.id == markerId);
        if (!originalMarker) {
            this.showError('Marker nicht gefunden');
            return;
        }
        
        const updateData = {
            event_type: originalMarker.event_type, // event_type beibehalten
            bloom_day: originalMarker.bloom_day, // bloom_day beibehalten
            event_name: formData.get('event_name'),
            category: formData.get('category'),
            importance: formData.get('importance'),
            notes: formData.get('notes')
        };

        try {
    
            
            const response = await fetch(`/flowering/marker/${this.currentPlantId}/${markerId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updateData)
            });

            if (response.ok) {
                const result = await response.json();
    
                
                this.showSuccess('Marker erfolgreich bearbeitet!');
                this.hideEditMarkerForm(markerId);
                
                // Daten neu laden
                await this.loadMarkers();
                this.updateMarkersList();
            } else {
                const error = await response.json();
                console.error('🌺 FloweringWidget: Fehler beim Bearbeiten:', error);
                this.showError(`Fehler beim Bearbeiten: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Bearbeiten des Markers:', error);
            this.showError('Fehler beim Bearbeiten des Markers');
        }
    }

    async submitDeleteMarker(markerId) {
        try {
    
            
            const response = await fetch(`/flowering/marker/${this.currentPlantId}/${markerId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                const result = await response.json();
    
                
                this.showSuccess('Marker erfolgreich gelöscht!');
                this.hideDeleteMarkerForm(markerId);
                
                // Daten neu laden
                await this.loadMarkers();
                this.updateMarkersList();
            } else {
                const error = await response.json();
                console.error('🌺 FloweringWidget: Fehler beim Löschen:', error);
                this.showError(`Fehler beim Löschen: ${error.error || 'Unbekannter Fehler'}`);
            }
        } catch (error) {
            console.error('🌺 FloweringWidget: Fehler beim Löschen des Markers:', error);
            this.showError('Fehler beim Löschen des Markers');
        }
    }

    setupMarkerActionListeners() {
        // Event-Listener für Bearbeiten-Buttons
        document.querySelectorAll('.marker-btn.edit').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.showEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Löschen-Buttons
        document.querySelectorAll('.marker-btn.delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.showDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Close-Buttons in Edit-Formularen
        document.querySelectorAll('.marker-edit-form .btn-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Close-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Abbrechen-Buttons in Edit-Formularen
        document.querySelectorAll('.marker-edit-form .btn-secondary').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideEditMarkerForm(markerId);
            });
        });

        // Event-Listener für Abbrechen-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-secondary').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.hideDeleteMarkerForm(markerId);
            });
        });

        // Event-Listener für Löschen-Buttons in Delete-Formularen
        document.querySelectorAll('.marker-delete-form .btn-danger').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const markerId = e.target.getAttribute('data-marker-id');
                this.submitDeleteMarker(markerId);
            });
        });

        // Event-Listener für Edit-Formulare
        document.querySelectorAll('.marker-edit-form-content').forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                const markerId = e.target.getAttribute('data-marker-id');
                this.submitEditMarker(e, markerId);
            });
        });
    }

    /**
     * Beleuchtungs-Daten laden
     */
    async loadLightingData() {
        // Delegiere an DataManager falls verfügbar
        if (this.dataManager) {
            try {
                const currentPhase = this.floweringData?.flowering_status?.phase || 'flowering_middle';
                const strainType = this.floweringData?.strain_profile?.strain_type || 'photoperiodic';

                const lightingData = await this.dataManager.loadLightingData(currentPhase, strainType);

                if (currentPhase === 'flush') {
                    this.checkSmartDimming(lightingData);
                }

                this.renderLightingOverview(lightingData);
                this.renderLightingSettings(lightingData);
                this.renderLightingGuidelines(lightingData);
                this.updateLightingStatus(lightingData);

                this.lightingData = lightingData;
                this.setupEnergyTracking();

                if (!this.lightingDataSent) {
                    this.lightingDataSent = true;
                    this.sendLightingDataToManager();
                }
            } catch (error) {
                console.error('🌸 Blüte-Widget: Fehler beim Laden der Beleuchtungsdaten:', error);
                this.showError('Beleuchtungsdaten konnten nicht geladen werden: ' + error.message);
            }
            return;
        }

        // Fallback: Originale Implementierung
        try {
            const currentPhase = this.floweringData?.flowering_status?.phase || 'flowering_middle';
            const strainType = this.floweringData?.strain_profile?.strain_type || 'photoperiodic';

            const response = await fetch(`/api/lighting/plan/${currentPhase}?strain_type=${strainType}&plant_id=${this.currentPlantId}`);

            if (!response.ok) {
                throw new Error('Beleuchtungsdaten konnten nicht geladen werden');
            }

            const lightingData = await response.json();

            if (currentPhase === 'flush') {
                this.checkSmartDimming(lightingData);
            }

            this.renderLightingOverview(lightingData);
            this.renderLightingSettings(lightingData);
            this.renderLightingGuidelines(lightingData);
            this.updateLightingStatus(lightingData);

            this.lightingData = lightingData;
            this.setupEnergyTracking();

            if (!this.lightingDataSent) {
                this.lightingDataSent = true;
                this.sendLightingDataToManager();
            }

        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Laden der Beleuchtungsdaten:', error);
            this.showError('Beleuchtungsdaten konnten nicht geladen werden: ' + error.message);
        }
    }

    /**
     * Beleuchtungs-Übersicht rendern
     */
    renderLightingOverview(data) {
        const container = this.getElementById('lightingOverview');
        if (!container) return;
        
        const rec = data.recommendations;
        const current = data.current;
        const strainType = this.floweringData?.strain_profile?.strain_type || 'photoperiodic';
        
        // PPFD-Status berechnen
        let ppfdStatus = 'optimal';
        let ppfdMessage = 'Perfekte Lichtbedingungen!';
        
        if (current.ppfd_calculated) {
            const ppfdRange = rec.ppfd_range;
            const rangeMatch = ppfdRange.match(/(\d+)-(\d+)/);
            if (rangeMatch) {
                const minPPFD = parseInt(rangeMatch[1]);
                const maxPPFD = parseInt(rangeMatch[2]);
                
                if (current.ppfd_calculated < minPPFD) {
                    ppfdStatus = 'low';
                    ppfdMessage = 'PPFD zu niedrig - Lampenleistung erhöhen';
                } else if (current.ppfd_calculated > maxPPFD) {
                    ppfdStatus = 'high';
                    ppfdMessage = 'PPFD zu hoch - Lampenleistung reduzieren';
                }
            }
        }
        
        // DLI berechnen (PPFD * Stunden * 3600 / 1000000)
        const dliCalculated = current.ppfd_calculated && current.light_hours 
            ? ((current.ppfd_calculated * current.light_hours * 3600) / 1000000).toFixed(1)
            : '--';
        
        // Photoperiode für Autoflowers anpassen
        const photoperiodLabel = strainType === 'autoflowering' ? 'Beleuchtungsstunden' : 'Photoperiode';
        const photoperiodValue = current.light_hours || '--';
        const photoperiodRecommended = strainType === 'autoflowering' ? '18-20h (Auto)' : `${rec.light_hours}h`;
        
        container.innerHTML = `
            <div class="lighting-overview-grid">
                <div class="lighting-card ppfd-card ${ppfdStatus}">
                    <div class="lighting-card-header">
                        <i class="fa-solid fa-lightbulb"></i>
                        <span>PPFD-Status</span>
                    </div>
                    <div class="lighting-card-content">
                        <div class="lighting-value">${current.ppfd_calculated || '--'} μmol/m²/s</div>
                        <div class="lighting-range">Ziel: ${rec.ppfd_range}</div>
                        <div class="lighting-message">${ppfdMessage}</div>
                    </div>
                </div>
                
                <div class="lighting-card dli-card">
                    <div class="lighting-card-header">
                        <i class="fa-solid fa-chart-line"></i>
                        <span>DLI (Daily Light Integral)</span>
                    </div>
                    <div class="lighting-card-content">
                        <div class="lighting-value">${dliCalculated} mol/m²/Tag</div>
                        <div class="lighting-range">Ziel: ${rec.dli_range}</div>
                    </div>
                </div>
                
                <div class="lighting-card photoperiod-card">
                    <div class="lighting-card-header">
                        <i class="fa-solid fa-clock"></i>
                        <span>${photoperiodLabel}</span>
                    </div>
                    <div class="lighting-card-content">
                        <div class="lighting-value">${photoperiodValue}h</div>
                        <div class="lighting-range">Empfohlen: ${photoperiodRecommended}</div>
                    </div>
                </div>
                
                <div class="lighting-card distance-card">
                    <div class="lighting-card-header">
                        <i class="fa-solid fa-arrows-alt-v"></i>
                        <span>Lampenabstand</span>
                    </div>
                    <div class="lighting-card-content">
                        <div class="lighting-value">${current.lamp_distance_cm}cm</div>
                        <div class="lighting-range">Empfohlen: ${rec.lamp_distance_cm}cm</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Beleuchtungs-Einstellungen rendern
     */
    renderLightingSettings(data) {
        const container = this.getElementById('lightingSettings');
        if (!container) return;
        
        const current = data.current;
        
        container.innerHTML = `
            <div class="lighting-settings-section">
                <h4><i class="fa-solid fa-cog me-2"></i>Beleuchtungs-Einstellungen</h4>
                
                <div class="lighting-settings-grid">
                    <div class="lighting-setting-group">
                        <label for="flowering-lamp-power">Lampenleistung (W)</label>
                        <input type="number" id="flowering-lamp-power" value="${current.lamp_power_w}" min="1" step="1">
                    </div>
                    
                    <div class="lighting-setting-group">
                        <label for="flowering-lamp-distance">Lampenabstand (cm)</label>
                        <input type="number" id="flowering-lamp-distance" value="${current.lamp_distance_cm}" min="5" step="1">
                    </div>
                    
                    <div class="lighting-setting-group">
                        <label for="flowering-light-hours">Beleuchtungsstunden</label>
                        <input type="number" id="flowering-light-hours" value="${current.light_hours}" min="1" max="24" step="1">
                    </div>
                    
                    <div class="lighting-setting-group">
                        <label for="flowering-color-temp">Farbtemperatur (K)</label>
                        <input type="number" id="flowering-color-temp" value="${current.color_temperature_k || 3500}" min="2000" max="10000" step="100">
                        <small class="lighting-input-hint">
                            <i class="fa fa-info-circle"></i> z.B. 3500K für warmes Licht
                        </small>
                    </div>
                    
                    <div class="lighting-setting-group">
                        <label for="flowering-power-percent">Leistung (%)</label>
                        <input type="number" id="flowering-power-percent" value="${current.power_percentage || 100}" min="1" max="100" step="1">
                        <small class="lighting-input-hint">
                            <i class="fa fa-info-circle"></i> Aktuelle Dimmereinstellung
                        </small>
                    </div>
                    
                    <div class="lighting-setting-group">
                        <label for="flowering-ppfd-measured">Gemessener PPFD (optional)</label>
                        <input type="number" id="flowering-ppfd-measured" value="${current.ppfd_measured || ''}" min="0" step="1" placeholder="PPFD in μmol/m²/s">
                        <small class="lighting-input-hint">
                            <i class="fa fa-info-circle"></i> PAR-Meter Messung (optional)
                        </small>
                    </div>
                </div>
                
                <div class="lighting-setting-group full-width">
                    <label for="flowering-wavelengths">Zusatz-Wellenlängen (optional)</label>
                    <input type="text" id="flowering-wavelengths" value="${current.additional_wavelengths || ''}" placeholder="z.B. 660nm HyperRed, 730nm FarRed">
                    <small class="lighting-input-hint">
                        <i class="fa fa-info-circle"></i> Spezielle LEDs wie HyperRed, FarRed etc.
                    </small>
                </div>
                
                <div class="lighting-actions">
                    <button type="button" class="btn btn-primary" id="updateLightingBtn">
                        <i class="fa-solid fa-save"></i> Einstellungen speichern
                    </button>
                    <button type="button" class="btn btn-outline-info" id="lightingGuidelinesBtn">
                        <i class="fa-solid fa-book"></i> Richtlinien anzeigen
                    </button>
                </div>
            </div>
        `;
        
        // Event-Listener für Beleuchtungs-Einstellungen
        this.setupLightingEventListeners();
    }
    
    /**
     * Beleuchtungs-Guidelines rendern
     */
    renderLightingGuidelines(data) {
        const container = this.getElementById('lightingGuidelines');
        if (!container) return;
        
        const rec = data.recommendations;
        const strainType = this.floweringData?.strain_profile?.strain_type || 'photoperiodic';
        
        // Photoperiode für Autoflowers anpassen
        const photoperiodValue = strainType === 'autoflowering' ? '18-20h' : `${rec.light_hours}h`;
        const photoperiodType = strainType === 'autoflowering' ? 'Autoflower' : 'Photoperiod';
        
        container.innerHTML = `
            <div class="lighting-guidelines-section">
                <h4><i class="fa-solid fa-book me-2"></i>Blüte-Beleuchtungsrichtlinien</h4>
                
                <div class="lighting-guidelines-content">
                    <div class="guideline-card">
                        <h5>🌺 Blütephase-spezifische Empfehlungen</h5>
                        <div class="guideline-item">
                            <strong>PPFD-Zielbereich:</strong> ${rec.ppfd_range}
                        </div>
                        <div class="guideline-item">
                            <strong>DLI-Zielbereich:</strong> ${rec.dli_range}
                        </div>
                        <div class="guideline-item">
                            <strong>Beleuchtungsstunden:</strong> ${photoperiodValue} (${photoperiodType})
                        </div>
                        <div class="guideline-item">
                            <strong>Lampenabstand:</strong> ${rec.lamp_distance_cm}cm
                        </div>
                    </div>
                    
                    <div class="guideline-card">
                        <h5>💡 Optimierungstipps</h5>
                        <ul class="guideline-tips">
                            <li>Reduziere PPFD in der Spätblüte um 10-15% für bessere Trichom-Entwicklung</li>
                            <li>Farbtemperatur auf 2700-3000K für optimale Blütenbildung</li>
                            <li>Zusatz-LEDs (660nm, 730nm) für verbesserte Blütenqualität</li>
                            <li>Lampenabstand regelmäßig anpassen bei Pflanzenwachstum</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }
    
    /**
     * Beleuchtungs-Status aktualisieren
     */
    updateLightingStatus(data) {
        const statusIndicator = this.getElementById('lightingStatusIndicator');
        const statusText = this.getElementById('lightingStatusText');
        
        if (!statusIndicator || !statusText) return;
        
        // Status basierend auf PPFD-Bewertung
        const current = data.current;
        let status = 'optimal';
        let message = 'Beleuchtung optimal eingestellt';
        
        if (current.ppfd_calculated) {
            const rec = data.recommendations;
            const rangeMatch = rec.ppfd_range.match(/(\d+)-(\d+)/);
            if (rangeMatch) {
                const minPPFD = parseInt(rangeMatch[1]);
                const maxPPFD = parseInt(rangeMatch[2]);
                
                if (current.ppfd_calculated < minPPFD) {
                    status = 'warning';
                    message = 'PPFD zu niedrig - Anpassung empfohlen';
                } else if (current.ppfd_calculated > maxPPFD) {
                    status = 'error';
                    message = 'PPFD zu hoch - Reduzierung erforderlich';
                }
            }
        }
        
        statusIndicator.className = `status-indicator ${status}`;
        statusText.textContent = message;
    }
    
    /**
     * Event-Listener für Beleuchtungs-Einstellungen
     */
    setupLightingEventListeners() {
        const updateBtn = this.getElementById('updateLightingBtn');
        const guidelinesBtn = this.getElementById('lightingGuidelinesBtn');
        
        if (updateBtn) {
            updateBtn.addEventListener('click', () => {
                this.updateLightingSettings();
            });
        }
        
        if (guidelinesBtn) {
            guidelinesBtn.addEventListener('click', () => {
                this.showLightingGuidelinesModal();
            });
        }
    }
    
    /**
     * Beleuchtungs-Einstellungen aktualisieren
     */
    async updateLightingSettings() {
        try {
            const lampPower = parseInt(this.getElementById('flowering-lamp-power').value);
            const lampDistance = parseInt(this.getElementById('flowering-lamp-distance').value);
            const lightHours = parseInt(this.getElementById('flowering-light-hours').value);
            const colorTemp = parseInt(this.getElementById('flowering-color-temp').value);
            const powerPercent = parseInt(this.getElementById('flowering-power-percent').value);
            const ppfdMeasured = parseFloat(this.getElementById('flowering-ppfd-measured').value) || null;
            const wavelengths = this.getElementById('flowering-wavelengths').value;
            
            // Hier würde die API-Aktualisierung erfolgen
            // Für jetzt nur Reload der Daten
            await this.loadLightingData();
            
            this.showSuccess('Beleuchtungs-Einstellungen aktualisiert');
            
        } catch (error) {
            console.error('Fehler beim Aktualisieren der Beleuchtungs-Einstellungen:', error);
            this.showError('Fehler beim Aktualisieren der Einstellungen');
        }
    }
    
    /**
     * Beleuchtungs-Guidelines Modal anzeigen
     */
    showLightingGuidelinesModal() {
        // Phase-spezifische Guidelines laden
        this.loadPhaseSpecificGuidelines();
        
        // Modal öffnen
        const modal = new bootstrap.Modal(document.getElementById('lightingGuidelinesModal'));
        modal.show();
        
        // Event-Listener für Print-Button
        this.setupGuidelinesModalEvents();
    }
    
    /**
     * Phase-spezifische Guidelines laden
     */
    loadPhaseSpecificGuidelines() {
        const container = document.getElementById('phaseGuidelinesContent');
        if (!container) return;
        
        const currentPhase = this.floweringData?.flowering_status?.phase || 'flowering_middle';
        const strainType = this.floweringData?.strain_profile?.strain_type || 'photoperiodic';
        
        const phaseGuidelines = this.getPhaseSpecificGuidelines(currentPhase, strainType);
        
        container.innerHTML = `
            <div class="phase-guidelines-content">
                <div class="current-phase-info">
                    <h5>Aktuelle Phase: ${this.getPhaseDisplayName(currentPhase)}</h5>
                    <p class="phase-description">${phaseGuidelines.description}</p>
                </div>
                
                <div class="guidelines-grid">
                    <div class="guideline-card">
                        <h6>💡 PPFD-Empfehlungen</h6>
                        <div class="guideline-values">
                            <div class="value-item">
                                <span class="label">Zielbereich:</span>
                                <span class="value">${phaseGuidelines.ppfd_range}</span>
                            </div>
                            <div class="value-item">
                                <span class="label">Optimal:</span>
                                <span class="value">${phaseGuidelines.ppfd_optimal}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="guideline-card">
                        <h6>📊 DLI-Empfehlungen</h6>
                        <div class="guideline-values">
                            <div class="value-item">
                                <span class="label">Zielbereich:</span>
                                <span class="value">${phaseGuidelines.dli_range}</span>
                            </div>
                            <div class="value-item">
                                <span class="label">Berechnung:</span>
                                <span class="value">${phaseGuidelines.dli_calculation}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="guideline-card">
                        <h6>⏰ Photoperiode</h6>
                        <div class="guideline-values">
                            <div class="value-item">
                                <span class="label">Empfohlen:</span>
                                <span class="value">${phaseGuidelines.photoperiod}</span>
                            </div>
                            <div class="value-item">
                                <span class="label">Strain-Typ:</span>
                                <span class="value">${strainType === 'autoflowering' ? 'Autoflower' : 'Photoperiod'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="guideline-card">
                        <h6>🌈 Spektrum-Fokus</h6>
                        <div class="guideline-values">
                            <div class="value-item">
                                <span class="label">Primär:</span>
                                <span class="value">${phaseGuidelines.spectrum_primary}</span>
                            </div>
                            <div class="value-item">
                                <span class="label">Sekundär:</span>
                                <span class="value">${phaseGuidelines.spectrum_secondary}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="phase-tips">
                    <h6>💡 Phase-spezifische Tipps</h6>
                    <ul class="tips-list">
                        ${phaseGuidelines.tips.map(tip => `<li>${tip}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;
    }
    
    /**
     * Phase-spezifische Guidelines abrufen
     */
    getPhaseSpecificGuidelines(phase, strainType) {
        const guidelines = {
            'vegetative_early': {
                description: 'Frühe Vegetationsphase - Fokus auf Wurzel- und Blattentwicklung',
                ppfd_range: '250-400 μmol/m²/s',
                ppfd_optimal: '350 μmol/m²/s',
                dli_range: '12-20 mol/m²/Tag',
                dli_calculation: '350 × 18h × 0.0036 = 22.7 mol/m²/Tag',
                photoperiod: '18h',
                spectrum_primary: 'Blau-Licht (30%)',
                spectrum_secondary: 'Rot-Licht (50%)',
                tips: [
                    'Langsam mit PPFD beginnen (250 μmol/m²/s)',
                    'Höhere Blau-Anteile für kompaktes Wachstum',
                    'Lampenabstand: 60-80cm',
                    'Vorsichtig mit UV-Licht beginnen (2-3%)'
                ]
            },
            'vegetative_middle': {
                description: 'Mittlere Vegetationsphase - Strukturelles Wachstum und Verzweigung',
                ppfd_range: '400-600 μmol/m²/s',
                ppfd_optimal: '500 μmol/m²/s',
                dli_range: '20-30 mol/m²/Tag',
                dli_calculation: '500 × 18h × 0.0036 = 32.4 mol/m²/Tag',
                photoperiod: '18h',
                spectrum_primary: 'Blau-Licht (25%)',
                spectrum_secondary: 'Rot-Licht (55%)',
                tips: [
                    'PPFD schrittweise erhöhen',
                    'Lampenabstand an Pflanzenwachstum anpassen',
                    'Far-Red für Stretch-Kontrolle (5-8%)',
                    'UV-Licht auf 3-5% erhöhen'
                ]
            },
            'vegetative_late': {
                description: 'Späte Vegetationsphase - Vorbereitung auf Blüte',
                ppfd_range: '500-700 μmol/m²/s',
                ppfd_optimal: '600 μmol/m²/s',
                dli_range: '25-35 mol/m²/Tag',
                dli_calculation: '600 × 18h × 0.0036 = 38.9 mol/m²/Tag',
                photoperiod: '18h',
                spectrum_primary: 'Rot-Licht (60%)',
                spectrum_secondary: 'Blau-Licht (20%)',
                tips: [
                    'Rot-Licht-Anteil erhöhen für Blüteninduktion',
                    'PPFD auf Blüte-Niveau vorbereiten',
                    'Far-Red für Photomorphogenese (8-10%)',
                    'Photoperiode für Blüte vorbereiten'
                ]
            },
            'flowering_early': {
                description: 'Frühe Blütephase - Stretchphase und Blüteninduktion',
                ppfd_range: '600-800 μmol/m²/s',
                ppfd_optimal: '700 μmol/m²/s',
                dli_range: '30-40 mol/m²/Tag',
                dli_calculation: '700 × 12h × 0.0036 = 30.2 mol/m²/Tag',
                photoperiod: '12h',
                spectrum_primary: 'Rot-Licht (65%)',
                spectrum_secondary: 'Far-Red (12%)',
                tips: [
                    'Photoperiode auf 12h umstellen',
                    'PPFD schrittweise auf Blüte-Niveau erhöhen',
                    'Far-Red für bessere Blüteninduktion',
                    'UV-Licht auf 5-7% erhöhen'
                ]
            },
            'flowering_middle': {
                description: 'Mittlere Blütephase - Volle Blüte und höchste Aufnahme',
                ppfd_range: '700-900 μmol/m²/s',
                ppfd_optimal: '800 μmol/m²/s',
                dli_range: '35-45 mol/m²/Tag',
                dli_calculation: '800 × 12h × 0.0036 = 34.6 mol/m²/Tag',
                photoperiod: '12h',
                spectrum_primary: 'Rot-Licht (70%)',
                spectrum_secondary: 'Far-Red (15%)',
                tips: [
                    'Maximale PPFD für Blütenbildung',
                    'Far-Red für bessere Blütenqualität',
                    'UV-Licht auf 7-10% erhöhen',
                    'Pflanzenreaktionen genau beobachten'
                ]
            },
            'flowering_late': {
                description: 'Späte Blütephase - Reifephase und Trichom-Entwicklung',
                ppfd_range: '600-800 μmol/m²/s',
                ppfd_optimal: '700 μmol/m²/s',
                dli_range: '30-40 mol/m²/Tag',
                dli_calculation: '700 × 12h × 0.0036 = 30.2 mol/m²/Tag',
                photoperiod: '12h',
                spectrum_primary: 'Rot-Licht (65%)',
                spectrum_secondary: 'UV-Licht (10%)',
                tips: [
                    'PPFD leicht reduzieren für Trichom-Entwicklung',
                    'UV-Licht für Sekundärmetaboliten',
                    'Far-Red für Blütenqualität beibehalten',
                    'Flush vorbereiten'
                ]
            },
            'flush': {
                description: 'Flush-Phase - Schonende Trichom-Entwicklung und Ernte-Vorbereitung',
                ppfd_range: '450-750 μmol/m²/s',
                ppfd_optimal: '600 μmol/m²/s',
                dli_range: '22-35 mol/m²/Tag',
                dli_calculation: '600 × 10h × 0.0036 = 21.6 mol/m²/Tag',
                photoperiod: '10h',
                spectrum_primary: 'Rot-Licht (60%)',
                spectrum_secondary: 'UV-Licht (8%)',
                tips: [
                    'PPFD um 20-30% reduzieren',
                    'Beleuchtungsstunden auf 10h reduzieren',
                    'Wärmere Farbtemperatur für Trichome',
                    'Langsame, schonende Anpassung über 3-5 Tage'
                ]
            }
        };
        
        return guidelines[phase] || guidelines['flowering_middle'];
    }
    
    /**
     * Guidelines Modal Event-Listener einrichten
     */
    setupGuidelinesModalEvents() {
        const printBtn = document.getElementById('printGuidelinesBtn');
        if (printBtn) {
            printBtn.addEventListener('click', () => {
                this.printGuidelines();
            });
        }
    }
    
    /**
     * Guidelines drucken
     */
    printGuidelines() {
        const modalContent = document.querySelector('#lightingGuidelinesModal .modal-body');
        if (modalContent) {
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>Beleuchtungs-Richtlinien</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            .guidelines-section { margin-bottom: 30px; }
                            .guideline-card { border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 8px; }
                            .guideline-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                            .guideline-table .table-header, .guideline-table .table-row { display: flex; }
                            .guideline-table span { flex: 1; padding: 8px; border: 1px solid #ddd; }
                            .guideline-table .table-header { background: #f8f9fa; font-weight: bold; }
                            .tips-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }
                            .tip-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
                            .tip-list { list-style: none; padding: 0; }
                            .tip-list li { padding: 5px 0; padding-left: 20px; position: relative; }
                            .tip-list li:before { content: "•"; position: absolute; left: 0; }
                            @media print { body { margin: 0; } }
                        </style>
                    </head>
                    <body>
                        <h1>🌱 Vollständige Beleuchtungs-Richtlinien</h1>
                        ${modalContent.innerHTML}
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }
    }

    /**
     * Smart Dimming für Flush-Phase prüfen
     */
    checkSmartDimming(lightingData) {
        const current = lightingData.current;
        const recommendations = lightingData.recommendations;
        
        // Prüfen ob Smart Dimming aktiviert ist
        const smartDimmingEnabled = localStorage.getItem(`smart_dimming_${this.currentPlantId}`) === 'true';
        
        if (smartDimmingEnabled) {
            // Automatische Beleuchtungsanpassung für Flush
            const flushAdjustments = this.calculateFlushAdjustments(current, recommendations);
            this.applyFlushAdjustments(flushAdjustments);
            
            // Smart Dimming Status anzeigen
            this.showSmartDimmingStatus(flushAdjustments);
        } else {
            // Smart Dimming Empfehlung anzeigen
            this.showSmartDimmingRecommendation();
        }
    }
    
    /**
     * Flush-Anpassungen berechnen
     */
    calculateFlushAdjustments(current, recommendations) {
        const adjustments = {
            ppfd_reduction: 0,
            light_hours_reduction: 0,
            color_temp_adjustment: 0,
            recommendations: []
        };
        
        // PPFD-Reduktion für Flush (20-30% weniger Licht)
        const currentPPFD = current.ppfd_calculated || 800;
        const flushPPFD = Math.round(currentPPFD * 0.75); // 25% Reduktion
        adjustments.ppfd_reduction = currentPPFD - flushPPFD;
        
        // Beleuchtungsstunden reduzieren (12h → 10h)
        const currentHours = current.light_hours || 12;
        const flushHours = Math.max(10, currentHours - 2);
        adjustments.light_hours_reduction = currentHours - flushHours;
        
        // Farbtemperatur anpassen (wärmer für Trichom-Entwicklung)
        const currentTemp = current.color_temperature_k || 3000;
        const flushTemp = Math.max(2700, currentTemp - 300);
        adjustments.color_temp_adjustment = currentTemp - flushTemp;
        
        // Empfehlungen generieren
        adjustments.recommendations = [
            `PPFD auf ${flushPPFD} μmol/m²/s reduzieren (${adjustments.ppfd_reduction} weniger)`,
            `Beleuchtungsstunden auf ${flushHours}h reduzieren`,
            `Farbtemperatur auf ${flushTemp}K anpassen für optimale Trichom-Entwicklung`,
            'Langsame Reduktion über 3-5 Tage empfohlen'
        ];
        
        return adjustments;
    }
    
    /**
     * Flush-Anpassungen anwenden
     */
    applyFlushAdjustments(adjustments) {
        // Hier würde die tatsächliche Anwendung der Anpassungen erfolgen
        // Für jetzt nur Logging und Status-Update
        

        
        // Status in localStorage speichern
        localStorage.setItem(`flush_adjustments_${this.currentPlantId}`, JSON.stringify({
            applied: true,
            timestamp: new Date().toISOString(),
            adjustments: adjustments
        }));
    }
    
    /**
     * Smart Dimming Status anzeigen
     */
    showSmartDimmingStatus(adjustments) {
        const container = document.getElementById('lightingOverview');
        if (!container) return;
        
        const smartDimmingCard = document.createElement('div');
        smartDimmingCard.className = 'lighting-card smart-dimming-card';
        smartDimmingCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-magic"></i>
                <span>Smart Dimming Aktiv</span>
            </div>
            <div class="lighting-card-content">
                <div class="smart-dimming-status">
                    <div class="status-badge active">Aktiv</div>
                    <div class="adjustment-summary">
                        <div class="adjustment-item">
                            <span class="adjustment-label">PPFD:</span>
                            <span class="adjustment-value">-${adjustments.ppfd_reduction} μmol/m²/s</span>
                        </div>
                        <div class="adjustment-item">
                            <span class="adjustment-label">Stunden:</span>
                            <span class="adjustment-value">-${adjustments.light_hours_reduction}h</span>
                        </div>
                        <div class="adjustment-item">
                            <span class="adjustment-label">Temperatur:</span>
                            <span class="adjustment-value">-${adjustments.color_temp_adjustment}K</span>
                        </div>
                    </div>
                </div>
                <div class="smart-dimming-actions">
                    <button class="btn btn-sm btn-outline-warning" onclick="this.showSmartDimmingDetails()">
                        <i class="fa-solid fa-info-circle"></i> Details
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="this.disableSmartDimming()">
                        <i class="fa-solid fa-power-off"></i> Deaktivieren
                    </button>
                </div>
            </div>
        `;
        
        // Smart Dimming Card am Anfang einfügen
        container.insertBefore(smartDimmingCard, container.firstChild);
    }
    
    /**
     * Smart Dimming Empfehlung anzeigen
     */
    showSmartDimmingRecommendation() {
        const container = document.getElementById('lightingOverview');
        if (!container) return;
        
        const recommendationCard = document.createElement('div');
        recommendationCard.className = 'lighting-card smart-dimming-recommendation';
        recommendationCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-lightbulb"></i>
                <span>Smart Dimming Empfehlung</span>
            </div>
            <div class="lighting-card-content">
                <div class="recommendation-text">
                    <p><strong>Flush-Phase erkannt!</strong> Smart Dimming kann automatisch die Beleuchtung für optimale Trichom-Entwicklung anpassen.</p>
                    <ul class="recommendation-benefits">
                        <li>Automatische PPFD-Reduktion (25% weniger)</li>
                        <li>Beleuchtungsstunden-Optimierung</li>
                        <li>Farbtemperatur-Anpassung für Trichome</li>
                        <li>Langsame, schonende Reduktion</li>
                    </ul>
                </div>
                <div class="recommendation-actions">
                    <button class="btn btn-success" onclick="this.enableSmartDimming()">
                        <i class="fa-solid fa-magic"></i> Smart Dimming aktivieren
                    </button>
                    <button class="btn btn-outline-secondary" onclick="this.showSmartDimmingInfo()">
                        <i class="fa-solid fa-info-circle"></i> Mehr erfahren
                    </button>
                </div>
            </div>
        `;
        
        // Empfehlung am Anfang einfügen
        container.insertBefore(recommendationCard, container.firstChild);
    }
    
    /**
     * Smart Dimming aktivieren
     */
    enableSmartDimming() {
        localStorage.setItem(`smart_dimming_${this.currentPlantId}`, 'true');
        this.showSuccess('Smart Dimming wurde aktiviert!');
        
        // Beleuchtungsdaten neu laden um Smart Dimming zu aktivieren
        this.loadLightingData();
    }
    
    /**
     * Smart Dimming deaktivieren
     */
    disableSmartDimming() {
        localStorage.setItem(`smart_dimming_${this.currentPlantId}`, 'false');
        this.showSuccess('Smart Dimming wurde deaktiviert!');
        
        // Beleuchtungsdaten neu laden
        this.loadLightingData();
    }
    
    /**
     * Smart Dimming Details anzeigen
     */
    showSmartDimmingDetails() {
        const adjustments = JSON.parse(localStorage.getItem(`flush_adjustments_${this.currentPlantId}`) || '{}');
        
        if (adjustments.adjustments) {
            const details = adjustments.adjustments.recommendations.join('\n');
            alert(`Smart Dimming Details:\n\n${details}`);
        }
    }
    
    /**
     * Smart Dimming Info anzeigen
     */
    showSmartDimmingInfo() {
        const info = `
Smart Dimming für Flush-Phase

Was macht Smart Dimming?
• Automatische Beleuchtungsanpassung in der Flush-Phase
• PPFD-Reduktion um 25% für schonende Trichom-Entwicklung
• Beleuchtungsstunden-Optimierung (12h → 10h)
• Farbtemperatur-Anpassung für bessere Trichom-Qualität

Vorteile:
• Optimale Trichom-Entwicklung
• Reduziert Stress in der kritischen Phase
• Automatische, schonende Anpassung
• Bessere Blütenqualität

Die Anpassungen erfolgen langsam über 3-5 Tage.
        `;
        alert(info);
    }

    /**
     * Energieverbrauch-Tracking
     */
    setupEnergyTracking() {
        // Energieverbrauch-Daten laden
        this.loadEnergyData();
        
        // Energieverbrauch-Card zur Übersicht hinzufügen
        this.addEnergyTrackingCard();
    }
    
    /**
     * Energieverbrauch-Daten laden
     */
    async loadEnergyData() {
        try {
            const current = this.lightingData?.current;
            if (!current) return;
            
            // Energieverbrauch berechnen
            const energyData = this.calculateEnergyConsumption(current);
            
            // Daten in localStorage speichern
            this.saveEnergyData(energyData);
            
            return energyData;
            
        } catch (error) {
            console.error('Fehler beim Laden der Energieverbrauch-Daten:', error);
        }
    }
    
    /**
     * Energieverbrauch berechnen
     */
    calculateEnergyConsumption(current) {
        const lampPower = current.lamp_power_w || 600;
        const lightHours = current.light_hours || 12;
        const daysInPhase = this.getDaysInCurrentPhase();
        
        // Täglicher Verbrauch (kWh)
        const dailyConsumption = (lampPower * lightHours) / 1000;
        
        // Wöchentlicher Verbrauch (kWh)
        const weeklyConsumption = dailyConsumption * 7;
        
        // Monatlicher Verbrauch (kWh)
        const monthlyConsumption = dailyConsumption * 30;
        
        // Kosten berechnen (0.30€/kWh Durchschnitt)
        const costPerKWh = 0.30;
        const dailyCost = dailyConsumption * costPerKWh;
        const weeklyCost = weeklyConsumption * costPerKWh;
        const monthlyCost = monthlyConsumption * costPerKWh;
        
        // CO2-Emissionen (0.5kg CO2/kWh)
        const co2PerKWh = 0.5;
        const dailyCO2 = dailyConsumption * co2PerKWh;
        const weeklyCO2 = weeklyConsumption * co2PerKWh;
        const monthlyCO2 = monthlyConsumption * co2PerKWh;
        
        // Effizienz-Bewertung
        const efficiency = this.calculateEfficiency(current);
        
        return {
            lampPower,
            lightHours,
            daysInPhase,
            dailyConsumption: Math.round(dailyConsumption * 100) / 100,
            weeklyConsumption: Math.round(weeklyConsumption * 100) / 100,
            monthlyConsumption: Math.round(monthlyConsumption * 100) / 100,
            dailyCost: Math.round(dailyCost * 100) / 100,
            weeklyCost: Math.round(weeklyCost * 100) / 100,
            monthlyCost: Math.round(monthlyCost * 100) / 100,
            dailyCO2: Math.round(dailyCO2 * 100) / 100,
            weeklyCO2: Math.round(weeklyCO2 * 100) / 100,
            monthlyCO2: Math.round(monthlyCO2 * 100) / 100,
            efficiency
        };
    }
    
    /**
     * Tage in aktueller Phase berechnen
     */
    getDaysInCurrentPhase() {
        const floweringData = this.floweringData?.flowering_status;
        if (!floweringData) return 0;
        
        const currentDay = floweringData.current_day || 0;
        const phaseStartDay = floweringData.phase_start_day || 0;
        
        return Math.max(0, currentDay - phaseStartDay);
    }
    
    /**
     * Effizienz berechnen
     */
    calculateEfficiency(current) {
        const ppfd = current.ppfd_calculated || 0;
        const lampPower = current.lamp_power_w || 600;
        
        if (lampPower === 0) return { score: 0, rating: 'Unbekannt' };
        
        // PPFD pro Watt (μmol/m²/s/W)
        const ppfdPerWatt = ppfd / lampPower;
        
        // Effizienz-Bewertung basierend auf PPFD/Watt
        let score, rating;
        
        if (ppfdPerWatt >= 1.5) {
            score = 5;
            rating = 'Sehr gut';
        } else if (ppfdPerWatt >= 1.2) {
            score = 4;
            rating = 'Gut';
        } else if (ppfdPerWatt >= 1.0) {
            score = 3;
            rating = 'Durchschnittlich';
        } else if (ppfdPerWatt >= 0.8) {
            score = 2;
            rating = 'Schlecht';
        } else {
            score = 1;
            rating = 'Sehr schlecht';
        }
        
        return {
            score,
            rating,
            ppfdPerWatt: Math.round(ppfdPerWatt * 100) / 100
        };
    }
    
    /**
     * Energieverbrauch-Daten speichern
     */
    saveEnergyData(energyData) {
        const plantId = this.currentPlantId;
        const timestamp = new Date().toISOString();
        
        // Aktuelle Daten speichern
        localStorage.setItem(`energy_data_${plantId}`, JSON.stringify({
            ...energyData,
            timestamp
        }));
        
        // Historische Daten laden und erweitern
        const history = JSON.parse(localStorage.getItem(`energy_history_${plantId}`) || '[]');
        history.push({
            ...energyData,
            timestamp
        });
        
        // Nur die letzten 30 Einträge behalten
        if (history.length > 30) {
            history.splice(0, history.length - 30);
        }
        
        localStorage.setItem(`energy_history_${plantId}`, JSON.stringify(history));
    }
    
    /**
     * Energieverbrauch-Card zur Übersicht hinzufügen
     */
    addEnergyTrackingCard() {
        const container = document.getElementById('lightingOverview');
        if (!container) return;
        
        // Energieverbrauch-Daten laden
        const energyData = JSON.parse(localStorage.getItem(`energy_data_${this.currentPlantId}`) || '{}');
        if (!energyData.dailyConsumption) return;
        
        const energyCard = document.createElement('div');
        energyCard.className = 'lighting-card energy-card';
        energyCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-bolt"></i>
                <span>Energieverbrauch</span>
            </div>
            <div class="lighting-card-content">
                <div class="energy-summary">
                    <div class="energy-item">
                        <span class="energy-label">Täglich:</span>
                        <span class="energy-value">${energyData.dailyConsumption} kWh</span>
                        <span class="energy-cost">${energyData.dailyCost}€</span>
                    </div>
                    <div class="energy-item">
                        <span class="energy-label">Wöchentlich:</span>
                        <span class="energy-value">${energyData.weeklyConsumption} kWh</span>
                        <span class="energy-cost">${energyData.weeklyCost}€</span>
                    </div>
                    <div class="energy-item">
                        <span class="energy-label">Monatlich:</span>
                        <span class="energy-value">${energyData.monthlyConsumption} kWh</span>
                        <span class="energy-cost">${energyData.monthlyCost}€</span>
                    </div>
                </div>
                
                <div class="energy-efficiency">
                    <div class="efficiency-header">
                        <span>Effizienz:</span>
                        <span class="efficiency-rating ${this.getEfficiencyClass(energyData.efficiency?.score)}">${energyData.efficiency?.rating}</span>
                    </div>
                    <div class="efficiency-details">
                        <span>PPFD/Watt: ${energyData.efficiency?.ppfdPerWatt}</span>
                    </div>
                </div>
                
                <div class="energy-actions">
                    <button class="btn btn-sm btn-outline-info" onclick="this.showEnergyDetails()">
                        <i class="fa-solid fa-chart-line"></i> Details
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="this.showEnergyOptimization()">
                        <i class="fa-solid fa-lightbulb"></i> Optimieren
                    </button>
                </div>
            </div>
        `;
        
        // Energy Card am Ende einfügen
        container.appendChild(energyCard);
    }
    
    /**
     * Effizienz-Klasse für CSS
     */
    getEfficiencyClass(score) {
        if (score >= 4) return 'excellent';
        if (score >= 3) return 'good';
        if (score >= 2) return 'average';
        return 'poor';
    }
    
    /**
     * Energieverbrauch-Details anzeigen
     */
    showEnergyDetails() {
        const energyData = JSON.parse(localStorage.getItem(`energy_data_${this.currentPlantId}`) || '{}');
        const history = JSON.parse(localStorage.getItem(`energy_history_${this.currentPlantId}`) || '[]');
        
        let details = `Energieverbrauch-Details:\n\n`;
        details += `Lampenleistung: ${energyData.lampPower}W\n`;
        details += `Beleuchtungsstunden: ${energyData.lightHours}h\n`;
        details += `Täglicher Verbrauch: ${energyData.dailyConsumption} kWh (${energyData.dailyCost}€)\n`;
        details += `Wöchentlicher Verbrauch: ${energyData.weeklyConsumption} kWh (${energyData.weeklyCost}€)\n`;
        details += `Monatlicher Verbrauch: ${energyData.monthlyConsumption} kWh (${energyData.monthlyCost}€)\n\n`;
        details += `CO2-Emissionen:\n`;
        details += `Täglich: ${energyData.dailyCO2} kg CO2\n`;
        details += `Wöchentlich: ${energyData.weeklyCO2} kg CO2\n`;
        details += `Monatlich: ${energyData.monthlyCO2} kg CO2\n\n`;
        details += `Effizienz: ${energyData.efficiency?.rating} (${energyData.efficiency?.ppfdPerWatt} PPFD/Watt)\n`;
        details += `Historische Einträge: ${history.length}`;
        
        alert(details);
    }
    
    /**
     * Energieverbrauch-Optimierung anzeigen
     */
    showEnergyOptimization() {
        const energyData = JSON.parse(localStorage.getItem(`energy_data_${this.currentPlantId}`) || '{}');
        const efficiency = energyData.efficiency;
        
        let optimization = `Energieverbrauch-Optimierung:\n\n`;
        
        if (efficiency?.score <= 2) {
            optimization += `🔴 Aktuelle Effizienz: ${efficiency?.rating}\n`;
            optimization += `Empfehlungen:\n`;
            optimization += `• LED-Lampen mit höherer Effizienz verwenden\n`;
            optimization += `• Lampenabstand optimieren\n`;
            optimization += `• Reflektoren für bessere Lichtausnutzung\n`;
            optimization += `• Beleuchtungsstunden reduzieren falls möglich\n`;
        } else if (efficiency?.score <= 3) {
            optimization += `🟡 Aktuelle Effizienz: ${efficiency?.rating}\n`;
            optimization += `Verbesserungen möglich:\n`;
            optimization += `• Lampenabstand überprüfen\n`;
            optimization += `• Reflektoren hinzufügen\n`;
            optimization += `• Beleuchtungsstunden optimieren\n`;
        } else {
            optimization += `🟢 Aktuelle Effizienz: ${efficiency?.rating}\n`;
            optimization += `Sehr gute Effizienz! Weitere Optimierungen:\n`;
            optimization += `• Automatische Dimming-Funktionen\n`;
            optimization += `• Smart Scheduling\n`;
            optimization += `• Zusatz-LEDs für spezifische Wellenlängen\n`;
        }
        
        optimization += `\nPotenzielle Einsparungen:\n`;
        optimization += `• 10% weniger Beleuchtungsstunden: ${Math.round(energyData.dailyCost * 0.1 * 100) / 100}€/Tag\n`;
        optimization += `• 20% effizientere Lampen: ${Math.round(energyData.dailyCost * 0.2 * 100) / 100}€/Tag\n`;
        
        alert(optimization);
    }

    /**
     * LightingManager Integration
     */
    setupLightingManager() {
        // Prüfen ob LightingManager verfügbar ist
        if (!window.lightingManager) {
            console.warn('🌸 Blüte-Widget: LightingManager nicht verfügbar');
            return;
        }
        
        // Widget beim LightingManager registrieren
        window.lightingManager.subscribe(this.widgetId, (eventType, data) => {
            this.handleLightingManagerEvent(eventType, data);
        });
        

    }
    
    /**
     * LightingManager Events behandeln
     */
    handleLightingManagerEvent(eventType, data) {
        const { plantId } = data;
        
        // Nur Events für die aktuelle Pflanze behandeln
        if (plantId !== this.currentPlantId) {
            return;
        }
        
        switch (eventType) {
            case 'lightingDataUpdated':
                this.handleLightingDataUpdate(data);
                break;
            case 'plantStateUpdated':
                this.handlePlantStateUpdate(data);
                break;
            case 'smartDimmingUpdated':
                this.handleSmartDimmingUpdate(data);
                break;
            case 'energyDataUpdated':
                this.handleEnergyDataUpdate(data);
                break;
            default:
                // Unbekanntes Event ignoriert
        }
    }
    
    /**
     * Beleuchtungsdaten-Update vom LightingManager behandeln
     */
    handleLightingDataUpdate(data) {
        // Beleuchtungsdaten aktualisieren
        this.lightingData = data.data;
        
        // UI aktualisieren falls Beleuchtungs-Tab aktiv ist
        if (this.currentTab === 'lighting') {
            this.renderLightingOverview(this.lightingData);
            this.renderLightingSettings(this.lightingData);
            this.updateLightingStatus(this.lightingData);
        }
        
        // Automatische Anpassung nur einmal beim ersten Laden durchführen
        // Nicht bei jedem Update, um Endlosschleifen zu vermeiden
        if (!this.autoAdjustmentChecked) {
            this.autoAdjustmentChecked = true;
            // Verzögerung hinzufügen, um sicherzustellen, dass alle Daten geladen sind
            setTimeout(() => {
                this.checkAutoAdjustment(data.data);
            }, 1000);
        }
    }
    
    /**
     * Pflanzenstatus-Update vom LightingManager behandeln
     */
    handlePlantStateUpdate(data) {
        // Pflanzenstatus aktualisieren
        const plantState = data.state;
        
        // Automatische Beleuchtungsanpassung prüfen
        this.checkAutoAdjustment(plantState);
    }
    
    /**
     * Smart Dimming-Update vom LightingManager behandeln
     */
    handleSmartDimmingUpdate(data) {
        // Smart Dimming Status aktualisieren
        const smartDimmingState = data.state;
        
        // UI aktualisieren falls Beleuchtungs-Tab aktiv ist
        if (this.currentTab === 'lighting') {
            this.updateSmartDimmingUI(smartDimmingState);
        }
    }
    
    /**
     * Energieverbrauch-Update vom LightingManager behandeln
     */
    handleEnergyDataUpdate(data) {
        // Energieverbrauch-Daten aktualisieren
        const energyData = data.data;
        
        // UI aktualisieren falls Beleuchtungs-Tab aktiv ist
        if (this.currentTab === 'lighting') {
            this.updateEnergyTrackingUI(energyData);
        }
    }
    
    /**
     * Automatische Beleuchtungsanpassung prüfen
     */
    async checkAutoAdjustment(plantState) {
        if (!window.lightingManager) return;
        
        try {
            // Automatische Anpassung anfordern
            const adjustments = await window.lightingManager.autoAdjustLighting(this.currentPlantId);
            
            if (adjustments) {
                // Anpassungen anzeigen
                this.showAutoAdjustmentNotification(adjustments);
                
                // Beleuchtungsdaten vom Manager abrufen (nicht neu laden)
                const updatedData = window.lightingManager.getLightingData(this.currentPlantId);
                if (updatedData && this.currentTab === 'lighting') {
                    this.lightingData = updatedData;
                    this.renderLightingOverview(updatedData);
                    this.renderLightingSettings(updatedData);
                    this.updateLightingStatus(updatedData);
                }
            }
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei automatischer Anpassung:', error);
        }
    }
    
    /**
     * Automatische Anpassungs-Benachrichtigung anzeigen
     */
    showAutoAdjustmentNotification(adjustments) {
        const notification = document.createElement('div');
        notification.className = 'auto-adjustment-notification';
        notification.innerHTML = `
            <div class="notification-header">
                <i class="fa-solid fa-magic"></i>
                <span>Automatische Beleuchtungsanpassung</span>
                <button class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
            <div class="notification-content">
                <p><strong>Grund:</strong> ${adjustments.reason}</p>
                <div class="adjustment-details">
                    ${adjustments.ppfd_adjustment !== 0 ? `<div>PPFD: ${adjustments.ppfd_adjustment > 0 ? '+' : ''}${adjustments.ppfd_adjustment} μmol/m²/s</div>` : ''}
                    ${adjustments.light_hours_adjustment !== 0 ? `<div>Beleuchtungsstunden: ${adjustments.light_hours_adjustment > 0 ? '+' : ''}${adjustments.light_hours_adjustment}h</div>` : ''}
                    ${adjustments.color_temp_adjustment !== 0 ? `<div>Farbtemperatur: ${adjustments.color_temp_adjustment > 0 ? '+' : ''}${adjustments.color_temp_adjustment}K</div>` : ''}
                </div>
                <div class="confidence-indicator">
                    <span>Konfidenz: ${Math.round(adjustments.confidence * 100)}%</span>
                </div>
            </div>
        `;
        
        // Notification am Anfang des Widgets einfügen
        this.element.insertBefore(notification, this.element.firstChild);
        
        // Automatisch nach 10 Sekunden entfernen
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }
    
    /**
     * Smart Dimming UI aktualisieren
     */
    updateSmartDimmingUI(smartDimmingState) {
        // Smart Dimming Status in der UI aktualisieren
        const smartDimmingCard = this.element.querySelector('.smart-dimming-card');
        if (smartDimmingCard && smartDimmingState.enabled) {
            // Smart Dimming ist aktiv, Status aktualisieren
            const statusBadge = smartDimmingCard.querySelector('.status-badge');
            if (statusBadge) {
                statusBadge.textContent = 'Aktiv';
                statusBadge.className = 'status-badge active';
            }
        }
    }
    
    /**
     * Energieverbrauch-Tracking UI aktualisieren
     */
    updateEnergyTrackingUI(energyData) {
        // Energieverbrauch-Card in der UI aktualisieren
        const energyCard = this.element.querySelector('.energy-card');
        if (energyCard) {
            // Energieverbrauch-Werte aktualisieren
            const dailyValue = energyCard.querySelector('.energy-item:nth-child(1) .energy-value');
            const dailyCost = energyCard.querySelector('.energy-item:nth-child(1) .energy-cost');
            
            if (dailyValue && energyData.dailyConsumption) {
                dailyValue.textContent = `${energyData.dailyConsumption} kWh`;
            }
            if (dailyCost && energyData.dailyCost) {
                dailyCost.textContent = `${energyData.dailyCost}€`;
            }
        }
    }
    
    /**
     * Beleuchtungsdaten an LightingManager senden
     */
    sendLightingDataToManager() {
        if (!window.lightingManager || !this.lightingData) return;
        
        // Beleuchtungsdaten an LightingManager senden
        window.lightingManager.setLightingData(this.currentPlantId, this.lightingData);
        
        // Pflanzenstatus an LightingManager senden
        if (this.floweringData?.flowering_status) {
            const plantState = {
                phase: this.floweringData.flowering_status.phase,
                current_day: this.floweringData.flowering_status.current_day,
                strain_type: this.floweringData.strain_profile?.strain_type || 'photoperiodic'
            };
            window.lightingManager.setPlantState(this.currentPlantId, plantState);
        }
        
        // Smart Dimming Status an LightingManager senden
        const smartDimmingEnabled = localStorage.getItem(`smart_dimming_${this.currentPlantId}`) === 'true';
        if (smartDimmingEnabled) {
            const smartDimmingState = {
                enabled: true,
                plantId: this.currentPlantId,
                timestamp: new Date().toISOString()
            };
            window.lightingManager.setSmartDimmingState(this.currentPlantId, smartDimmingState);
        }
        
        // Energieverbrauch-Daten an LightingManager senden
        const energyData = JSON.parse(localStorage.getItem(`energy_data_${this.currentPlantId}`) || '{}');
        if (energyData.dailyConsumption) {
            window.lightingManager.setEnergyData(this.currentPlantId, energyData);
        }
        
        // KI-Empfehlungen nur einmal beim ersten Laden generieren
        if (!this.aiRecommendationsGenerated) {
            this.aiRecommendationsGenerated = true;
            setTimeout(() => {
                this.generateAIRecommendations();
            }, 2000);
        }
    }
    
    /**
     * Widget beim Verlassen aufräumen
     */
    cleanup() {
        if (window.lightingManager) {
            window.lightingManager.unsubscribe(this.widgetId);
        }
    }
    
    /**
     * Smart Scheduling Integration
     */
    setupSmartScheduling() {
        if (!window.smartScheduler) {
            console.warn('🌸 Blüte-Widget: SmartScheduler nicht verfügbar');
            return;
        }
        
        // Smart Scheduling nur beim ersten Laden initialisieren
        if (!this.smartSchedulingInitialized) {
            this.smartSchedulingInitialized = true;
            
            // Benutzer-Präferenzen laden
            const userPreferences = window.smartScheduler.getUserPreferences(this.currentPlantId);
            
            // Intelligente Zeitpläne mit Verzögerung generieren
            setTimeout(() => {
                this.generateSmartSchedule(userPreferences);
            }, 3000);
        }
    }
    
    /**
     * Intelligente Zeitpläne generieren
     */
    async generateSmartSchedule(userPreferences = {}) {
        if (!window.smartScheduler) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.floweringData?.flowering_status?.current_day || 0,
                phase_start_day: this.floweringData?.flowering_status?.phase_start_day || 0
            };
            
            // Intelligente Zeitpläne generieren
            const schedule = await window.smartScheduler.generateSmartSchedule(
                this.currentPlantId,
                plantData,
                this.lightingData,
                userPreferences
            );
            
            if (schedule) {
                // Zeitplan anzeigen
                this.showSmartSchedule(schedule);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Generieren des Smart Schedules:', error);
        }
    }
    
    /**
     * Smart Schedule anzeigen
     */
    showSmartSchedule(schedule) {
        // Prüfen ob bereits eine Smart Schedule Card existiert
        let scheduleCard = this.element.querySelector('.smart-schedule-card');
        
        if (!scheduleCard) {
            // Neue Smart Schedule Card erstellen
            scheduleCard = document.createElement('div');
            scheduleCard.className = 'lighting-card smart-schedule-card';
            this.element.appendChild(scheduleCard);
        }
        
        // Card-Inhalt aktualisieren
        scheduleCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-calendar-alt"></i>
                <span>Smart Schedule</span>
                <div class="schedule-status">
                    <span class="status-badge active">Aktiv</span>
                </div>
            </div>
            <div class="lighting-card-content">
                <div class="schedule-info">
                    <h6>${schedule.name}</h6>
                    <p>${schedule.description}</p>
                </div>
                <div class="schedule-details">
                    <div class="schedule-item">
                        <span class="label">Startzeit:</span>
                        <span class="value">${schedule.schedule.start_time}</span>
                    </div>
                    <div class="schedule-item">
                        <span class="label">Endzeit:</span>
                        <span class="value">${schedule.schedule.end_time}</span>
                    </div>
                    <div class="schedule-item">
                        <span class="label">Beleuchtungsstunden:</span>
                        <span class="value">${schedule.schedule.total_hours}h</span>
                    </div>
                    <div class="schedule-item">
                        <span class="label">PPFD-Bereich:</span>
                        <span class="value">${schedule.schedule.ppfd_ramp.start}-${schedule.schedule.ppfd_ramp.peak} μmol/m²/s</span>
                    </div>
                </div>
                <div class="schedule-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="this.showScheduleDetails()">
                        <i class="fa-solid fa-chart-line"></i> Details
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="this.activateSchedule()">
                        <i class="fa-solid fa-play"></i> Aktivieren
                    </button>
                </div>
            </div>
        `;
        
        // Schedule Card in den Phase 6 Features Container einfügen
        const phase6Container = this.element.querySelector('#phase6Features');
        if (phase6Container && scheduleCard.parentElement !== phase6Container) {
            phase6Container.appendChild(scheduleCard);
        }
    }
    
    /**
     * Predictive Analytics Integration
     */
    setupPredictiveAnalytics() {
        if (!window.predictiveAnalytics) {
            console.warn('🌸 Blüte-Widget: PredictiveAnalytics nicht verfügbar');
            return;
        }
        
        // Predictive Analytics nur beim ersten Laden initialisieren
        if (!this.predictiveAnalyticsInitialized) {
            this.predictiveAnalyticsInitialized = true;
            
            // Analytics mit Verzögerung generieren
            setTimeout(() => {
                this.generateGrowthPrediction();
                this.predictProblems();
                this.generateHarvestPrediction();
            }, 4000);
        }
    }
    
    /**
     * Wachstumsprognose generieren
     */
    async generateGrowthPrediction() {
        if (!window.predictiveAnalytics) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.floweringData?.flowering_status?.current_day || 0,
                phase_start_day: this.floweringData?.flowering_status?.phase_start_day || 0
            };
            
            // Wachstumsprognose generieren
            const prediction = await window.predictiveAnalytics.generateGrowthPrediction(
                this.currentPlantId,
                plantData,
                this.lightingData,
                7 // 7 Tage Vorhersage
            );
            
            if (prediction.available) {
                // Prognose anzeigen
                this.showGrowthPrediction(prediction);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Wachstumsprognose:', error);
        }
    }
    
    /**
     * Wachstumsprognose anzeigen
     */
    showGrowthPrediction(prediction) {
        // Prüfen ob bereits eine Prognose Card existiert
        let predictionCard = this.element.querySelector('.growth-prediction-card');
        
        if (!predictionCard) {
            // Neue Prognose Card erstellen
            predictionCard = document.createElement('div');
            predictionCard.className = 'lighting-card growth-prediction-card';
            this.element.appendChild(predictionCard);
        }
        
        // Card-Inhalt aktualisieren
        predictionCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-chart-line"></i>
                <span>Wachstumsprognose</span>
                <div class="prediction-confidence">
                    <span class="confidence-badge ${this.getConfidenceClass(prediction.confidence)}">
                        ${Math.round(prediction.confidence * 100)}% Konfidenz
                    </span>
                </div>
            </div>
            <div class="lighting-card-content">
                <div class="prediction-summary">
                    <p>7-Tage Wachstumsprognose basierend auf historischen Daten</p>
                </div>
                <div class="prediction-chart">
                    <div class="chart-container">
                        ${prediction.prediction.map((day, index) => `
                            <div class="prediction-day">
                                <div class="day-label">Tag ${day.day}</div>
                                <div class="prediction-bars">
                                    <div class="height-bar" style="height: ${Math.min(100, day.height / 2)}%"></div>
                                    <div class="ppfd-bar" style="height: ${Math.min(100, day.ppfd / 10)}%"></div>
                                </div>
                                <div class="prediction-values">
                                    <span class="height-value">${day.height.toFixed(1)}cm</span>
                                    <span class="ppfd-value">${day.ppfd} μmol/m²/s</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="prediction-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="this.showPredictionDetails()">
                        <i class="fa-solid fa-info-circle"></i> Details
                    </button>
                </div>
            </div>
        `;
        
        // Prognose Card in den Phase 6 Features Container einfügen
        const phase6Container = this.element.querySelector('#phase6Features');
        if (phase6Container && predictionCard.parentElement !== phase6Container) {
            phase6Container.appendChild(predictionCard);
        }
    }
    
    /**
     * Problemerkennung durchführen
     */
    async predictProblems() {
        if (!window.predictiveAnalytics) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.floweringData?.flowering_status?.current_day || 0
            };
            
            // Umwelt-Daten (vereinfacht)
            const environmentalData = {
                temperature: 25, // Beispiel-Wert
                humidity: 60,    // Beispiel-Wert
                co2: 400         // Beispiel-Wert
            };
            
            // Probleme vorhersagen
            const problems = await window.predictiveAnalytics.predictProblems(
                this.currentPlantId,
                plantData,
                this.lightingData,
                environmentalData
            );
            
            if (problems.length > 0) {
                // Probleme anzeigen
                this.showProblemPredictions(problems);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Problemerkennung:', error);
        }
    }
    
    /**
     * Problem-Vorhersagen anzeigen
     */
    showProblemPredictions(problems) {
        // Prüfen ob bereits eine Problem Card existiert
        let problemCard = this.element.querySelector('.problem-prediction-card');
        
        if (!problemCard) {
            // Neue Problem Card erstellen
            problemCard = document.createElement('div');
            problemCard.className = 'lighting-card problem-prediction-card';
            this.element.appendChild(problemCard);
        }
        
        // Card-Inhalt aktualisieren
        problemCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-exclamation-triangle"></i>
                <span>Problemerkennung</span>
                <div class="problem-count">
                    <span class="count-badge">${problems.length}</span>
                </div>
            </div>
            <div class="lighting-card-content">
                <div class="problems-list">
                    ${problems.map((problem, index) => `
                        <div class="problem-item ${problem.severity}">
                            <div class="problem-header">
                                <span class="severity-badge ${problem.severity}">${this.getSeverityText(problem.severity)}</span>
                                <span class="probability">${Math.round(problem.probability * 100)}% Wahrscheinlichkeit</span>
                            </div>
                            <div class="problem-content">
                                <p>${problem.message}</p>
                                <div class="problem-recommendation">
                                    <strong>Empfehlung:</strong> ${problem.recommendation}
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // Problem Card in den Phase 6 Features Container einfügen
        const phase6Container = this.element.querySelector('#phase6Features');
        if (phase6Container && problemCard.parentElement !== phase6Container) {
            phase6Container.appendChild(problemCard);
        }
    }
    
    /**
     * Ernte-Prognose generieren
     */
    async generateHarvestPrediction() {
        if (!window.predictiveAnalytics) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.floweringData?.flowering_status?.current_day || 0
            };
            
            // Trichom-Daten (vereinfacht)
            const trichomeData = {
                clear: 30,
                milky: 50,
                amber: 20
            };
            
            // Ernte-Prognose generieren
            const harvestPrediction = await window.predictiveAnalytics.generateHarvestPrediction(
                this.currentPlantId,
                plantData,
                trichomeData
            );
            
            if (harvestPrediction.available) {
                // Ernte-Prognose anzeigen
                this.showHarvestPrediction(harvestPrediction);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Ernte-Prognose:', error);
        }
    }
    
    /**
     * Ernte-Prognose anzeigen
     */
    showHarvestPrediction(harvestPrediction) {
        // Delegiere an UIRenderer falls verfügbar
        if (this.uiRenderer) {
            return this.uiRenderer.showHarvestPrediction(harvestPrediction);
        }

        // Fallback: Originale Implementierung
        let harvestCard = this.element.querySelector('.harvest-prediction-card');

        if (!harvestCard) {
            harvestCard = document.createElement('div');
            harvestCard.className = 'lighting-card harvest-prediction-card';
            this.element.appendChild(harvestCard);
        }

        harvestCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-cut"></i>
                <span>Ernte-Prognose</span>
                <div class="harvest-confidence">
                    <span class="confidence-badge ${this.getConfidenceClass(harvestPrediction.confidence)}">
                        ${Math.round(harvestPrediction.confidence * 100)}% Konfidenz
                    </span>
                </div>
            </div>
            <div class="lighting-card-content">
                <div class="harvest-info">
                    <div class="harvest-date">
                        <span class="label">Optimaler Ernte-Tag:</span>
                        <span class="value">Tag ${harvestPrediction.harvestDate}</span>
                    </div>
                    <div class="harvest-window">
                        <span class="label">Ernte-Fenster:</span>
                        <span class="value">Tag ${harvestPrediction.harvestWindow.early}-${harvestPrediction.harvestWindow.late}</span>
                    </div>
                </div>
            </div>
        `;

        const phase6Container = this.element.querySelector('#phase6Features');
        if (phase6Container && harvestCard.parentElement !== phase6Container) {
            phase6Container.appendChild(harvestCard);
        }
    }
    
    /**
     * Schweregrad-Text
     */
    getSeverityText(severity) {
        switch (severity) {
            case 'high': return 'Hoch';
            case 'medium': return 'Mittel';
            case 'low': return 'Niedrig';
            default: return 'Unbekannt';
        }
    }
    
    /**
     * Advanced ML Integration
     */
    setupAdvancedML() {
        if (!window.advancedMLSystem) {
            console.warn('🌸 Blüte-Widget: AdvancedMLSystem nicht verfügbar');
            return;
        }
        
        // Advanced ML nur beim ersten Laden initialisieren
        if (!this.advancedMLInitialized) {
            this.advancedMLInitialized = true;
            
            // ML-Systeme mit Verzögerung starten
            setTimeout(() => {
                this.createDeepLearningModel();
                this.startPatternRecognition();
                this.startAnomalyDetection();
            }, 5000);
        }
    }
    
    /**
     * Deep Learning Modell erstellen
     */
    async createDeepLearningModel() {
        if (!window.advancedMLSystem) return;
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.floweringData?.flowering_status?.current_day || 0
            };
            
            // Deep Learning Modell erstellen
            const model = await window.advancedMLSystem.createDeepLearningModel(
                this.currentPlantId,
                plantData
            );
            
            if (model) {
        
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Erstellen des Deep Learning Modells:', error);
        }
    }
    
    /**
     * Muster-Erkennung starten
     */
    async startPatternRecognition() {
        if (!window.advancedMLSystem) return;
        
        try {
            // Historische Daten sammeln (vereinfacht)
            const historicalData = this.generateHistoricalData();
            
            // Muster erkennen
            const patterns = await window.advancedMLSystem.recognizePatterns(
                this.currentPlantId,
                historicalData
            );
            
            // Immer Muster anzeigen (auch mit leeren Daten für Demo)
            this.showPatternRecognition(patterns.length > 0 ? patterns : this.generateDemoPatterns());
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Muster-Erkennung:', error);
            // Fallback: Demo-Daten anzeigen
            this.showPatternRecognition(this.generateDemoPatterns());
        }
    }
    
    /**
     * Demo-Muster für Anzeige generieren
     */
    generateDemoPatterns() {
        return [
            {
                type: 'growth_cycles',
                description: '3 Wachstums-Zyklen erkannt mit durchschnittlicher Dauer von 7 Tagen',
                confidence: 0.85,
                data: [
                    { peak: 7, value: 25, duration: 7 },
                    { peak: 14, value: 35, duration: 7 },
                    { peak: 21, value: 45, duration: 7 }
                ]
            },
            {
                type: 'efficiency_patterns',
                description: 'PPFD-Effizienz-Muster erkannt - Optimierungspotential identifiziert',
                confidence: 0.78,
                data: [
                    { day: 5, efficiency: 1.3, type: 'high_efficiency' },
                    { day: 12, efficiency: 0.7, type: 'low_efficiency' },
                    { day: 18, efficiency: 1.1, type: 'high_efficiency' }
                ]
            }
        ];
    }
    
    /**
     * Historische Daten generieren (vereinfacht)
     */
    generateHistoricalData() {
        const data = [];
        const currentDay = this.floweringData?.flowering_status?.current_day || 0;
        
        for (let day = Math.max(0, currentDay - 30); day <= currentDay; day++) {
            data.push({
                day: day,
                height: 20 + day * 2 + Math.random() * 5,
                ppfd: 600 + Math.random() * 200,
                temperature: 25 + Math.random() * 5,
                humidity: 60 + Math.random() * 10,
                phase_day: day
            });
        }
        
        return data;
    }
    
    /**
     * Muster-Erkennung anzeigen
     */
    showPatternRecognition(patterns) {

        
        // Prüfen ob bereits eine Muster Card existiert
        let patternCard = this.element.querySelector('.pattern-recognition-card');
        
        if (!patternCard) {
            // Neue Muster Card erstellen
            patternCard = document.createElement('div');
            patternCard.className = 'lighting-card pattern-recognition-card';
            this.element.appendChild(patternCard);

        }
        
        // Card-Inhalt aktualisieren
        patternCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-brain"></i>
                <span>Muster-Erkennung</span>
                <div class="pattern-count">
                    <span class="count-badge">${patterns.length}</span>
                </div>
            </div>
            <div class="lighting-card-content">
                <!-- ML Deaktivierungs-Hinweis -->
                <div class="ml-notice alert alert-info mb-3">
                    <i class="fa-solid fa-info-circle me-2"></i>
                    <strong>ML System:</strong> Automatische Updates deaktiviert. Muster werden nur bei Bedarf erkannt.
                </div>
                
                <div class="patterns-list">
                    ${patterns.map((pattern, index) => `
                        <div class="pattern-item">
                            <div class="pattern-header">
                                <span class="pattern-type">${this.getPatternTypeText(pattern.type)}</span>
                                <span class="confidence">${Math.round(pattern.confidence * 100)}% Konfidenz</span>
                            </div>
                            <div class="pattern-content">
                                <p>${pattern.description}</p>
                                ${pattern.data ? `
                                    <div class="pattern-data">
                                        <strong>Details:</strong> ${this.formatPatternData(pattern.data)}
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // Muster Card in den Phase 6 Features Container einfügen
        const phase6Container = this.element.querySelector('#phase6Features');
        if (phase6Container && patternCard.parentElement !== phase6Container) {
            phase6Container.appendChild(patternCard);
        }
        
        // Muster Card auch in den Overview Tab einfügen, falls aktiv
        const overviewTab = this.element.querySelector('#overview');
        if (overviewTab && overviewTab.classList.contains('active') && patternCard.parentElement !== overviewTab) {
            overviewTab.insertBefore(patternCard, overviewTab.firstChild);
        }
    }
    
    /**
     * Muster-Typ Text
     */
    getPatternTypeText(type) {
        switch (type) {
            case 'growth_cycles': return 'Wachstums-Zyklen';
            case 'acceleration_patterns': return 'Beschleunigungs-Muster';
            case 'efficiency_patterns': return 'Effizienz-Muster';
            case 'temp_humidity_patterns': return 'Temperatur-Humidity-Korrelation';
            default: return type;
        }
    }
    
    /**
     * Muster-Daten formatieren
     */
    formatPatternData(data) {
        if (Array.isArray(data)) {
            return `${data.length} Datenpunkte erkannt`;
        }
        return JSON.stringify(data).substring(0, 100) + '...';
    }
    
    /**
     * Anomalie-Erkennung starten
     */
    async startAnomalyDetection() {
        if (!window.advancedMLSystem) return;
        
        try {
            // Sensor-Daten simulieren
            const sensorData = {
                ppfd: [600, 650, 700, 750, 800, 850, 900, 950, 1000],
                temperature: [25, 26, 27, 28, 29, 30, 31, 32, 33],
                humidity: [60, 62, 64, 66, 68, 70, 72, 74, 76],
                growth: [20, 22, 24, 26, 28, 30, 32, 34, 36]
            };
            
            // Anomalien erkennen
            const anomalies = await window.advancedMLSystem.detectAnomalies(
                this.currentPlantId,
                sensorData
            );
            
            // Immer Anomalien anzeigen (auch mit leeren Daten für Demo)
            this.showAnomalyDetection(anomalies.length > 0 ? anomalies : this.generateDemoAnomalies());
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler bei Anomalie-Erkennung:', error);
            // Fallback: Demo-Daten anzeigen
            this.showAnomalyDetection(this.generateDemoAnomalies());
        }
    }
    
    /**
     * Demo-Anomalien für Anzeige generieren
     */
    generateDemoAnomalies() {
        return [
            {
                type: 'ppfd_too_high',
                severity: 'medium',
                description: 'PPFD zu hoch: 950 μmol/m²/s',
                timestamp: new Date().toISOString(),
                confidence: 0.85
            },
            {
                type: 'temperature_too_high',
                severity: 'high',
                description: 'Temperatur zu hoch: 32°C',
                timestamp: new Date().toISOString(),
                confidence: 0.92
            }
        ];
    }
    
    /**
     * Anomalie-Erkennung anzeigen
     */
    showAnomalyDetection(anomalies) {
        // Prüfen ob bereits eine Anomalie Card existiert
        let anomalyCard = this.element.querySelector('.anomaly-detection-card');
        
        if (!anomalyCard) {
            // Neue Anomalie Card erstellen
            anomalyCard = document.createElement('div');
            anomalyCard.className = 'lighting-card anomaly-detection-card';
            this.element.appendChild(anomalyCard);
        }
        
        // Card-Inhalt aktualisieren
        anomalyCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-radar"></i>
                <span>Anomalie-Erkennung</span>
                <div class="anomaly-count">
                    <span class="count-badge">${anomalies.length}</span>
                </div>
            </div>
            <div class="lighting-card-content">
                <!-- Anomalie Deaktivierungs-Hinweis -->
                <div class="anomaly-notice alert alert-info mb-3">
                    <i class="fa-solid fa-info-circle me-2"></i>
                    <strong>Anomalie-Erkennung:</strong> Automatische Updates deaktiviert. Anomalien werden nur bei Bedarf erkannt.
                </div>
                
                <div class="anomalies-list">
                    ${anomalies.map((anomaly, index) => `
                        <div class="anomaly-item ${anomaly.severity}">
                            <div class="anomaly-header">
                                <span class="severity-badge ${anomaly.severity}">${this.getSeverityText(anomaly.severity)}</span>
                                <span class="confidence">${Math.round(anomaly.confidence * 100)}% Konfidenz</span>
                            </div>
                            <div class="anomaly-content">
                                <p>${anomaly.description}</p>
                                <div class="anomaly-timestamp">
                                    <small>Erkannt: ${new Date(anomaly.timestamp).toLocaleString('de-DE')}</small>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // Anomalie Card in den Phase 6 Features Container einfügen
        const phase6Container = this.element.querySelector('#phase6Features');
        if (phase6Container && anomalyCard.parentElement !== phase6Container) {
            phase6Container.appendChild(anomalyCard);
        }
    }
    
    /**
     * IoT Sensor Integration
     */
    setupIoTSensors() {
        if (!window.iotSensorIntegration) {
            console.warn('🌸 Blüte-Widget: IoTSensorIntegration nicht verfügbar');
            return;
        }
        
        // IoT Sensors nur beim ersten Laden initialisieren
        if (!this.iotSensorsInitialized) {
            this.iotSensorsInitialized = true;
            
            // IoT-Systeme mit Verzögerung starten
            setTimeout(() => {
                this.createSensorNetwork();
                this.registerSensors();
                this.startRemoteMonitoring();
            }, 6000);
        }
        
        // IoT Status nur beim ersten Laden anzeigen
        if (!this.iotStatusShown) {
            this.iotStatusShown = true;
            setTimeout(() => {
                this.showIoTStatus();
            }, 7000);
        }
        

    }
    
    /**
     * Sensor-Netzwerk erstellen
     */
    createSensorNetwork() {
        if (!window.iotSensorIntegration) return;
        
        try {
            const networkId = `network_${this.currentPlantId}`;
            
            // Sensor-Netzwerk erstellen
            const network = window.iotSensorIntegration.createSensorNetwork(
                networkId,
                this.currentPlantId,
                []
            );
            
            if (network) {

            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Erstellen des Sensor-Netzwerks:', error);
        }
    }
    
    /**
     * Sensoren registrieren
     */
    registerSensors() {
        if (!window.iotSensorIntegration) return;
        
        try {
            const sensors = [
                { id: `temp_${this.currentPlantId}`, type: 'temperature', location: 'Grow Room' },
                { id: `humidity_${this.currentPlantId}`, type: 'humidity', location: 'Grow Room' },
                { id: `ppfd_${this.currentPlantId}`, type: 'ppfd', location: 'Canopy Level' },
                { id: `co2_${this.currentPlantId}`, type: 'co2', location: 'Grow Room' }
            ];
            
            for (const sensor of sensors) {
                const registeredSensor = window.iotSensorIntegration.registerSensor(
                    sensor.id,
                    sensor.type,
                    sensor.location
                );
                
                if (registeredSensor) {
                    // Sensor zum Netzwerk hinzufügen
                    const networkId = `network_${this.currentPlantId}`;
                    window.iotSensorIntegration.addSensorToNetwork(networkId, sensor.id);
                    
    
                }
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Registrieren der Sensoren:', error);
        }
    }
    
    /**
     * Remote-Monitoring starten
     */
    startRemoteMonitoring() {
        if (!window.iotSensorIntegration) return;
        
        try {
            // Automatische Updates deaktiviert - nur manuelle Updates
    
            
            // Hinweis anzeigen
            this.showUpdateNotice('IoT Monitoring: Automatische Updates deaktiviert. Daten werden nur bei Bedarf aktualisiert.');
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Starten des Remote-Monitorings:', error);
        }
    }
    
    /**
     * Update-Hinweis anzeigen
     */
    showUpdateNotice(message) {
        const noticeElement = document.createElement('div');
        noticeElement.className = 'alert alert-info alert-sm mt-2';
        noticeElement.innerHTML = `
            <i class="fa-solid fa-info-circle me-2"></i>
            <small>${message}</small>
        `;
        
        // Hinweis in den Widget-Container einfügen
        const container = this.element.querySelector('.flowering-widget-container');
        if (container) {
            container.appendChild(noticeElement);
        }
    }
    
    /**
     * Simulierte Sensor-Daten senden
     */
    async sendSimulatedSensorData() {
        if (!window.iotSensorIntegration) return;
        
        try {
            const sensors = [
                { id: `temp_${this.currentPlantId}`, type: 'temperature' },
                { id: `humidity_${this.currentPlantId}`, type: 'humidity' },
                { id: `ppfd_${this.currentPlantId}`, type: 'ppfd' },
                { id: `co2_${this.currentPlantId}`, type: 'co2' }
            ];
            
            for (const sensor of sensors) {
                const value = this.generateSimulatedValue(sensor.type);
                
                await window.iotSensorIntegration.receiveSensorData(sensor.id, {
                    value: value,
                    quality: 0.9 + Math.random() * 0.1,
                    timestamp: new Date().toISOString()
                });
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Senden der Sensor-Daten:', error);
        }
    }
    
    /**
     * Simulierte Werte generieren
     */
    generateSimulatedValue(sensorType) {
        switch (sensorType) {
            case 'temperature':
                return 25 + Math.random() * 5; // 25-30°C
            case 'humidity':
                return 60 + Math.random() * 10; // 60-70%
            case 'ppfd':
                return 600 + Math.random() * 200; // 600-800 μmol/m²/s
            case 'co2':
                return 400 + Math.random() * 100; // 400-500 ppm
            default:
                return Math.random() * 100;
        }
    }
    
    /**
     * IoT Status anzeigen
     */
    showIoTStatus() {
        if (!window.iotSensorIntegration) return;
        
        try {
            const networkId = `network_${this.currentPlantId}`;
            const networkStatus = window.iotSensorIntegration.getNetworkStatus(networkId);
            
            if (networkStatus.status === 'not_found') {
                console.warn('🌸 Blüte-Widget: IoT Netzwerk nicht gefunden');
                // Fallback: Demo-IoT-Status anzeigen
                this.showIoTStatusCard(this.generateDemoIoTStatus());
                return;
            }
            
            // IoT Status Card erstellen/anzeigen
            this.showIoTStatusCard(networkStatus);
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Abrufen des IoT Status:', error);
            // Fallback: Demo-IoT-Status anzeigen
            this.showIoTStatusCard(this.generateDemoIoTStatus());
        }
    }
    
    /**
     * Demo-IoT-Status für Anzeige generieren
     */
    generateDemoIoTStatus() {
        return {
            id: `network_${this.currentPlantId}`,
            plantId: this.currentPlantId,
            status: 'active',
            sensorCount: 4,
            onlineSensors: 4,
            lastSync: new Date().toISOString(),
            dataAggregation: {
                temperature: { min: 24.5, max: 28.3, avg: 26.2 },
                humidity: { min: 58, max: 72, avg: 65 },
                ppfd: { min: 580, max: 920, avg: 750 },
                co2: { min: 380, max: 520, avg: 450 }
            },
            alerts: [
                {
                    type: 'humidity_too_low',
                    severity: 'warning',
                    message: 'Luftfeuchtigkeit zu niedrig: 58%',
                    timestamp: new Date().toISOString()
                }
            ]
        };
    }
    
    /**
     * IoT Status Card anzeigen
     */
    showIoTStatusCard(networkStatus) {
        // Prüfen ob bereits eine IoT Status Card existiert
        let iotCard = this.element.querySelector('.iot-status-card');
        
        if (!iotCard) {
            // Neue IoT Status Card erstellen
            iotCard = document.createElement('div');
            iotCard.className = 'lighting-card iot-status-card';
            this.element.appendChild(iotCard);
        }
        
        // Card-Inhalt aktualisieren
        iotCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-network-wired"></i>
                <span>IoT Sensor-Netzwerk</span>
                <div class="iot-status">
                    <span class="status-badge ${networkStatus.status}">${networkStatus.status}</span>
                </div>
            </div>
            <div class="lighting-card-content">
                <!-- IoT Deaktivierungs-Hinweis -->
                <div class="iot-notice alert alert-info mb-3">
                    <i class="fa-solid fa-info-circle me-2"></i>
                    <strong>IoT Monitoring:</strong> Automatische Updates deaktiviert. Daten werden nur bei Bedarf aktualisiert.
                </div>
                
                <div class="iot-info">
                    <div class="sensor-count">
                        <span class="label">Sensoren:</span>
                        <span class="value">${networkStatus.onlineSensors}/${networkStatus.sensorCount} online</span>
                    </div>
                    <div class="last-sync">
                        <span class="label">Letzte Synchronisation:</span>
                        <span class="value">${networkStatus.lastSync ? new Date(networkStatus.lastSync).toLocaleString('de-DE') : 'Nie'}</span>
                    </div>
                </div>
                <div class="iot-data">
                    <h6>Daten-Aggregation:</h6>
                    ${Object.entries(networkStatus.dataAggregation).map(([type, data]) => `
                        <div class="data-item">
                            <span class="type">${this.getSensorTypeText(type)}:</span>
                            <span class="values">
                                ${data.avg ? `${data.avg.toFixed(1)} ${this.getSensorUnit(type)}` : 'Keine Daten'}
                            </span>
                        </div>
                    `).join('')}
                </div>
                ${networkStatus.alerts.length > 0 ? `
                    <div class="iot-alerts">
                        <h6>Alerts (${networkStatus.alerts.length}):</h6>
                        <div class="alerts-list">
                            ${networkStatus.alerts.slice(-3).map(alert => `
                                <div class="alert-item ${alert.severity}">
                                    <span class="message">${alert.message}</span>
                                    <span class="time">${new Date(alert.timestamp).toLocaleTimeString('de-DE')}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
        
        // IoT Card in den Phase 6 Features Container einfügen
        const phase6Container = this.element.querySelector('#phase6Features');
        if (phase6Container && iotCard.parentElement !== phase6Container) {
            phase6Container.appendChild(iotCard);
        }
    }
    
    /**
     * Sensor-Typ Text
     */
    getSensorTypeText(type) {
        switch (type) {
            case 'temperature': return 'Temperatur';
            case 'humidity': return 'Luftfeuchtigkeit';
            case 'ppfd': return 'PPFD';
            case 'co2': return 'CO2';
            default: return type;
        }
    }
    
    /**
     * Sensor-Einheit
     */
    getSensorUnit(type) {
        switch (type) {
            case 'temperature': return '°C';
            case 'humidity': return '%';
            case 'ppfd': return 'μmol/m²/s';
            case 'co2': return 'ppm';
            default: return '';
        }
    }

    /**
     * KI-Empfehlungen generieren
     */
    async generateAIRecommendations() {
        if (!window.aiLightingAdvisor) {
            console.warn('🌸 Blüte-Widget: AI Lighting Advisor nicht verfügbar');
            return;
        }
        
        try {
            // Pflanzenstatus-Daten vorbereiten
            const plantData = {
                phase: this.floweringData?.flowering_status?.phase || 'flowering_middle',
                strain_type: this.floweringData?.strain_profile?.strain_type || 'photoperiodic',
                current_day: this.floweringData?.flowering_status?.current_day || 0,
                phase_start_day: this.floweringData?.flowering_status?.phase_start_day || 0
            };
            
            // Energieverbrauch-Daten laden
            const energyData = JSON.parse(localStorage.getItem(`energy_data_${this.currentPlantId}`) || '{}');
            
            // KI-Empfehlungen generieren
            const recommendations = await window.aiLightingAdvisor.generateRecommendations(
                this.currentPlantId,
                plantData,
                this.lightingData,
                energyData
            );
            
            if (recommendations && recommendations.recommendations.length > 0) {
                // Wachstumsdaten für Trend-Analyse nur einmal speichern
                if (this.lightingData?.current && !this.growthDataSaved) {
                    this.growthDataSaved = true;
                    const growthData = {
                        ppfd: this.lightingData.current.ppfd_calculated || 0,
                        efficiency: energyData.efficiency?.ppfdPerWatt || 0,
                        phase: plantData.phase,
                        day: plantData.current_day
                    };
                    window.aiLightingAdvisor.saveGrowthData(this.currentPlantId, growthData);
                }
                
                // AI-Empfehlungen anzeigen
                this.showAIRecommendations(recommendations);
            }
            
        } catch (error) {
            console.error('🌸 Blüte-Widget: Fehler beim Generieren von KI-Empfehlungen:', error);
        }
    }
    
    /**
     * AI-Empfehlungen anzeigen
     */
    showAIRecommendations(recommendations) {
        // Prüfen ob bereits eine AI-Empfehlungs-Card existiert
        let aiCard = this.element.querySelector('.ai-recommendations-card');
        
        if (!aiCard) {
            // Neue AI-Empfehlungs-Card erstellen
            aiCard = document.createElement('div');
            aiCard.className = 'lighting-card ai-recommendations-card';
            this.element.appendChild(aiCard);
        }
        
        // Card-Inhalt aktualisieren
        aiCard.innerHTML = `
            <div class="lighting-card-header">
                <i class="fa-solid fa-robot"></i>
                <span>KI-Empfehlungen</span>
                <div class="ai-confidence">
                    <span class="confidence-badge ${this.getConfidenceClass(recommendations.confidence)}">
                        ${Math.round(recommendations.confidence * 100)}% Konfidenz
                    </span>
                </div>
            </div>
            <div class="lighting-card-content">
                <div class="ai-recommendations-list">
                    ${recommendations.recommendations.map((rec, index) => `
                        <div class="ai-recommendation-item ${rec.priority}">
                            <div class="recommendation-header">
                                <span class="priority-badge ${rec.priority}">${this.getPriorityText(rec.priority)}</span>
                                <h6>${rec.title}</h6>
                            </div>
                            <div class="recommendation-content">
                                <p>${rec.description}</p>
                                <div class="recommendation-actions">
                                    <span class="action-text"><strong>Aktion:</strong> ${rec.action}</span>
                                    <span class="improvement-text"><strong>Erwartete Verbesserung:</strong> ${rec.expectedImprovement}</span>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <div class="ai-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="this.showAIRecommendationDetails()">
                        <i class="fa-solid fa-chart-line"></i> Details
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="this.applyAIRecommendations()">
                        <i class="fa-solid fa-magic"></i> Anwenden
                    </button>
                </div>
            </div>
        `;
        
        // AI-Card in den Phase 6 Features Container einfügen
        const phase6Container = this.element.querySelector('#phase6Features');
        if (phase6Container && aiCard.parentElement !== phase6Container) {
            phase6Container.appendChild(aiCard);
        }
    }
    
    /**
     * Konfidenz-Klasse für CSS
     */
    getConfidenceClass(confidence) {
        if (confidence >= 0.8) return 'excellent';
        if (confidence >= 0.6) return 'good';
        if (confidence >= 0.4) return 'average';
        return 'poor';
    }
    
    /**
     * Priorität-Text
     */
    getPriorityText(priority) {
        switch (priority) {
            case 'high': return 'Hoch';
            case 'medium': return 'Mittel';
            case 'low': return 'Niedrig';
            default: return 'Unbekannt';
        }
    }
    
    /**
     * AI-Empfehlungs-Details anzeigen
     */
    showAIRecommendationDetails() {
        if (!window.aiLightingAdvisor) return;
        
        const currentRecommendation = window.aiLightingAdvisor.getCurrentRecommendation(this.currentPlantId);
        if (!currentRecommendation) {
            alert('Keine aktuellen KI-Empfehlungen verfügbar.');
            return;
        }
        
        let details = `🤖 KI-Empfehlungs-Details:\n\n`;
        details += `Pflanze: ${this.currentPlantId}\n`;
        details += `Phase: ${currentRecommendation.analysis.currentPhase}\n`;
        details += `Strain-Typ: ${currentRecommendation.analysis.strainType}\n`;
        details += `Konfidenz: ${Math.round(currentRecommendation.confidence * 100)}%\n`;
        details += `Priorität: ${this.getPriorityText(currentRecommendation.priority)}\n\n`;
        
        details += `📊 Analyse:\n`;
        details += `• Phasenfortschritt: ${Math.round(currentRecommendation.analysis.phaseProgress)}%\n`;
        details += `• Beleuchtungseffizienz: ${currentRecommendation.analysis.lightingEfficiency.score}/100\n`;
        details += `• Wachstumstrend: ${currentRecommendation.analysis.growthTrends.trend}\n`;
        details += `• Stress-Indikatoren: ${currentRecommendation.analysis.stressIndicators.length}\n`;
        details += `• Optimierungsmöglichkeiten: ${currentRecommendation.analysis.optimizationOpportunities.length}\n\n`;
        
        details += `💡 Empfehlungen:\n`;
        currentRecommendation.recommendations.forEach((rec, index) => {
            details += `${index + 1}. ${rec.title}\n`;
            details += `   ${rec.description}\n`;
            details += `   Aktion: ${rec.action}\n`;
            details += `   Verbesserung: ${rec.expectedImprovement}\n\n`;
        });
        
        alert(details);
    }
    
    /**
     * AI-Empfehlungen anwenden
     */
    async applyAIRecommendations() {
        if (!window.aiLightingAdvisor) return;
        
        const currentRecommendation = window.aiLightingAdvisor.getCurrentRecommendation(this.currentPlantId);
        if (!currentRecommendation || currentRecommendation.recommendations.length === 0) {
            alert('Keine Empfehlungen zum Anwenden verfügbar.');
            return;
        }
        
        const confirmed = confirm(
            `Möchtest du ${currentRecommendation.recommendations.length} KI-Empfehlung(en) anwenden?\n\n` +
            `Dies wird automatische Anpassungen an deinen Beleuchtungseinstellungen vornehmen.`
        );
        
        if (!confirmed) return;
        
        try {
            // Empfehlungen anwenden
            for (const recommendation of currentRecommendation.recommendations) {
                await this.applySingleRecommendation(recommendation);
            }
            
            // Beleuchtungsdaten neu laden
            await this.loadLightingData();
            
            this.showSuccess('KI-Empfehlungen erfolgreich angewendet!');
            
        } catch (error) {
            console.error('Fehler beim Anwenden der KI-Empfehlungen:', error);
            this.showError('Fehler beim Anwenden der KI-Empfehlungen: ' + error.message);
        }
    }
    
    /**
     * Einzelne Empfehlung anwenden
     */
    async applySingleRecommendation(recommendation) {
        if (!this.lightingData?.current) return;
        
        const current = this.lightingData.current;
        
        switch (recommendation.type) {
            case 'ppfd_adjustment':
                // PPFD-Anpassung über LightingManager
                if (window.lightingManager) {
                    const adjustments = {
                        ppfd_adjustment: recommendation.target - current.ppfd_calculated,
                        reason: recommendation.description,
                        confidence: 0.8
                    };
                    
                    const updatedLighting = window.lightingManager.applyAdjustments(this.lightingData, adjustments);
                    window.lightingManager.setLightingData(this.currentPlantId, updatedLighting);
                }
                break;
                
            case 'photoperiod_adjustment':
                // Photoperiode-Anpassung
                const hoursMatch = recommendation.description.match(/(\d+)h/);
                if (hoursMatch) {
                    const targetHours = parseInt(hoursMatch[1]);
                    current.light_hours = targetHours;
                    
                    // Über LightingManager aktualisieren
                    if (window.lightingManager) {
                        window.lightingManager.setLightingData(this.currentPlantId, this.lightingData);
                    }
                }
                break;
                
            case 'efficiency_improvement':
                // Effizienz-Verbesserung (Lampenabstand anpassen)
                if (current.lamp_distance_cm > 60) {
                    current.lamp_distance_cm = Math.max(40, current.lamp_distance_cm - 10);
                    
                    // Über LightingManager aktualisieren
                    if (window.lightingManager) {
                        window.lightingManager.setLightingData(this.currentPlantId, this.lightingData);
                    }
                }
                break;
                
            case 'color_temp_adjustment':
                // Farbtemperatur-Anpassung
                const tempMatch = recommendation.description.match(/(\d+)K/);
                if (tempMatch) {
                    const targetTemp = parseInt(tempMatch[1]);
                    current.color_temperature_k = targetTemp;
                    
                    // Über LightingManager aktualisieren
                    if (window.lightingManager) {
                        window.lightingManager.setLightingData(this.currentPlantId, this.lightingData);
                    }
                }
                break;
        }
    }
}

// Automatische Initialisierung des FloweringWidget
document.addEventListener('DOMContentLoaded', function() {
    const floweringWidget = new FloweringWidget();
});
