# 🎯 Basis-Version Plan - Grow-Tagebuch

**Index:** 02 - Basis-Version Plan  
**Datum:** 11.07.2025  
**Zweck:** Planung der neuen, minimalen Basis-Version  
**Status:** Planungsphase

---

## ✅ **Best Practices (IMMER beachten)**

### **Modulare Architektur**
- **Templates** für alle HTML-Strukturen
- **Separate CSS-Dateien** für Komponenten
- **Modulare JavaScript-Dateien** für Funktionalitäten
- **Keine HTML/CSS in Scripts** - strikte Trennung

### **Saubere Trennung**
- **Keine Inline-Styles** - alles in CSS-Dateien
- **Keine globalen Funktionen** - Module-Pattern
- **Template-System** für dynamische Inhalte
- **CSS-Variablen** für konsistente Styles

### **Backup-Strategie**
```powershell
# PowerShell-Backup-Code (immer verwenden)
Compress-Archive -Path * -DestinationPath "grow_diary_backup_$(Get-Date -Format yyyyMMdd_HHmm).zip" -Exclude "_backup/*", "_reports/*", "_unused/*", "venv/*", "ZenTest/*"
```

---

## 🖥️ **Hybrid-Entwicklungskonzept**

### **Phase 1: PC-Basis-Version (Port 5002)**
```
🌱 Grow-Diary Basic (PC-fokussiert)
├── Einfache Installation für Anfänger
├── Minimale Dependencies (Flask + SQLite)
├── Responsive Web-UI
├── Lokale Datenbank (eine Datei)
└── Modulare Plugin-Architektur
```

### **Phase 2: Raspberry Pi-Plugin (Optional)**
```
🌱 Grow-Diary Advanced (Raspberry Pi)
├── 24/7-Betrieb
├── Sensor-Integrationen (APERO, ZenTest)
├── Automatische Backups
├── Remote-Zugriff
└── GPIO-Konfiguration
```

---

## ⚙️ **Konfigurationsseite - Feature-Flags**

### **Grundfunktionen (immer aktiv)**
- ✅ **Pflanzenverwaltung** - CRUD-Operationen
- ✅ **Eintragsverwaltung** - Basis-Einträge
- ✅ **Notizen-System** - Text-Notizen

### **Messwerte-System (optional)**
```javascript
{
  "measurements": {
    "enabled": false,
    "features": {
      "temperature": false,      // Temperatur-Messung
      "humidity": false,         // Luftfeuchtigkeit
      "vpd": false,             // VPD-Berechnung
      "height": false,          // Pflanzenhöhe
      "lamp_distance": false,   // Lampenabstand
      "lamp_power": false       // Lampenleistung
    }
  }
}
```

### **Bewässerungs-System (optional)**
```javascript
{
  "watering": {
    "enabled": false,
    "features": {
      "water_amount": false,    // Gießmenge
      "drain_amount": false,    // Drain-Menge
      "ph_water": false,        // pH-Wert Wasser
      "ec_water": false,        // EC-Wert Wasser
      "ppm_water": false        // PPM-Wert Wasser
    }
  }
}
```

### **Dünger-System (optional)**
```javascript
{
  "fertilizer": {
    "enabled": false,
    "features": {
      "biobizz": false,         // BioBizz-Produkte
      "custom_nutrients": false, // Eigene Dünger
      "fertilizer_tracking": false // Dünger-Verfolgung
    }
  }
}
```

### **Sensor-Integrationen (optional)**
```javascript
{
  "sensors": {
    "enabled": false,
    "features": {
      "apero_pen": false,       // APERO Pen Integration
      "zentest": false,         // ZenTest Integration
      "bluetooth": false,       // Bluetooth-Support
      "live_data": false        // Echtzeit-Daten
    }
  }
}
```

### **Analyse-System (optional)**
```javascript
{
  "analysis": {
    "enabled": false,
    "features": {
      "basic_stats": false,     // Grundlegende Statistiken
      "trend_analysis": false,  // Trend-Analysen
      "heatmaps": false,        // Heatmap-Visualisierungen
      "recommendations": false, // Automatische Empfehlungen
      "chatgpt": false          // ChatGPT-Integration
    }
  }
}
```

### **Blüte-Management-Widget (optional)**
```javascript
{
  "flowering_management": {
    "enabled": false,
    "features": {
      "overview_tab": false,        // Überblick mit Zeitlinie
      "marker_system": false,       // Event-Marker mit Filter
      "trichome_analysis": false,   // Trichome-Analyse & Guidelines
      "flush_trigger": false,       // Automatische & manuelle Flush-Auslösung
      "harvest_prediction": false,  // Erntezeit-Prognose
      "guidelines": false           // Dynamische Guidelines aus JSON
    }
  }
}
```

**Widget-Funktionen:**
- **5 Tabs**: Überblick, Marker, Trichome, Flush-Trigger, Prognose
- **Guideline-Integration**: Dynamisches Laden aus JSON-Dateien
- **Marker-System**: Event-Marker mit Filter (Kategorie, Wichtigkeit)
- **Trichome-Analyse**: Persistente Speicherung, Auswertung, Empfehlungen
- **Flush-Trigger**: Automatische & manuelle Auslösung mit Status-Tracking
- **Prognose-System**: Erntezeit-Prognose mit Countdown und Risiko-Bewertung

**Dokumentation:** `/docs/BLUETE_MANAGEMENT_WIDGET.md`

---

## 📁 **Ordner-Struktur**

```
grow-diary-basic/
├── templates/          # HTML-Templates
│   ├── base/
│   ├── components/
│   └── pages/
├── static/
│   ├── styles/        # Modulare CSS-Dateien
│   │   ├── base/
│   │   │   ├── reset.css
│   │   │   └── variables.css
│   │   ├── components/
│   │   │   ├── buttons.css
│   │   │   ├── forms.css
│   │   │   └── modals.css
│   │   ├── layout/
│   │   │   ├── container.css
│   │   │   └── grid.css
│   │   └── utilities/
│   │       ├── spacing.css
│   │       └── colors.css
│   └── scripts/       # Modulare JS-Dateien
│       ├── core/
│       │   ├── app.js
│       │   ├── database.js
│       │   └── utils.js
│       ├── components/
│       │   ├── plants.js
│       │   ├── entries.js
│       │   └── modals.js
│       └── modules/
│           ├── measurements.js
│           ├── watering.js
│           └── sensors.js
├── modules/           # Python-Module
│   ├── core/
│   ├── plugins/
│   └── config/
├── docs/             # Dokumentation
├── config/           # Konfiguration
└── tests/            # Tests
```

---

## 🚀 **Entwicklungs-Phasen**

### **Phase 1: Kernfunktionen**
1. **Backup der Full-Version** erstellen
2. **Minimale Datenbank** (plants, entries)
3. **Basis-API** (CRUD-Operationen)
4. **Grundlegende UI** (Startseite, Einträge)
5. **Responsive Design** (Mobile-first)

### **Phase 2: Konfigurationssystem**
1. **Konfigurationsseite** implementieren
2. **Feature-Flags** System
3. **Dynamische UI-Anpassung**
4. **Modulare Aktivierung**

### **Phase 3: Erweiterte Funktionen**
1. **Messwerte-System** (optional)
2. **Bewässerungs-System** (optional)
3. **Dünger-System** (optional)
4. **Grundlegende Analysen** (optional)

### **Phase 4: Plugin-System**
1. **Sensor-Integrationen** (APERO, ZenTest)
2. **ChatGPT-Integration**
3. **Erweiterte Analysen**
4. **Export/Import-Funktionen**

### **Phase 5: Raspberry Pi-Plugin**
1. **24/7-Betrieb** Konfiguration
2. **Automatische Backups**
3. **Remote-Zugriff**
4. **GPIO-Integration**

---

## 📋 **Technische Anforderungen**

### **Backend (Python/Flask)**
```python
# Minimale Dependencies
- Flask
- SQLite3 (eingebaut)
- datetime
- json
- uuid
```

### **Frontend (JavaScript)**
```javascript
// Minimale Dependencies
- Vanilla JavaScript (keine Frameworks)
- Fetch API für HTTP-Requests
- LocalStorage für Theme-Persistierung
```

### **Datenbank (SQLite)**
```sql
-- Minimale Tabellen für Basis-Version
CREATE TABLE plants (
    id TEXT PRIMARY KEY,
    plant_name TEXT NOT NULL,
    start_date TEXT,
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE entries (
    id TEXT PRIMARY KEY,
    plant_id TEXT NOT NULL,
    entry_type TEXT NOT NULL,
    date TEXT NOT NULL,
    title TEXT,
    note TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (plant_id) REFERENCES plants (id)
);

CREATE TABLE config (
    key TEXT PRIMARY KEY,
    value TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🎯 **Nächste Schritte**

1. **Backup erstellen** - Full-Version sichern
2. **Basis-Version erstellen** - Minimale Funktionalität
3. **Konfigurationsseite** - Feature-Management
4. **Plugin-System** - Modulare Erweiterungen
5. **Dokumentation** - Schrittweise Dokumentation

---

**Dieser Plan dient als Blaupause für die neue, saubere Basis-Version.** 