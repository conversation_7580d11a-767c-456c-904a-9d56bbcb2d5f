/* Flowering Widget - Er<PERSON>tert um Marker-Verwaltung und Flush-Trigger */

.flowering-widget-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* <PERSON><PERSON>-Bereich */
.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.plant-info h1 {
    margin: 0 0 10px 0;
    font-size: 2.2em;
    font-weight: 700;
}

.plant-details {
    display: flex;
    gap: 20px;
    align-items: center;
}

.plant-id {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9em;
}

.strain-name {
    font-size: 1.1em;
    opacity: 0.9;
}

.current-status {
    display: flex;
    align-items: center;
    gap: 30px;
}

.bloom-day {
    text-align: center;
}

.day-number {
    display: block;
    font-size: 3em;
    font-weight: 800;
    line-height: 1;
}

.day-label {
    font-size: 0.9em;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.progress-circle {
    position: relative;
}

.progress-ring {
    position: relative;
    display: inline-block;
}

.progress-ring svg {
    transform: rotate(-90deg);
}

.progress-ring-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.3);
    stroke-width: 4;
}

.progress-ring-fill {
    fill: none;
    stroke: #4ade80;
    stroke-width: 4;
    stroke-linecap: round;
    transition: stroke-dasharray 0.5s ease;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8em;
    font-weight: 600;
    color: white;
}

/* Tab-Navigation */
.tab-navigation {
    display: flex;
    background: #f8fafc;
    border-radius: 12px;
    padding: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    font-weight: 600;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.tab-btn.active {
    background: #667eea;
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Tab-Inhalte */
.tab-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.tab-pane {
    display: none;
    padding: 30px;
}

.tab-pane.active {
    display: block;
}

/* Übersicht Tab */
.overview-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
}

.info-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

/* Phase-Card Hover-Effekt deaktivieren */
.phase-card:hover {
    transform: none;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}

.info-card h3 {
    margin: 0 0 20px 0;
    font-size: 1.3em;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Phase Card */
.phase-card {
    border-left: 4px solid #667eea;
}

.phase-name {
    font-size: 1.4em;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 15px;
}

.phase-progress {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.progress-bar {
    flex: 1;
    height: 12px;
    background: #e2e8f0;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 6px;
    transition: width 0.5s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Bootstrap Progress-Bar Override für Blüte-Widget */
.flowering-widget-container .progress {
    background-color: #e2e8f0;
    border-radius: 6px;
    height: 12px;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.flowering-widget-container .progress .progress-bar {
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 6px;
    transition: width 0.5s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-label {
    font-weight: 600;
    color: #64748b;
    min-width: 40px;
}

.phase-description {
    color: #64748b;
    font-size: 0.95em;
    line-height: 1.5;
}

/* Flush Card */
.flush-card {
    border-left: 4px solid #f59e0b;
}

.flush-card.triggered {
    border-left-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.flush-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.flush-status {
    font-size: 1.2em;
    font-weight: 600;
    color: #f59e0b;
}

.flush-card.triggered .flush-status {
    color: #ef4444;
}

.flush-countdown {
    text-align: center;
}

.days-remaining {
    display: block;
    font-size: 2em;
    font-weight: 800;
    color: #f59e0b;
}

.countdown-label {
    font-size: 0.8em;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.flush-reason {
    color: #64748b;
    font-size: 0.9em;
    line-height: 1.4;
}

/* Harvest Card */
.harvest-card {
    border-left: 4px solid #10b981;
}

.harvest-windows {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.harvest-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-radius: 8px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
}

.harvest-option.early {
    border-left: 4px solid #f59e0b;
}

.harvest-option.optimal {
    border-left: 4px solid #10b981;
    background: #f0fdf4;
}

.harvest-option.late {
    border-left: 4px solid #ef4444;
}

.harvest-label {
    font-weight: 600;
    color: #1e293b;
}

.harvest-days {
    font-weight: 600;
    color: #64748b;
}

/* Milestones Card */
.milestones-card {
    border-left: 4px solid #8b5cf6;
}

.milestones-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.milestone-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.milestone-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    background: #8b5cf6;
    color: white;
}

.milestone-content {
    flex: 1;
}

.milestone-title {
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 5px;
}

.milestone-details {
    font-size: 0.9em;
    color: #64748b;
}

.milestone-countdown {
    font-weight: 600;
    color: #8b5cf6;
}

/* Marker Tab */
.markers-container {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.markers-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.markers-header h3 {
    margin: 0;
    font-size: 1.5em;
    color: #1e293b;
}

.markers-filter {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.filter-select {
    padding: 10px 15px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: white;
    font-size: 0.9em;
    min-width: 150px;
}

.markers-list {
    display: grid;
    gap: 15px;
}

.marker-item {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.marker-item:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.marker-item.filtered-out {
    display: none !important;
}

.no-markers-message {
    text-align: center;
    padding: 40px 20px;
    color: #64748b;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.empty-icon {
    font-size: 3em;
    opacity: 0.5;
}

.empty-text {
    font-size: 1.1em;
    font-weight: 500;
}

.marker-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.marker-info {
    flex: 1;
}

.marker-title {
    font-size: 1.1em;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 5px;
}

.marker-meta {
    display: flex;
    gap: 15px;
    font-size: 0.85em;
    color: #64748b;
}

.marker-category {
    background: #f1f5f9;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.marker-importance {
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.75em;
    letter-spacing: 0.5px;
}

.marker-importance.high {
    background: #fef2f2;
    color: #ef4444;
}

.marker-importance.medium {
    background: #fffbeb;
    color: #f59e0b;
}

.marker-importance.low {
    background: #f0fdf4;
    color: #10b981;
}

.marker-actions {
    display: flex;
    gap: 8px;
}

.marker-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 0.8em;
    cursor: pointer;
    transition: all 0.2s ease;
}

.marker-btn.edit {
    background: #dbeafe;
    color: #2563eb;
}

.marker-btn.edit:hover {
    background: #bfdbfe;
}

.marker-btn.delete {
    background: #fee2e2;
    color: #ef4444;
}

.marker-btn.delete:hover {
    background: #fecaca;
}

.marker-notes {
    color: #64748b;
    font-size: 0.9em;
    line-height: 1.5;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e2e8f0;
}

/* Flush-Trigger Tab */
.flush-trigger-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.trigger-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.trigger-header h3 {
    margin: 0;
    font-size: 1.5em;
    color: #1e293b;
}

.trigger-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

.status-indicator.triggered {
    background: #ef4444;
}

.status-text {
    font-weight: 600;
    color: #64748b;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Trigger-Bedingungen */
.trigger-conditions h4 {
    margin: 0 0 20px 0;
    font-size: 1.2em;
    color: #1e293b;
}

.conditions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.condition-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
}

.condition-card:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.condition-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.condition-icon {
    font-size: 1.2em;
}

.condition-name {
    flex: 1;
    font-weight: 600;
    color: #1e293b;
}

.condition-status {
    font-size: 1.2em;
}

.condition-status.met {
    color: #10b981;
}

.condition-status.not-met {
    color: #ef4444;
}

.condition-details {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.condition-progress {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.progress-text {
    font-size: 0.9em;
    color: #64748b;
    font-weight: 500;
}

.condition-threshold {
    font-size: 0.85em;
    color: #94a3b8;
    font-style: italic;
}

.trichome-stats, .pistil-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.trichome-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    font-weight: 500;
    color: #64748b;
}

.stat-value {
    font-weight: 600;
    color: #1e293b;
}

/* Manueller Trigger */
.manual-trigger {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 25px;
}

.manual-trigger h4 {
    margin: 0 0 20px 0;
    font-size: 1.2em;
    color: #1e293b;
}

.manual-trigger-form {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 15px;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.9em;
}

.form-group input, .form-group textarea, .form-group select {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.9em;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus, .form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Flush-Status-Details */
.flush-status-details {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 25px;
}

.flush-status-details h4 {
    margin: 0 0 20px 0;
    font-size: 1.2em;
    color: #1e293b;
}

.status-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.status-detail {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.detail-label {
    font-size: 0.85em;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.detail-value {
    font-size: 1.1em;
    font-weight: 600;
    color: #1e293b;
}

/* Prognose Tab */
.prediction-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.prediction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 2px solid #e2e8f0;
}

.prediction-header h3 {
    margin: 0;
    font-size: 1.5em;
    color: #1e293b;
}

.prediction-confidence {
    display: flex;
    align-items: center;
    gap: 10px;
}

.confidence-label {
    font-size: 0.9em;
    color: #64748b;
}

.confidence-value {
    font-weight: 600;
    color: #10b981;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}



/* Empfehlungen */
.recommendations h4 {
    margin: 0 0 20px 0;
    font-size: 1.2em;
    color: #1e293b;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.recommendation-icon {
    font-size: 1.2em;
    color: #667eea;
    margin-top: 2px;
}

.recommendation-text {
    flex: 1;
    color: #374151;
    line-height: 1.5;
}

/* Risikofaktoren */
.risk-factors h4 {
    margin: 0 0 20px 0;
    font-size: 1.2em;
    color: #1e293b;
}

.risk-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.risk-category {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
}

.risk-category.high {
    border-left: 4px solid #ef4444;
}

.risk-category.medium {
    border-left: 4px solid #f59e0b;
}

.risk-category.low {
    border-left: 4px solid #10b981;
}

.risk-category h5 {
    margin: 0 0 15px 0;
    font-size: 1em;
    color: #1e293b;
}

.risk-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.risk-list li {
    padding: 8px 12px;
    background: #f8fafc;
    border-radius: 6px;
    font-size: 0.9em;
    color: #64748b;
    border-left: 3px solid transparent;
}

.risk-category.high .risk-list li {
    border-left-color: #ef4444;
    background: #fef2f2;
}

.risk-category.medium .risk-list li {
    border-left-color: #f59e0b;
    background: #fffbeb;
}

.risk-category.low .risk-list li {
    border-left-color: #10b981;
    background: #f0fdf4;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: var(--card-bg, #ffffff);
    margin: 0;
    padding: 0;
    border: 1px solid var(--border-color, #e2e8f0);
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color, #e2e8f0);
    background-color: var(--card-header-bg, #f8fafc);
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary, #1e293b);
    font-size: 1.1rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary, #64748b);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background-color: var(--hover-bg, #f1f5f9);
    color: var(--text-primary, #1e293b);
}

.modal-body {
    padding: 1.5rem;
    color: var(--text-primary, #1e293b);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color, #e2e8f0);
    background-color: var(--card-footer-bg, #f8fafc);
    border-radius: 0 0 8px 8px;
}

/* Lösch-Bestätigungs-Modal spezifische Styles */
#confirmDeleteModal .modal-body {
    text-align: center;
}

#confirmDeleteModal .warning-text {
    color: var(--danger-color, #ef4444);
    font-weight: 500;
    margin-top: 0.5rem;
    font-size: 0.9rem;
}

#confirmDeleteModal .btn-confirm-delete {
    background-color: var(--danger-color, #ef4444);
    border-color: var(--danger-color, #ef4444);
}

#confirmDeleteModal .btn-confirm-delete:hover {
    background-color: var(--danger-hover, #dc2626);
    border-color: var(--danger-hover, #dc2626);
}

/* Dark Mode Anpassungen */
[data-theme="dark"] .modal-content {
    background-color: var(--card-bg-dark, #1e293b);
    border-color: var(--border-color-dark, #334155);
}

[data-theme="dark"] .modal-header {
    background-color: var(--card-header-bg-dark, #334155);
    border-color: var(--border-color-dark, #334155);
}

[data-theme="dark"] .modal-footer {
    background-color: var(--card-footer-bg-dark, #334155);
    border-color: var(--border-color-dark, #334155);
}

/* Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f1f5f9;
    color: #374151;
}

.btn-secondary:hover {
    background: #e2e8f0;
}

.btn-warning {
    background: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background: #d97706;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* ===== TRICHOME INTEGRATION STYLES ===== */

.trichome-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.trichome-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e5e7eb;
}

.trichome-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
}

.trichome-status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    border-radius: 20px;
    background: #f9fafb;
    border: 1px solid #e5e7eb;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #9ca3af;
}

.status-dot.low { background: #10b981; }
.status-dot.medium { background: #f59e0b; }
.status-dot.high { background: #ef4444; }
.status-dot.no-data { background: #6b7280; }

.status-text {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

/* Trichom-Status-Karte */
.trichome-status-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.status-header h4 {
    margin: 0;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
}

.last-update {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Status-Badge */
.trichome-status-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    margin-bottom: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.trichome-status-badge.loading {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #e5e7eb;
}

.trichome-status-badge.early {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

.trichome-status-badge.developing {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
}

.trichome-status-badge.flush_ready {
    background: #fecaca;
    color: #991b1b;
    border: 1px solid #f87171;
}

.trichome-status-badge.harvest_ready {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #86efac;
}

.badge-icon {
    font-size: 1rem;
}

.badge-text {
    font-weight: 600;
}

/* Verbesserte Trichom-Fortschrittsbalken */
.trichome-progress-container {
    margin-bottom: 20px;
}

.progress-combined {
    display: flex;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-segment {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    transition: width 0.5s ease;
    min-width: 40px;
    flex-shrink: 0;
}

.progress-segment.clear-segment {
    background: linear-gradient(135deg, #dbeafe, #3b82f6);
    color: white;
}

.progress-segment.milky-segment {
    background: linear-gradient(135deg, #fef3c7, #f59e0b);
    color: #92400e;
}

.progress-segment.amber-segment {
    background: linear-gradient(135deg, #fed7aa, #ea580c);
    color: white;
}

.segment-label {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.segment-value {
    font-size: 0.875rem;
    font-weight: 700;
    margin-top: 2px;
}

/* Flush-Warnung Alert */
.flush-alert {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.flush-alert.warning {
    background: linear-gradient(135deg, #fef3c7, #f59e0b);
    border: 1px solid #fcd34d;
}

.flush-alert.danger {
    background: linear-gradient(135deg, #fecaca, #ef4444);
    border: 1px solid #f87171;
}

.flush-alert.success {
    background: linear-gradient(135deg, #dcfce7, #10b981);
    border: 1px solid #86efac;
}

.alert-content {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
}

.alert-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.alert-text {
    flex: 1;
}

.alert-text strong {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.alert-text span {
    font-size: 0.875rem;
    opacity: 0.9;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Trichom-Beobachtungslog */
.trichome-observation-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.observation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.observation-header h4 {
    margin: 0;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.75rem;
    border-radius: 6px;
}

.observation-content {
    max-height: 300px;
    overflow-y: auto;
}

.observation-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.observation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f9fafb;
    border-radius: 8px;
    border-left: 4px solid #3b82f6;
    transition: all 0.2s ease;
}

.observation-item:hover {
    background: #f3f4f6;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.observation-date {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.observation-values {
    display: flex;
    gap: 16px;
    font-size: 0.875rem;
    font-weight: 600;
}

.observation-value {
    display: flex;
    align-items: center;
    gap: 4px;
}

.observation-value.clear {
    color: #3b82f6;
}

.observation-value.milky {
    color: #f59e0b;
}

.observation-value.amber {
    color: #ea580c;
}

.observation-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;
    color: #9ca3af;
    font-style: italic;
}

.placeholder-text {
    font-size: 0.875rem;
}

/* Trichom-Zusammenfassung */
.trichome-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.maturity-level {
    display: flex;
    align-items: center;
    gap: 8px;
}

.level-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.level-value {
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.875rem;
}

.level-value.early {
    background: #dbeafe;
    color: #1e40af;
}

.level-value.developing {
    background: #fef3c7;
    color: #92400e;
}

.level-value.flush_ready {
    background: #fecaca;
    color: #991b1b;
}

.level-value.harvest_ready {
    background: #dcfce7;
    color: #166534;
}

.level-value.no-data {
    background: #f3f4f6;
    color: #6b7280;
}

.flush-progress {
    display: flex;
    align-items: center;
    gap: 12px;
}

.progress-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.progress-circle.small {
    position: relative;
    display: inline-block;
}

.progress-circle.small .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: #374151;
}

/* Flush-Trigger-Karte */
.flush-trigger-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.trigger-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.trigger-header h4 {
    margin: 0;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
}

.trigger-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.trigger-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #9ca3af;
}

.trigger-dot.active {
    background: #ef4444;
    animation: pulse 2s infinite;
}

.trigger-dot.inactive {
    background: #10b981;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.trigger-text {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
}

.trigger-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.trigger-message {
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
}

.trigger-message.low {
    background: #f0fdf4;
    color: #166534;
    border: 1px solid #bbf7d0;
}

.trigger-message.medium {
    background: #fffbeb;
    color: #92400e;
    border: 1px solid #fed7aa;
}

.trigger-message.high {
    background: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.trigger-actions {
    display: flex;
    gap: 12px;
}

/* Trichom-Empfehlung */
.trichome-recommendation-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.recommendation-header h4 {
    margin: 0;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
}

.urgency-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.75rem;
    font-weight: 600;
}

.urgency-badge.low {
    background: #dbeafe;
    color: #1e40af;
}

.urgency-badge.medium {
    background: #fef3c7;
    color: #92400e;
}

.urgency-badge.high {
    background: #fecaca;
    color: #991b1b;
}

.recommendation-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.recommendation-message {
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.5;
}

.recommendation-action {
    padding: 8px 12px;
    background: #f9fafb;
    border-radius: 6px;
    border-left: 4px solid #3b82f6;
}

.action-text {
    font-size: 0.875rem;
    color: #1f2937;
    font-weight: 500;
}

/* Trichom-Fortschritt */
.trichome-progress-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.progress-header h4 {
    margin: 0;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
}

.progress-info {
    font-size: 0.875rem;
    color: #6b7280;
}

.trichome-columns {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 20px;
}

.trichome-column {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 24px 16px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 16px;
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
    text-align: center;
}

.trichome-column:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #cbd5e1;
}

.column-icon {
    font-size: 2.5rem;
    margin-bottom: 12px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.column-label {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.column-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 8px;
}

.column-trend {
    display: flex;
    align-items: center;
    justify-content: center;
}

.trend-arrow {
    font-size: 1.25rem;
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.8);
}

.trend-arrow.increasing {
    color: #10b981;
}

.trend-arrow.decreasing {
    color: #ef4444;
}

.trend-arrow.stable {
    color: #f59e0b;
}

/* Spezifische Farben für jede Spalte */
.clear-column {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.clear-column .column-icon {
    color: #3b82f6;
}

.milky-column {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.milky-column .column-icon {
    color: #f59e0b;
}

.amber-column {
    border-color: #ea580c;
    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.amber-column .column-icon {
    color: #ea580c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .trichome-columns {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .trichome-column {
        padding: 20px 12px;
    }
    
    .column-icon {
        font-size: 2rem;
    }
    
    .column-value {
        font-size: 1.25rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .flowering-widget-container {
        padding: 15px;
    }
    
    .widget-header {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }
    
    .current-status {
        flex-direction: column;
        gap: 20px;
    }
    
    .tab-navigation {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: none;
        min-width: 120px;
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .conditions-grid {
        grid-template-columns: 1fr;
    }
    
    .manual-trigger-form {
        grid-template-columns: 1fr;
    }
    
    .status-details-grid {
        grid-template-columns: 1fr;
    }
    
    .risk-categories {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .trichome-container {
        padding: 16px;
    }
    
    .trichome-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .trichome-summary {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .trigger-actions {
        flex-direction: column;
    }
    
    .recommendation-header {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .plant-details {
        flex-direction: column;
        gap: 10px;
    }
    
    .day-number {
        font-size: 2.5em;
    }
    
    .info-card {
        padding: 20px;
    }
    
    .markers-filter {
        flex-direction: column;
    }
    
    .event-details {
        flex-direction: column;
        gap: 5px;
    }
}

/* ===== DARK MODE SUPPORT ===== */

/* CSS-Variablen für Dark Mode */
:root {
    --fw-bg-primary: #ffffff;
    --fw-bg-secondary: #f8fafc;
    --fw-bg-card: #ffffff;
    --fw-text-primary: #1e293b;
    --fw-text-secondary: #64748b;
    --fw-border: #e2e8f0;
    --fw-shadow: rgba(0, 0, 0, 0.08);
    --fw-shadow-hover: rgba(0, 0, 0, 0.12);
    --fw-tab-bg: #f8fafc;
    --fw-tab-text: #64748b;
    --fw-tab-active: #667eea;
    --fw-tab-hover: rgba(102, 126, 234, 0.1);
}

/* Dark Mode Variablen */
[data-theme="dark"] {
    --fw-bg-primary: #1a1a1a;
    --fw-bg-secondary: #2d2d2d;
    --fw-bg-card: #2d2d2d;
    --fw-text-primary: #e5e5e5;
    --fw-text-secondary: #a0a0a0;
    --fw-border: #404040;
    --fw-shadow: rgba(0, 0, 0, 0.3);
    --fw-shadow-hover: rgba(0, 0, 0, 0.4);
    --fw-tab-bg: #2d2d2d;
    --fw-tab-text: #a0a0a0;
    --fw-tab-active: #667eea;
    --fw-tab-hover: rgba(102, 126, 234, 0.2);
}

/* Dark Mode Anpassungen */
[data-theme="dark"] .flowering-widget-container {
    background: var(--fw-bg-primary);
    color: var(--fw-text-primary);
}

[data-theme="dark"] .tab-navigation {
    background: var(--fw-tab-bg);
    box-shadow: 0 2px 8px var(--fw-shadow);
}

[data-theme="dark"] .tab-btn {
    color: var(--fw-tab-text);
}

[data-theme="dark"] .tab-btn:hover {
    background: var(--fw-tab-hover);
    color: var(--fw-tab-active);
}

[data-theme="dark"] .tab-btn.active {
    background: var(--fw-tab-active);
    color: white;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

[data-theme="dark"] .tab-content {
    background: var(--fw-bg-card);
    box-shadow: 0 4px 20px var(--fw-shadow);
}

[data-theme="dark"] .info-card {
    background: var(--fw-bg-card);
    border: 1px solid var(--fw-border);
    box-shadow: 0 4px 16px var(--fw-shadow);
}

[data-theme="dark"] .info-card:hover {
    box-shadow: 0 8px 25px var(--fw-shadow-hover);
}

/* Phase-Card Hover-Effekt im Dark Theme deaktivieren */
[data-theme="dark"] .phase-card:hover {
    transform: none;
    box-shadow: 0 4px 16px var(--fw-shadow);
}

[data-theme="dark"] .info-card h3 {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .phase-name {
    color: var(--fw-tab-active);
}

[data-theme="dark"] .progress-bar {
    background: var(--fw-border);
}

[data-theme="dark"] .phase-description {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .flush-reason {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .harvest-option {
    background: var(--fw-bg-secondary);
    border: 1px solid var(--fw-border);
}

[data-theme="dark"] .harvest-label {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .harvest-days {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .milestone-title {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .milestone-details {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .milestone-countdown {
    color: var(--fw-text-secondary);
}

/* Trichome Dark Mode */
[data-theme="dark"] .trichome-status-card,
[data-theme="dark"] .flush-trigger-card,
[data-theme="dark"] .trichome-recommendation-card,
[data-theme="dark"] .trichome-progress-card,
[data-theme="dark"] .trichome-observation-card {
    background: var(--fw-bg-card);
    border: 1px solid var(--fw-border);
}

[data-theme="dark"] .trichome-header h3,
[data-theme="dark"] .status-header h4,
[data-theme="dark"] .trigger-header h4,
[data-theme="dark"] .recommendation-header h4,
[data-theme="dark"] .progress-header h4,
[data-theme="dark"] .observation-header h4 {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .last-update {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .badge-text {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .segment-label {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .segment-value {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .level-label {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .level-value {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .progress-label {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .trigger-text {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .trigger-message {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .recommendation-message {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .action-text {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .trend-label {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .observation-date {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .placeholder-text {
    color: var(--fw-text-secondary);
}

/* Marker Dark Mode */
[data-theme="dark"] .markers-header h3 {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .filter-select {
    background: var(--fw-bg-card);
    border: 1px solid var(--fw-border);
    color: var(--fw-text-primary);
}

[data-theme="dark"] .marker-item {
    background: var(--fw-bg-card);
    border: 1px solid var(--fw-border);
}

[data-theme="dark"] .marker-item:hover {
    box-shadow: 0 4px 12px var(--fw-shadow-hover);
}

[data-theme="dark"] .marker-title {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .marker-meta {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .marker-notes {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .no-markers-message {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .empty-text {
    color: var(--fw-text-secondary);
}

/* Flush Trigger Dark Mode */
[data-theme="dark"] .trigger-header h3 {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .status-text {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .condition-card {
    background: var(--fw-bg-card);
    border: 1px solid var(--fw-border);
}

[data-theme="dark"] .condition-name {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .condition-threshold {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .stat-label {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .stat-value {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .form-group label {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea,
[data-theme="dark"] .form-group select {
    background: var(--fw-bg-card);
    border: 1px solid var(--fw-border);
    color: var(--fw-text-primary);
}

[data-theme="dark"] .detail-label {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .detail-value {
    color: var(--fw-text-primary);
}

/* Prediction Dark Mode */
[data-theme="dark"] .prediction-header h3 {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .confidence-label {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .confidence-value {
    color: var(--fw-text-primary);
}



[data-theme="dark"] .recommendation-text {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .risk-category h5 {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .risk-list li {
    color: var(--fw-text-secondary);
}

/* Modal Dark Mode */
[data-theme="dark"] .modal-content {
    background: var(--fw-bg-card);
    box-shadow: 0 10px 40px var(--fw-shadow-hover);
}

[data-theme="dark"] .modal-header h3 {
    color: var(--fw-text-primary);
}

[data-theme="dark"] .modal-close {
    color: var(--fw-text-secondary);
}

[data-theme="dark"] .modal-close:hover {
    color: var(--fw-text-primary);
} 

/* --- DARK MODE: Verbesserte Lesbarkeit & Kontrast für Flowering Widget --- */
[data-theme="dark"] .info-card,
[data-theme="dark"] .trichome-status-card,
[data-theme="dark"] .flush-trigger-card,
[data-theme="dark"] .trichome-recommendation-card,
[data-theme="dark"] .trichome-progress-card,
[data-theme="dark"] .trichome-observation-card {
  background: #23242a;
  color: #f3f4f6;
  border: 1px solid #35363c;
}

[data-theme="dark"] .tab-content {
  background: #23242a;
}

[data-theme="dark"] .tab-btn,
[data-theme="dark"] .tab-btn.active {
  color: #e0e7ef;
}

[data-theme="dark"] .tab-btn.active {
  background: #667eea;
  color: #fff;
}

[data-theme="dark"] .progress-bar {
  background: #35363c;
}

[data-theme="dark"] .progress-segment.clear-segment {
  background: linear-gradient(90deg, #3b82f6, #2563eb);
  color: #fff;
}
[data-theme="dark"] .progress-segment.milky-segment {
  background: linear-gradient(90deg, #fbbf24, #f59e42);
  color: #23242a;
  font-weight: bold;
}
[data-theme="dark"] .progress-segment.amber-segment {
  background: linear-gradient(90deg, #f59e42, #ea580c);
  color: #fff;
}
[data-theme="dark"] .segment-label,
[data-theme="dark"] .segment-value {
  color: #fff;
  text-shadow: 0 1px 2px #0008;
}

[data-theme="dark"] .trichome-status-badge,
[data-theme="dark"] .urgency-badge,
[data-theme="dark"] .level-value {
  background: #35363c !important;
  color: #fff !important;
  border: 1px solid #444;
  box-shadow: 0 1px 4px #0004;
}
[data-theme="dark"] .trichome-status-badge.early { background: #2563eb !important; color: #fff !important; }
[data-theme="dark"] .trichome-status-badge.developing { background: #fbbf24 !important; color: #23242a !important; }
[data-theme="dark"] .trichome-status-badge.flush_ready { background: #f59e42 !important; color: #23242a !important; }
[data-theme="dark"] .trichome-status-badge.harvest_ready { background: #ea580c !important; color: #fff !important; }
[data-theme="dark"] .urgency-badge.low { background: #2563eb !important; color: #fff !important; }
[data-theme="dark"] .urgency-badge.medium { background: #fbbf24 !important; color: #23242a !important; }
[data-theme="dark"] .urgency-badge.high { background: #ef4444 !important; color: #fff !important; }
[data-theme="dark"] .level-value.early { background: #2563eb !important; color: #fff !important; }
[data-theme="dark"] .level-value.developing { background: #fbbf24 !important; color: #23242a !important; }
[data-theme="dark"] .level-value.flush_ready { background: #f59e42 !important; color: #23242a !important; }
[data-theme="dark"] .level-value.harvest_ready { background: #ea580c !important; color: #fff !important; }

[data-theme="dark"] .flush-alert,
[data-theme="dark"] .recommendation-message,
[data-theme="dark"] .action-text {
  background: #35363c !important;
  color: #fff !important;
  border: 1px solid #444;
}
[data-theme="dark"] .flush-alert.warning { background: #bfa600 !important; color: #23242a !important; }
[data-theme="dark"] .flush-alert.success { background: #15803d !important; color: #fff !important; }
[data-theme="dark"] .flush-alert.danger { background: #991b1b !important; color: #fff !important; }

[data-theme="dark"] .btn,
[data-theme="dark"] .btn-primary,
[data-theme="dark"] .btn-secondary {
  background: #35363c;
  color: #fff;
  border: 1px solid #444;
}
[data-theme="dark"] .btn-primary { background: #2563eb; color: #fff; }
[data-theme="dark"] .btn-primary:disabled { background: #35363c; color: #888; }
[data-theme="dark"] .btn-secondary { background: #23242a; color: #fff; }
[data-theme="dark"] .btn-secondary:hover { background: #35363c; color: #fff; }

[data-theme="dark"] .observation-list,
[data-theme="dark"] .progress-content {
  background: #23242a;
  color: #e0e7ef;
  border: 1px solid #35363c;
}
[data-theme="dark"] .observation-item {
  background: #35363c;
  color: #e0e7ef;
  border: 1px solid #444;
}
[data-theme="dark"] .observation-date {
  color: #a0a0a0;
}
[data-theme="dark"] .observation-value.clear { color: #60a5fa !important; }
[data-theme="dark"] .observation-value.milky { color: #fbbf24 !important; }
[data-theme="dark"] .observation-value.amber { color: #f59e42 !important; }
[data-theme="dark"] .placeholder-text { color: #888; }

[data-theme="dark"] .observation-actions {
    border-top-color: #374151;
}

[data-theme="dark"] .btn-edit {
    background: #2563eb;
    color: white;
}

[data-theme="dark"] .btn-edit:hover {
    background: #1d4ed8;
}

[data-theme="dark"] .btn-delete {
    background: #dc2626;
    color: white;
}

[data-theme="dark"] .btn-delete:hover {
    background: #b91c1c;
}

[data-theme="dark"] .observation-location {
    color: #10b981;
}

[data-theme="dark"] .progress-header h4,
[data-theme="dark"] .recommendation-header h4,
[data-theme="dark"] .trigger-header h4,
[data-theme="dark"] .status-header h4,
[data-theme="dark"] .observation-header h4 {
  color: #e0e7ef;
}
[data-theme="dark"] .progress-info,
[data-theme="dark"] .progress-label,
[data-theme="dark"] .trend-label {
  color: #a0a0a0;
}
[data-theme="dark"] .trend-arrow.increasing { color: #10b981 !important; }
[data-theme="dark"] .trend-arrow.decreasing { color: #ef4444 !important; }
[data-theme="dark"] .trend-arrow.stable { color: #fbbf24 !important; }

[data-theme="dark"] .flush-countdown,
[data-theme="dark"] .days-remaining,
[data-theme="dark"] .countdown-label {
  color: #fbbf24;
}

[data-theme="dark"] .status-dot.low { background: #10b981; }
[data-theme="dark"] .status-dot.medium { background: #fbbf24; }
[data-theme="dark"] .status-dot.high { background: #ef4444; }
[data-theme="dark"] .status-dot.no-data { background: #6b7280; } 

/* --- DARK MODE: gezielte Lesbarkeits- und Kontrastverbesserungen --- */
[data-theme="dark"] .info-card h3,
[data-theme="dark"] .status-header h4,
[data-theme="dark"] .trigger-header h4,
[data-theme="dark"] .recommendation-header h4,
[data-theme="dark"] .progress-header h4,
[data-theme="dark"] .observation-header h4,
[data-theme="dark"] .milestone-title,
[data-theme="dark"] .milestone-details,
[data-theme="dark"] .milestone-countdown,
[data-theme="dark"] .flush-status-details h4,
[data-theme="dark"] .detail-label,
[data-theme="dark"] .detail-value,
[data-theme="dark"] .harvest-label,
[data-theme="dark"] .harvest-days,
[data-theme="dark"] .countdown-label {
  color: #e0e7ef !important;
}
[data-theme="dark"] .flush-status-details {
  background: #23242a;
  color: #e0e7ef;
}
[data-theme="dark"] .form-group label {
  color: #e0e7ef;
}
[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea,
[data-theme="dark"] .form-group select {
  background: #23242a;
  color: #e0e7ef;
  border: 1px solid #444;
}
[data-theme="dark"] .form-group input::placeholder,
[data-theme="dark"] .form-group textarea::placeholder {
  color: #a0a0a0;
  opacity: 1;
}
[data-theme="dark"] .badge-text,
[data-theme="dark"] .level-label,
[data-theme="dark"] .level-value,
[data-theme="dark"] .urgency-badge {
  color: #fff !important;
  text-shadow: 0 1px 2px #000a;
}
[data-theme="dark"] .trichome-status-badge.developing,
[data-theme="dark"] .urgency-badge.medium,
[data-theme="dark"] .level-value.developing {
  color: #23242a !important;
  text-shadow: none;
}
[data-theme="dark"] .trend-arrow {
  text-shadow: 0 1px 2px #000a;
}
[data-theme="dark"] .trend-arrow.increasing { color: #10b981 !important; }
[data-theme="dark"] .trend-arrow.decreasing { color: #ef4444 !important; }
[data-theme="dark"] .trend-arrow.stable { color: #fbbf24 !important; }
[data-theme="dark"] .flush-alert,
[data-theme="dark"] .recommendation-message,
[data-theme="dark"] .action-text {
  color: #fff !important;
}
[data-theme="dark"] .flush-alert.warning { background: #bfa600 !important; color: #23242a !important; }
[data-theme="dark"] .flush-alert.success { background: #15803d !important; color: #fff !important; }
[data-theme="dark"] .flush-alert.danger { background: #991b1b !important; color: #fff !important; }
[data-theme="dark"] .btn,
[data-theme="dark"] .btn-primary,
[data-theme="dark"] .btn-secondary {
  background: #35363c;
  color: #fff;
  border: 1px solid #444;
}
[data-theme="dark"] .btn-primary { background: #2563eb; color: #fff; }
[data-theme="dark"] .btn-primary:disabled { background: #35363c; color: #888; }
[data-theme="dark"] .btn-secondary { background: #23242a; color: #fff; }
[data-theme="dark"] .btn-secondary:hover { background: #35363c; color: #fff; }
[data-theme="dark"] .observation-list,
[data-theme="dark"] .progress-content {
  background: #23242a;
  color: #e0e7ef;
  border: 1px solid #35363c;
}
[data-theme="dark"] .observation-item {
  background: #35363c;
  color: #e0e7ef;
  border: 1px solid #444;
}
[data-theme="dark"] .observation-value.clear { color: #60a5fa !important; }
[data-theme="dark"] .observation-value.milky { color: #fbbf24 !important; }
[data-theme="dark"] .observation-value.amber { color: #f59e42 !important; }
[data-theme="dark"] .recommendation-list .recommendation-item {
  background: #23242a;
  color: #e0e7ef;
  border-left: 4px solid #2563eb;
}
[data-theme="dark"] .recommendation-icon {
  color: #fbbf24;
}
[data-theme="dark"] .risk-category.high {
  background: #991b1b;
  color: #fff;
}
[data-theme="dark"] .risk-category.medium {
  background: #bfa600;
  color: #23242a;
}
[data-theme="dark"] .risk-category.low {
  background: #15803d;
  color: #fff;
} 

[data-theme="dark"] .phase-card .phase-name {
  background: #2d2e36 !important;
  color: #e0e7ef !important;
}
[data-theme="dark"] .phase-card .phase-progress {
  background: #23242a !important;
}
[data-theme="dark"] .milestone-item {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
}
[data-theme="dark"] .milestone-icon {
  background: #2d2e36 !important;
  color: #fbbf24 !important;
}
[data-theme="dark"] .milestone-content {
  color: #e0e7ef !important;
}
[data-theme="dark"] .harvest-option {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
}
[data-theme="dark"] .harvest-option.early {
  border-left: 4px solid #f59e0b !important;
}
[data-theme="dark"] .harvest-option.optimal {
  border-left: 4px solid #10b981 !important;
}
[data-theme="dark"] .harvest-option.late {
  border-left: 4px solid #ef4444 !important;
}
[data-theme="dark"] .harvest-label, [data-theme="dark"] .harvest-days {
  color: #e0e7ef !important;
}
[data-theme="dark"] .milestone-title, [data-theme="dark"] .milestone-details, [data-theme="dark"] .milestone-countdown {
  color: #e0e7ef !important;
} 

[data-theme="dark"] .current-status {
  background: #23242a !important;
  border: 1px solid #35363c !important;
  color: #e0e7ef !important;
}
[data-theme="dark"] .bloom-day {
  background: #23242a !important;
  color: #e0e7ef !important;
}
[data-theme="dark"] .progress-circle,
[data-theme="dark"] .progress-ring {
  background: #23242a !important;
}
[data-theme="dark"] .progress-text {
  color: #e0e7ef !important;
}
[data-theme="dark"] .day-number {
  color: #e0e7ef !important;
}
[data-theme="dark"] .day-label {
  color: #a0a0a0 !important;
} 

/* --- DARK MODE: trigger-message, recommendation-action und trend icon Anpassungen --- */
[data-theme="dark"] .trigger-message.low {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
}

[data-theme="dark"] .trigger-message.medium {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
}

[data-theme="dark"] .trigger-message.high {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
}

[data-theme="dark"] .recommendation-action {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
  border-left: 4px solid #2563eb !important;
}

[data-theme="dark"] .trend-item {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
}

[data-theme="dark"] .trend-icon {
  color: #e0e7ef !important;
  text-shadow: 0 1px 2px #000a;
}

[data-theme="dark"] .trend-arrow {
  color: #e0e7ef !important;
  text-shadow: 0 1px 2px #000a;
}

/* --- DARK MODE: manual-trigger, recommendation-item und Empfehlungen Überschrift --- */
[data-theme="dark"] .manual-trigger {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
}

[data-theme="dark"] .manual-trigger h4 {
  color: #e0e7ef !important;
}

[data-theme="dark"] .recommendation-item {
  background: #23242a !important;
  color: #e0e7ef !important;
  border: 1px solid #35363c !important;
  border-left: 4px solid #2563eb !important;
}

[data-theme="dark"] .recommendations h4 {
  color: #e0e7ef !important;
}

[data-theme="dark"] .recommendation-text {
    color: #e0e7ef !important;
}

/* Phase Percentage im Header */
.phase-percentage {
    font-size: 0.8em;
    color: #64748b;
    font-weight: 500;
    margin-left: 8px;
}

[data-theme="dark"] .phase-percentage {
    color: #a0a0a0 !important;
}

/* --- DARK MODE: Alle h4 Elemente im Flowering Widget --- */
[data-theme="dark"] .flowering-widget-container h4,
[data-theme="dark"] .trichome-container h4,
[data-theme="dark"] .trigger-conditions h4,
[data-theme="dark"] .manual-trigger h4,
[data-theme="dark"] .flush-status-details h4,
[data-theme="dark"] .recommendations h4,
[data-theme="dark"] .risk-factors h4 {
  color: #e0e7ef !important;
}

.observation-notes {
    margin-top: 8px;
    font-size: 0.9em;
    color: #6b7280;
    font-style: italic;
}

.observation-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    padding-top: 8px;
    border-top: 1px solid #e5e7eb;
}

.btn-edit, .btn-delete {
    padding: 4px 12px;
    border: none;
    border-radius: 6px;
    font-size: 0.8em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-edit {
    background: #3b82f6;
    color: white;
}

.btn-edit:hover {
    background: #2563eb;
    transform: translateY(-1px);
}

.btn-delete {
    background: #ef4444;
    color: white;
}

.btn-delete:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.observation-location {
    margin-top: 6px;
    font-size: 0.9em;
    color: #059669;
    font-weight: 500;
}

/* Trichom-Segmente Styles */
.trichome-segments-container {
    margin-top: 20px;
    padding: 15px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.trichome-segments-container h5 {
    margin: 0 0 15px 0;
    font-size: 1.1em;
    color: #374151;
    font-weight: 600;
}

.trichome-segments {
    display: flex;
    height: 48px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.trichome-segment {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1em;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    position: relative;
    min-width: 32px;
    padding: 0 8px;
}

.trichome-segment:hover {
    transform: scaleY(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.trichome-segment .segment-label {
    font-size: 0.9em;
    font-weight: 600;
    margin-bottom: 2px;
    letter-spacing: 0.5px;
}

.trichome-segment .segment-value {
    font-size: 1.1em;
    font-weight: 700;
    margin-top: 2px;
}

/* Spezifische Farben für Trichom-Segmente */
.trichome-segment.clear-segment {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.trichome-segment.milky-segment {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.trichome-segment.amber-segment {
    background: linear-gradient(135deg, #ea580c 0%, #c2410c 100%);
}

/* Segment-Labels und Werte */
.trichome-segment .segment-label {
    font-size: 0.7em;
    font-weight: 600;
    margin-bottom: 2px;
}

.trichome-segment .segment-value {
    font-size: 0.8em;
    font-weight: 700;
}

/* Dark Mode für Trichom-Segmente */
[data-theme="dark"] .trichome-segments-container {
    background: #1f2937;
    border-color: #374151;
}

[data-theme="dark"] .trichome-segments-container h5 {
    color: #f3f4f6;
}

[data-theme="dark"] .trichome-segments {
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Trichom-Beobachtungsformular */
.observation-form-container {
    margin: 1rem 0;
}

.form-card.compact {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-card.compact h5 {
    margin: 0 0 1rem 0;
    color: var(--accent);
    font-size: 1.1rem;
}

.form-row.compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-size: 0.85rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: var(--text);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.5rem;
    border: 1px solid var(--card-border);
    border-radius: 4px;
    background: var(--input-bg);
    color: var(--text);
    font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 2px rgba(var(--accent-rgb), 0.2);
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}

.form-actions.compact {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
    margin-top: 1rem;
}

.form-actions.compact .btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .form-row.compact {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .form-actions.compact {
        flex-direction: column;
    }
    
    .form-actions.compact .btn {
        width: 100%;
    }
}

/* ========================================
   FLOWERING WIDGET - BEARBEITEN & LÖSCHEN FORMULARE
   ======================================== */

/* Bearbeiten-Formular */
.edit-form {
    margin-top: 15px;
    padding: 15px;
    background: var(--bg-secondary);
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    box-shadow: var(--shadow);
    animation: slideDown 0.3s ease-out;
}

.edit-form .form-card {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
}

.edit-form h5 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1rem;
    font-weight: 600;
}

.edit-form .form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 10px;
}

.edit-form .form-group {
    display: flex;
    flex-direction: column;
}

.edit-form label {
    font-size: 0.8rem;
    font-weight: 500;
    margin-bottom: 4px;
    color: var(--text-muted);
}

.edit-form input,
.edit-form select,
.edit-form textarea {
    padding: 6px 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 0.85rem;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.edit-form input:focus,
.edit-form select:focus,
.edit-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}

.edit-form .form-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 12px;
}

.edit-form .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* Löschen-Formular */
.delete-form {
    margin-top: 15px;
    padding: 15px;
    background: var(--bg-secondary);
    border: 2px solid var(--danger-color);
    border-radius: 8px;
    box-shadow: var(--shadow);
    animation: slideDown 0.3s ease-out;
}

.delete-form .form-card {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
}

.delete-form h5 {
    color: var(--danger-color);
    margin-bottom: 15px;
    font-size: 1rem;
    font-weight: 600;
}

.delete-form .confirm-text {
    color: var(--text-primary);
    margin-bottom: 15px;
    font-size: 0.9rem;
    line-height: 1.4;
}

.delete-form .form-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.delete-form .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
    border-radius: 4px;
}

.delete-form .btn-delete-confirm {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
}

.delete-form .btn-delete-confirm:hover {
    background: var(--danger-hover);
    border-color: var(--danger-hover);
}

/* Animation für das Einblenden der Formulare */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .edit-form .form-row {
        grid-template-columns: 1fr;
    }
    
    .edit-form .form-actions,
    .delete-form .form-actions {
        flex-direction: column;
    }
    
    .edit-form .btn,
    .delete-form .btn {
        width: 100%;
    }
}

/* Hervorhebung für Bearbeiten-Modus (Test: feste Farbe) */
.observation-item.is-editing {
    border: 2px solid #1976d2 !important;
    background: rgba(25, 118, 210, 0.08) !important;
    box-shadow: 0 0 0 2px #1976d2 !important;
    position: relative;
}
.observation-item.is-editing::before {
    content: 'Bearbeiten';
    position: absolute;
    top: 8px;
    right: 12px;
    background: #1976d2;
    color: #fff;
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 6px;
    z-index: 2;
    opacity: 0.85;
}

/* Hervorhebung für Löschen-Modus (Test: feste Farbe) */
.observation-item.is-deleting {
    border: 2px solid #dc3545 !important;
    background: rgba(220, 53, 69, 0.08) !important;
    box-shadow: 0 0 0 2px #dc3545 !important;
    position: relative;
}
.observation-item.is-deleting::before {
    content: 'Löschen';
    position: absolute;
    top: 8px;
    right: 12px;
    background: #dc3545;
    color: #fff;
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 6px;
    z-index: 2;
    opacity: 0.85;
}

.trichome-observation-card .observation-item.is-editing {
    border: 2px solid #1976d2 !important;
    background: rgba(25, 118, 210, 0.08) !important;
    box-shadow: 0 0 0 2px #1976d2 !important;
    position: relative !important;
    display: block !important;
    min-height: 40px !important;
    z-index: 10 !important;
}
.trichome-observation-card .observation-item.is-editing::before {
    content: 'Bearbeiten';
    position: absolute;
    top: 8px;
    right: 12px;
    background: #1976d2;
    color: #fff;
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 6px;
    z-index: 20;
    opacity: 0.85;
}

.trichome-observation-card .observation-item.is-deleting {
    border: 2px solid #dc3545 !important;
    background: rgba(220, 53, 69, 0.08) !important;
    box-shadow: 0 0 0 2px #dc3545 !important;
    position: relative !important;
    display: block !important;
    min-height: 40px !important;
    z-index: 10 !important;
}
.trichome-observation-card .observation-item.is-deleting::before {
    content: 'Löschen';
    position: absolute;
    top: 8px;
    right: 12px;
    background: #dc3545;
    color: #fff;
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 6px;
    z-index: 20;
    opacity: 0.85;
}

/* Neue Formular-Elemente: Radio-Buttons, Schieberegler und Live-Summenanzeige */

/* Radio-Button Gruppe */
.radio-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 8px;
    margin-top: 8px;
}

.radio-option {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #fff;
}

.radio-option:hover {
    border-color: #667eea;
    background: #f8fafc;
}

.radio-option input[type="radio"] {
    margin-right: 8px;
    accent-color: #667eea;
}

.radio-option input[type="radio"]:checked + .radio-label {
    color: #667eea;
    font-weight: 600;
}

.radio-label {
    font-size: 0.9rem;
    color: #374151;
    cursor: pointer;
}

/* Schieberegler Gruppe */
.slider-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-top: 12px;
}

.slider-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.slider-item label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
}

/* Trichom-Schieberegler */
.trichome-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.trichome-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.trichome-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    background: transparent;
}

/* Schieberegler Farben */
.clear-slider::-webkit-slider-thumb {
    background: #60a5fa;
}

.clear-slider::-moz-range-thumb {
    background: #60a5fa;
}

.milky-slider::-webkit-slider-thumb {
    background: #fbbf24;
}

.milky-slider::-moz-range-thumb {
    background: #fbbf24;
}

.amber-slider::-webkit-slider-thumb {
    background: #f59e42;
}

.amber-slider::-moz-range-thumb {
    background: #f59e42;
}

/* Live-Summenanzeige */
.percentage-summary {
    margin-top: 16px;
    padding: 12px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.sum-display {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.sum-label {
    font-weight: 600;
    color: #374151;
}

.sum-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: #059669;
}

.sum-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 6px;
    color: #92400e;
}

.warning-icon {
    font-size: 1.1rem;
}

.warning-text {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Dark Mode Anpassungen */
[data-theme="dark"] .radio-option {
    background: #23242a;
    border-color: #35363c;
    color: #fff;
}

[data-theme="dark"] .radio-option:hover {
    border-color: #667eea;
    background: #2d2e35;
}

[data-theme="dark"] .radio-label {
    color: #e5e7eb;
}

[data-theme="dark"] .trichome-slider {
    background: #35363c;
}

[data-theme="dark"] .slider-item label {
    color: #e5e7eb;
}

[data-theme="dark"] .percentage-summary {
    background: #2d2e35;
    border-color: #35363c;
}

[data-theme="dark"] .sum-label {
    color: #e5e7eb;
}

[data-theme="dark"] .sum-value {
    color: #10b981;
}

[data-theme="dark"] .sum-warning {
    background: #451a03;
    border-color: #f59e0b;
    color: #fbbf24;
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .radio-group {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .slider-group {
        gap: 12px;
    }
    
    .percentage-summary {
        margin-top: 12px;
        padding: 10px;
    }
}

@media (max-width: 480px) {
    .radio-group {
        grid-template-columns: 1fr;
    }
    
    .slider-item label {
        font-size: 0.85rem;
    }
}

.flush-guideline-recommendation {
    margin-top: 16px;
    background: #f8fafc;
    border-left: 4px solid #3b82f6;
    padding: 12px 16px;
    border-radius: 6px;
    color: #1e293b;
    font-size: 1.05em;
    max-height: 400px;
    overflow-y: auto;
}

.flush-guideline-recommendation .info-icon {
    font-size: 1.3em;
    margin-right: 8px;
}

/* Guidelines-Sektionen */
.guideline-section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e8f0;
}

.guideline-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.guideline-section h5 {
    color: #1e293b;
    font-size: 1.1em;
    font-weight: 600;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.guideline-section h6 {
    color: #374151;
    font-size: 1em;
    font-weight: 600;
    margin: 10px 0 8px 0;
}

.strain-type-section {
    margin-bottom: 15px;
    padding: 12px;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.strain-type-section:last-child {
    margin-bottom: 0;
}

.strain-type-section ul {
    margin: 8px 0;
    padding-left: 20px;
}

.strain-type-section li {
    margin-bottom: 4px;
    line-height: 1.4;
}

.strain-type-section p {
    margin: 8px 0 0 0;
    font-weight: 500;
}

.method-item {
    margin-bottom: 15px;
    padding: 12px;
    background: rgba(34, 197, 94, 0.05);
    border-radius: 6px;
    border-left: 3px solid #22c55e;
}

.method-item:last-child {
    margin-bottom: 0;
}

.method-item h6 {
    color: #15803d;
    margin: 0 0 8px 0;
}

.method-item p {
    margin: 0 0 10px 0;
    line-height: 1.4;
}

.method-pros-cons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 10px;
}

.pros, .cons {
    padding: 8px;
    border-radius: 4px;
}

.pros {
    background: rgba(34, 197, 94, 0.1);
    border-left: 3px solid #22c55e;
}

.cons {
    background: rgba(239, 68, 68, 0.1);
    border-left: 3px solid #ef4444;
}

.pros strong, .cons strong {
    display: block;
    margin-bottom: 6px;
    font-size: 0.9em;
}

.pros ul, .cons ul {
    margin: 0;
    padding-left: 16px;
    font-size: 0.85em;
}

.pros li, .cons li {
    margin-bottom: 3px;
    line-height: 1.3;
}

.error-list, .rule-list {
    margin: 8px 0;
    padding-left: 20px;
}

.error-list li, .rule-list li {
    margin-bottom: 6px;
    line-height: 1.4;
    padding-left: 8px;
}

.error-list li {
    color: #dc2626;
}

.rule-list li {
    color: #059669;
}

/* Dark Theme Anpassungen */
[data-theme="dark"] .flush-guideline-recommendation {
    background: #1e293b;
    border-color: #334155;
    color: #cbd5e1;
}

[data-theme="dark"] .guideline-section {
    border-bottom-color: #334155;
}

[data-theme="dark"] .guideline-section h5 {
    color: #f1f5f9;
}

[data-theme="dark"] .guideline-section h6 {
    color: #e2e8f0;
}

[data-theme="dark"] .strain-type-section {
    background: rgba(102, 126, 234, 0.1);
    border-left-color: #667eea;
}

[data-theme="dark"] .method-item {
    background: rgba(34, 197, 94, 0.1);
    border-left-color: #22c55e;
}

[data-theme="dark"] .method-item h6 {
    color: #4ade80;
}

[data-theme="dark"] .pros {
    background: rgba(34, 197, 94, 0.15);
    border-left-color: #22c55e;
}

[data-theme="dark"] .cons {
    background: rgba(239, 68, 68, 0.15);
    border-left-color: #ef4444;
}

[data-theme="dark"] .error-list li {
    color: #f87171;
}

[data-theme="dark"] .rule-list li {
    color: #4ade80;
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .method-pros-cons {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .flush-guideline-recommendation {
        max-height: 300px;
        font-size: 0.85em;
    }
    
    .guideline-section h5 {
        font-size: 1em;
    }
    
    .guideline-section h6 {
        font-size: 0.9em;
    }
}

/* Neue Guidelines-Container Styles */
.flush-guideline-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.flush-guideline-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.guideline-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f1f5f9;
}

.guideline-header h4 {
    margin: 0;
    font-size: 1.2em;
    font-weight: 600;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.guideline-content {
    font-size: 0.95em;
    line-height: 1.6;
    color: #475569;
}

/* Dark Theme für neue Guidelines-Container */
[data-theme="dark"] .flush-guideline-card {
    background: #1e293b;
    border-color: #334155;
    color: #cbd5e1;
}

[data-theme="dark"] .guideline-header {
    border-bottom-color: #334155;
}

[data-theme="dark"] .guideline-header h4 {
    color: #f1f5f9;
}

[data-theme="dark"] .guideline-content {
    color: #cbd5e1;
}

/* Responsive für neue Guidelines-Container */
@media (max-width: 768px) {
    .flush-guideline-card {
        padding: 15px;
        margin: 15px 0;
    }
    
    .guideline-header h4 {
        font-size: 1.1em;
    }
    
    .guideline-content {
        font-size: 0.9em;
    }
}

/* Event-Formular und Marker-Formular Styles */
.event-form-container,
.marker-form-container {
    margin-bottom: 2rem;
    animation: slideDown 0.3s ease-out;
}

.event-form-card,
.marker-form-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.form-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.btn-close:hover {
    background: var(--danger-bg);
    color: var(--danger-color);
}

.event-form,
.marker-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--input-bg);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-bg);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.form-actions .btn {
    min-width: 120px;
}

/* Animation für das Formular */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Marker-Bearbeitungs- und Löschformulare */
.marker-edit-form,
.marker-delete-form {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.marker-edit-form .form-header,
.marker-delete-form .form-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.marker-edit-form .form-header h5,
.marker-delete-form .form-header h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.delete-confirmation {
    text-align: center;
}

.delete-confirmation p {
    margin-bottom: 1rem;
}

.delete-warning {
    color: var(--danger-color);
    font-weight: 500;
}

/* Responsive Anpassungen */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .form-actions .btn {
        width: 100%;
    }
}

/* Flush-Guidelines Styles */
.flush-guidelines, .trichome-guidelines {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border: 1px solid #e2e8f0;
}

.flush-guidelines h4, .trichome-guidelines h4 {
    margin: 0 0 20px 0;
    color: #1e293b;
    font-size: 1.4em;
}

/* Trichome-Guidelines spezifische Styles */
.trichome-guidelines {
    border-left: 4px solid #8b5cf6; /* Lila für Trichome */
}

.trichome-guidelines h4 {
    color: #8b5cf6;
}

.guidelines-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 20px;
    background: #f8fafc;
    border-radius: 8px;
    padding: 4px;
}

.guideline-tab {
    flex: 1;
    padding: 10px 15px;
    border: none;
    background: transparent;
    border-radius: 6px;
    font-weight: 600;
    color: #64748b;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.guideline-tab:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.guideline-tab.active {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.guidelines-content {
    min-height: 200px;
}

.guideline-panel {
    display: none;
    animation: fadeIn 0.3s ease;
}

.guideline-panel.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.guideline-panel h6 {
    margin: 0 0 15px 0;
    color: #1e293b;
    font-size: 1.1em;
    font-weight: 600;
}

.guideline-panel ul {
    margin: 0 0 15px 0;
    padding-left: 20px;
}

.guideline-panel li {
    margin-bottom: 8px;
    color: #374151;
    line-height: 1.5;
}

.guideline-panel p {
    margin: 0 0 10px 0;
    color: #64748b;
    font-size: 0.9em;
}

.strain-type-section {
    background: #f8fafc;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.strain-type-section:last-child {
    margin-bottom: 0;
}

.strain-type-section ul {
    margin: 10px 0;
}

.strain-type-section li {
    margin-bottom: 5px;
}

.strain-type-section p {
    margin: 10px 0 0 0;
    font-weight: 500;
}

.no-guidelines {
    text-align: center;
    padding: 40px 20px;
    color: #64748b;
}

.no-guidelines p {
    margin: 0;
    font-style: italic;
}

/* Dark Mode für Guidelines */
[data-theme="dark"] .flush-guidelines, [data-theme="dark"] .trichome-guidelines {
    background: #1e293b;
    border-color: #334155;
}

[data-theme="dark"] .flush-guidelines h4, [data-theme="dark"] .trichome-guidelines h4 {
    color: #f1f5f9;
}

/* Dark Mode für Trichome-Guidelines spezifische Styles */
[data-theme="dark"] .trichome-guidelines {
    border-left-color: #a78bfa; /* Helleres Lila für Dark Mode */
}

[data-theme="dark"] .trichome-guidelines h4 {
    color: #a78bfa;
}

[data-theme="dark"] .guidelines-tabs {
    background: #334155;
}

[data-theme="dark"] .guideline-tab {
    color: #cbd5e1;
}

[data-theme="dark"] .guideline-tab:hover {
    background: rgba(102, 126, 234, 0.2);
    color: #93c5fd;
}

[data-theme="dark"] .guideline-tab.active {
    background: #667eea;
    color: white;
}

[data-theme="dark"] .guideline-panel h6 {
    color: #f1f5f9;
}

[data-theme="dark"] .guideline-panel li {
    color: #cbd5e1;
}

[data-theme="dark"] .guideline-panel p {
    color: #94a3b8;
}

[data-theme="dark"] .strain-type-section {
    background: #334155;
    border-left-color: #667eea;
}

[data-theme="dark"] .no-guidelines {
    color: #94a3b8;
}

/* Responsive für Guidelines */
@media (max-width: 768px) {
    .guidelines-tabs {
        flex-direction: column;
        gap: 2px;
    }
    
    .guideline-tab {
        text-align: center;
        padding: 12px 10px;
    }
    
    .flush-guidelines, .trichome-guidelines {
        padding: 20px;
    }
    
    .guideline-panel {
        font-size: 0.9em;
    }
}

/* ===== Beleuchtungs-Integration ===== */

.lighting-container {
    padding: 25px;
    margin: 0;
}

/* Phase 6 Features Container */
.phase6-features {
    margin-bottom: 30px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.phase6-features .lighting-card {
    margin-bottom: 0;
}

/* Phase 6 Features Hinweise */
.ml-notice, .iot-notice {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 15px;
    font-size: 0.9em;
}

.ml-notice i, .iot-notice i {
    color: #93c5fd;
}

[data-theme="dark"] .ml-notice, [data-theme="dark"] .iot-notice {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
    color: #e0e7ff;
}

[data-theme="dark"] .ml-notice i, [data-theme="dark"] .iot-notice i {
    color: #818cf8;
}

.lighting-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.lighting-header h3 {
    margin: 0;
    color: #495057;
    font-size: 1.5rem;
    font-weight: 600;
}

.lighting-status {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #28a745;
}

.status-indicator.warning {
    background: #ffc107;
}

.status-indicator.error {
    background: #dc3545;
}

.status-text {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

/* Beleuchtungs-Übersicht */
.lighting-overview-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    margin-bottom: 35px;
}

.lighting-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.lighting-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.lighting-card.ppfd-card.optimal {
    border-color: #28a745;
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.lighting-card.ppfd-card.low {
    border-color: #ffc107;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.lighting-card.ppfd-card.high {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

.lighting-card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 18px;
    font-weight: 600;
    color: #495057;
}

.lighting-card-header i {
    font-size: 1.3rem;
    color: #6c757d;
}

.lighting-card-content {
    text-align: center;
}

.lighting-value {
    font-size: 2.2rem;
    font-weight: 700;
    color: #212529;
    margin-bottom: 8px;
}

.lighting-range {
    font-size: 0.95rem;
    color: #6c757d;
    margin-bottom: 10px;
}

.lighting-message {
    font-size: 0.9rem;
    color: #495057;
    font-style: italic;
}

/* Beleuchtungs-Einstellungen */
.lighting-settings-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 35px;
}

.lighting-settings-section h4 {
    margin-bottom: 25px;
    color: #495057;
    font-weight: 600;
}

.lighting-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.lighting-setting-group {
    display: flex;
    flex-direction: column;
}

.lighting-setting-group label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 10px;
    font-size: 0.95rem;
}

.lighting-setting-group input {
    padding: 14px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.lighting-setting-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.lighting-setting-group.full-width {
    grid-column: 1 / -1;
}

.lighting-input-hint {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
    font-style: italic;
}

.lighting-input-hint i {
    margin-right: 4px;
    color: #667eea;
}

.lighting-actions {
    display: flex;
    gap: 18px;
    justify-content: flex-start;
}

.lighting-actions .btn {
    padding: 14px 24px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
}

/* Beleuchtungs-Guidelines */
.lighting-guidelines-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.lighting-guidelines-section h4 {
    margin-bottom: 25px;
    color: #495057;
    font-weight: 600;
}

.lighting-guidelines-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
}

.guideline-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 25px;
    border-left: 4px solid #667eea;
}

.guideline-card h5 {
    margin-bottom: 18px;
    color: #495057;
    font-weight: 600;
}

.guideline-item {
    margin-bottom: 12px;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.guideline-item:last-child {
    border-bottom: none;
}

/* ===== Dark Mode Styles für Beleuchtungs-Integration ===== */

[data-theme="dark"] .lighting-container {
    background: #1e293b;
    border-radius: 15px;
    margin: 0;
}

[data-theme="dark"] .lighting-header {
    border-bottom-color: #334155;
}

[data-theme="dark"] .lighting-header h3 {
    color: #f1f5f9;
}

[data-theme="dark"] .status-text {
    color: #94a3b8;
}

[data-theme="dark"] .lighting-card {
    background: #334155;
    border-color: #475569;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .lighting-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: #667eea;
}

[data-theme="dark"] .lighting-card.ppfd-card.optimal {
    border-color: #22c55e;
    background: linear-gradient(135deg, #14532d 0%, #166534 100%);
}

[data-theme="dark"] .lighting-card.ppfd-card.low {
    border-color: #fbbf24;
    background: linear-gradient(135deg, #78350f 0%, #92400e 100%);
}

[data-theme="dark"] .lighting-card.ppfd-card.high {
    border-color: #ef4444;
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
}

[data-theme="dark"] .lighting-card-header {
    color: #f1f5f9;
}

[data-theme="dark"] .lighting-card-header i {
    color: #94a3b8;
}

[data-theme="dark"] .lighting-value {
    color: #f8fafc;
}

[data-theme="dark"] .lighting-range {
    color: #cbd5e1;
}

[data-theme="dark"] .lighting-message {
    color: #e2e8f0;
}

[data-theme="dark"] .lighting-settings-section {
    background: #334155;
    border-color: #475569;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .lighting-settings-section h4 {
    color: #f1f5f9;
}

[data-theme="dark"] .lighting-setting-group label {
    color: #e2e8f0;
}

[data-theme="dark"] .lighting-setting-group input {
    background: #1e293b;
    border-color: #475569;
    color: #f8fafc;
}

[data-theme="dark"] .lighting-setting-group input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

[data-theme="dark"] .lighting-setting-group input::placeholder {
    color: #64748b;
}

[data-theme="dark"] .lighting-input-hint {
    color: #9ca3af;
}

[data-theme="dark"] .lighting-input-hint i {
    color: #818cf8;
}

[data-theme="dark"] .lighting-guidelines-section {
    background: #334155;
    border-color: #475569;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .lighting-guidelines-section h4 {
    color: #f1f5f9;
}

[data-theme="dark"] .guideline-card {
    background: #1e293b;
    border-left-color: #667eea;
}

[data-theme="dark"] .guideline-card h5 {
    color: #f1f5f9;
}

[data-theme="dark"] .guideline-item {
    border-bottom-color: #475569;
    color: #e2e8f0;
}

/* Responsive für Beleuchtungs-Integration */
@media (max-width: 768px) {
    .lighting-container {
        padding: 20px;
    }
    
    .lighting-header {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .lighting-overview-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .lighting-settings-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .lighting-guidelines-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .lighting-actions {
        flex-direction: column;
        gap: 12px;
    }
    
    .lighting-card {
        padding: 20px;
    }
    
    .lighting-settings-section,
    .lighting-guidelines-section {
        padding: 25px;
    }
}

/* ===== Smart Dimming Features ===== */

.smart-dimming-card {
    border-color: #667eea !important;
    background: linear-gradient(135deg, #e8f2ff 0%, #d1e7ff 100%) !important;
    position: relative;
    overflow: hidden;
}

.smart-dimming-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.smart-dimming-recommendation {
    border-color: #ffc107 !important;
    background: linear-gradient(135deg, #fff8e1 0%, #fff3cd 100%) !important;
}

.smart-dimming-status {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: #28a745;
    color: white;
}

.adjustment-summary {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
}

.adjustment-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 8px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
}

.adjustment-label {
    font-size: 0.75rem;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 2px;
}

.adjustment-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #dc3545;
}

.smart-dimming-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.smart-dimming-actions .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
}

.recommendation-text {
    margin-bottom: 20px;
}

.recommendation-text p {
    margin-bottom: 15px;
    color: #495057;
    line-height: 1.5;
}

.recommendation-benefits {
    list-style: none;
    padding: 0;
    margin: 0;
}

.recommendation-benefits li {
    padding: 6px 0;
    padding-left: 20px;
    position: relative;
    color: #495057;
}

.recommendation-benefits li::before {
    content: "✅";
    position: absolute;
    left: 0;
    top: 6px;
}

.recommendation-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.recommendation-actions .btn {
    padding: 10px 20px;
    font-weight: 600;
}

/* Dark Mode für Smart Dimming */
[data-theme="dark"] .smart-dimming-card {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
    border-color: #667eea !important;
}

[data-theme="dark"] .smart-dimming-recommendation {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
    border-color: #ffc107 !important;
}

[data-theme="dark"] .adjustment-item {
    background: rgba(45, 55, 72, 0.7);
}

[data-theme="dark"] .adjustment-label {
    color: #a0aec0;
}

[data-theme="dark"] .adjustment-value {
    color: #fc8181;
}

[data-theme="dark"] .recommendation-text p {
    color: #e2e8f0;
}

[data-theme="dark"] .recommendation-benefits li {
    color: #e2e8f0;
}

/* Responsive Design für Smart Dimming */
@media (max-width: 768px) {
    .smart-dimming-status {
        flex-direction: column;
        gap: 10px;
    }
    
    .adjustment-summary {
        grid-template-columns: 1fr;
    }
    
    .smart-dimming-actions,
    .recommendation-actions {
        flex-direction: column;
    }
    
    .adjustment-item {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }
}

/* ===== Guidelines Modal Styles ===== */

.guidelines-container {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
}

.guidelines-section {
    margin-bottom: 40px;
}

.guidelines-section h4 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 25px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.guidelines-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.guideline-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.guideline-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.guideline-card h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.guideline-content p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 15px;
}

.guideline-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.guideline-table .table-header,
.guideline-table .table-row {
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

.guideline-table .table-header {
    background: #667eea;
    color: white;
    font-weight: 600;
}

.guideline-table .table-row:last-child {
    border-bottom: none;
}

.guideline-table span {
    flex: 1;
    padding: 12px;
    text-align: center;
    font-size: 0.9rem;
}

.guideline-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.guideline-list li {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    color: #495057;
}

.guideline-list li:last-child {
    border-bottom: none;
}

.guideline-list li strong {
    color: #667eea;
}

/* Phase-spezifische Guidelines */
.phase-guidelines-content {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.current-phase-info {
    text-align: center;
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
}

.current-phase-info h5 {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 10px;
}

.phase-description {
    color: #6c757d;
    font-style: italic;
    margin: 0;
}

.guideline-values {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.value-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.value-item:last-child {
    border-bottom: none;
}

.value-item .label {
    font-weight: 600;
    color: #495057;
}

.value-item .value {
    color: #667eea;
    font-weight: 600;
}

.phase-tips {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

.phase-tips h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tips-list li {
    padding: 8px 0;
    padding-left: 25px;
    position: relative;
    color: #495057;
}

.tips-list li::before {
    content: "💡";
    position: absolute;
    left: 0;
    top: 8px;
}

/* Tips Grid */
.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tip-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.tip-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.tip-card h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 15px;
}

.tip-list {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.tip-list li {
    padding: 6px 0;
    padding-left: 20px;
    position: relative;
    color: #6c757d;
    font-size: 0.9rem;
}

.tip-list li::before {
    content: "•";
    position: absolute;
    left: 0;
    top: 6px;
    color: #667eea;
    font-weight: bold;
}

/* Modal Footer */
.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 20px;
    background: #f8f9fa;
}

.modal-footer .btn {
    padding: 10px 20px;
    font-weight: 600;
}

/* Dark Mode für Guidelines Modal */
[data-theme="dark"] .guidelines-container {
    background: #1a1a1a;
}

[data-theme="dark"] .guidelines-section h4 {
    color: #e9ecef;
    border-bottom-color: #404040;
}

[data-theme="dark"] .guideline-card {
    background: #2d2d2d;
    border-left-color: #667eea;
}

[data-theme="dark"] .guideline-card h5 {
    color: #e9ecef;
}

[data-theme="dark"] .guideline-content p {
    color: #adb5bd;
}

[data-theme="dark"] .guideline-table {
    background: #404040;
}

[data-theme="dark"] .guideline-table .table-header {
    background: #667eea;
}

[data-theme="dark"] .guideline-table .table-row {
    border-bottom-color: #555555;
}

[data-theme="dark"] .guideline-list li {
    color: #e2e8f0;
    border-bottom-color: #555555;
}

[data-theme="dark"] .guideline-list li strong {
    color: #667eea;
}

[data-theme="dark"] .phase-guidelines-content {
    background: #2d2d2d;
}

[data-theme="dark"] .current-phase-info {
    border-bottom-color: #404040;
}

[data-theme="dark"] .current-phase-info h5 {
    color: #667eea;
}

[data-theme="dark"] .phase-description {
    color: #adb5bd;
}

[data-theme="dark"] .value-item {
    border-bottom-color: #404040;
}

[data-theme="dark"] .value-item .label {
    color: #e9ecef;
}

[data-theme="dark"] .value-item .value {
    color: #667eea;
}

[data-theme="dark"] .phase-tips {
    border-top-color: #404040;
}

[data-theme="dark"] .phase-tips h6 {
    color: #e9ecef;
}

[data-theme="dark"] .tips-list li {
    color: #e2e8f0;
}

[data-theme="dark"] .tip-card {
    background: #2d2d2d;
    border-color: #404040;
}

[data-theme="dark"] .tip-card:hover {
    border-color: #667eea;
}

[data-theme="dark"] .tip-card h5 {
    color: #e9ecef;
}

[data-theme="dark"] .tip-list li {
    color: #adb5bd;
}

[data-theme="dark"] .tip-list li::before {
    color: #667eea;
}

[data-theme="dark"] .modal-footer {
    background: #2d2d2d;
    border-top-color: #404040;
}

/* Responsive Design für Guidelines Modal */
@media (max-width: 768px) {
    .guidelines-container {
        padding: 15px;
    }
    
    .guidelines-grid {
        grid-template-columns: 1fr;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
    
    .guideline-table .table-header,
    .guideline-table .table-row {
        flex-direction: column;
    }
    
    .guideline-table span {
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }
    
    .guideline-table span:last-child {
        border-bottom: none;
    }
    
    .value-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* ===== Energieverbrauch-Tracking Styles ===== */

.energy-card {
    border-color: #ffc107 !important;
    background: linear-gradient(135deg, #fff8e1 0%, #fff3cd 100%) !important;
    position: relative;
    overflow: hidden;
}

.energy-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffc107, #ff9800);
}

.energy-summary {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 20px;
}

.energy-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    border-left: 3px solid #ffc107;
}

.energy-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.energy-value {
    font-weight: 700;
    color: #ffc107;
    font-size: 1rem;
}

.energy-cost {
    font-weight: 600;
    color: #28a745;
    font-size: 0.9rem;
    background: rgba(40, 167, 69, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
}

.energy-efficiency {
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.efficiency-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #495057;
}

.efficiency-rating {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.efficiency-rating.excellent {
    background: #28a745;
    color: white;
}

.efficiency-rating.good {
    background: #17a2b8;
    color: white;
}

.efficiency-rating.average {
    background: #ffc107;
    color: #212529;
}

.efficiency-rating.poor {
    background: #dc3545;
    color: white;
}

.efficiency-details {
    font-size: 0.85rem;
    color: #6c757d;
    text-align: center;
}

.energy-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.energy-actions .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Dark Mode für Energieverbrauch */
[data-theme="dark"] .energy-card {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
    border-color: #ffc107 !important;
}

[data-theme="dark"] .energy-item {
    background: rgba(45, 55, 72, 0.7);
    border-left-color: #ffc107;
}

[data-theme="dark"] .energy-label {
    color: #e9ecef;
}

[data-theme="dark"] .energy-value {
    color: #ffc107;
}

[data-theme="dark"] .energy-cost {
    color: #4ade80;
    background: rgba(74, 222, 128, 0.1);
}

[data-theme="dark"] .energy-efficiency {
    background: rgba(45, 55, 72, 0.8);
    border-color: #404040;
}

[data-theme="dark"] .efficiency-header {
    color: #e9ecef;
}

[data-theme="dark"] .efficiency-details {
    color: #adb5bd;
}

/* Responsive Design für Energieverbrauch */
@media (max-width: 768px) {
    .energy-summary {
        gap: 8px;
    }
    
    .energy-item {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
    
    .energy-actions {
        flex-direction: column;
    }
    
    .efficiency-header {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }
}

/* ===== Automatische Anpassungs-Benachrichtigungen ===== */

.auto-adjustment-notification {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
    animation: slideInFromTop 0.5s ease-out;
}

.auto-adjustment-notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4ade80, #22c55e);
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 1.1rem;
}

.notification-header i {
    margin-right: 8px;
    color: #4ade80;
}

.notification-header .btn-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.notification-header .btn-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.notification-content p {
    margin-bottom: 15px;
    line-height: 1.5;
}

.notification-content strong {
    color: #4ade80;
}

.adjustment-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.adjustment-details div {
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.confidence-indicator {
    text-align: center;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
}

.confidence-indicator span {
    color: #4ade80;
}

/* Animation für Benachrichtigungen */
@keyframes slideInFromTop {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Dark Mode für Benachrichtigungen */
[data-theme="dark"] .auto-adjustment-notification {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

[data-theme="dark"] .adjustment-details div {
    background: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .confidence-indicator {
    background: rgba(255, 255, 255, 0.05);
}

/* Responsive Design für Benachrichtigungen */
@media (max-width: 768px) {
    .auto-adjustment-notification {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .notification-header {
        font-size: 1rem;
    }
    
    .adjustment-details {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .adjustment-details div {
        font-size: 0.85rem;
        padding: 6px 10px;
    }
}

/* ===== AI-Empfehlungen Styles ===== */

.ai-recommendations-card {
    border-color: #8b5cf6 !important;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%) !important;
    position: relative;
    overflow: hidden;
}

.ai-recommendations-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #8b5cf6, #a855f7);
}

.ai-confidence {
    display: flex;
    align-items: center;
    gap: 10px;
}

.confidence-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.confidence-badge.excellent {
    background: #10b981;
    color: white;
}

.confidence-badge.good {
    background: #3b82f6;
    color: white;
}

.confidence-badge.average {
    background: #f59e0b;
    color: white;
}

.confidence-badge.poor {
    background: #ef4444;
    color: white;
}

.ai-recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.ai-recommendation-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border-left: 4px solid #e5e7eb;
    transition: all 0.3s ease;
}

.ai-recommendation-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ai-recommendation-item.high {
    border-left-color: #ef4444;
}

.ai-recommendation-item.medium {
    border-left-color: #f59e0b;
}

.ai-recommendation-item.low {
    border-left-color: #10b981;
}

.recommendation-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.priority-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-badge.high {
    background: #fee2e2;
    color: #dc2626;
}

.priority-badge.medium {
    background: #fef3c7;
    color: #d97706;
}

.priority-badge.low {
    background: #d1fae5;
    color: #059669;
}

.recommendation-header h6 {
    margin: 0;
    color: #374151;
    font-weight: 600;
    font-size: 1rem;
}

.recommendation-content p {
    margin-bottom: 10px;
    color: #6b7280;
    line-height: 1.5;
}

.recommendation-actions {
    display: flex;
    flex-direction: column;
    gap: 5px;
    font-size: 0.85rem;
}

.action-text {
    color: #374151;
    font-weight: 500;
}

.improvement-text {
    color: #059669;
    font-weight: 500;
}

.ai-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.ai-actions .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
    font-weight: 600;
}

/* Dark Mode für AI-Empfehlungen */
[data-theme="dark"] .ai-recommendations-card {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
    border-color: #8b5cf6 !important;
}

[data-theme="dark"] .ai-recommendation-item {
    background: #374151;
    border-left-color: #4b5563;
}

[data-theme="dark"] .ai-recommendation-item.high {
    border-left-color: #ef4444;
}

[data-theme="dark"] .ai-recommendation-item.medium {
    border-left-color: #f59e0b;
}

[data-theme="dark"] .ai-recommendation-item.low {
    border-left-color: #10b981;
}

[data-theme="dark"] .recommendation-header h6 {
    color: #e5e7eb;
}

[data-theme="dark"] .recommendation-content p {
    color: #9ca3af;
}

[data-theme="dark"] .action-text {
    color: #e5e7eb;
}

[data-theme="dark"] .improvement-text {
    color: #34d399;
}

/* Responsive Design für AI-Empfehlungen */
@media (max-width: 768px) {
    .ai-recommendations-list {
        gap: 10px;
    }
    
    .ai-recommendation-item {
        padding: 12px;
    }
    
    .recommendation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .recommendation-actions {
        font-size: 0.8rem;
    }
    
    .ai-actions {
        flex-direction: column;
    }
}

/* ===== Phase 5: Smart Scheduling & Predictive Analytics ===== */

/* Smart Schedule Card */
.smart-schedule-card {
    border-color: #10b981 !important;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%) !important;
    position: relative;
    overflow: hidden;
}

.smart-schedule-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #10b981, #059669);
}

.schedule-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge.active {
    background: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.schedule-info h6 {
    margin: 0 0 8px 0;
    color: #065f46;
    font-weight: 600;
    font-size: 1.1rem;
}

.schedule-info p {
    margin: 0 0 15px 0;
    color: #047857;
    font-size: 0.9rem;
    line-height: 1.4;
}

.schedule-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 8px;
    border-left: 3px solid #10b981;
}

.schedule-item .label {
    font-weight: 600;
    color: #065f46;
    font-size: 0.85rem;
}

.schedule-item .value {
    color: #047857;
    font-weight: 600;
    font-size: 0.9rem;
}

.schedule-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

/* Growth Prediction Card */
.growth-prediction-card {
    border-color: #3b82f6 !important;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
    position: relative;
    overflow: hidden;
}

.growth-prediction-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #2563eb);
}

.prediction-confidence {
    display: flex;
    align-items: center;
    gap: 10px;
}

.prediction-summary p {
    margin: 0 0 15px 0;
    color: #1e40af;
    font-size: 0.9rem;
    text-align: center;
}

.prediction-chart {
    margin-bottom: 20px;
}

.chart-container {
    display: flex;
    justify-content: space-between;
    align-items: end;
    height: 120px;
    padding: 10px 0;
    gap: 8px;
}

.prediction-day {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 60px;
}

.day-label {
    font-size: 0.7rem;
    color: #1e40af;
    font-weight: 600;
    margin-bottom: 8px;
    text-align: center;
}

.prediction-bars {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
    height: 60px;
    margin-bottom: 8px;
}

.height-bar {
    background: linear-gradient(to top, #3b82f6, #60a5fa);
    border-radius: 2px;
    min-height: 4px;
    transition: height 0.3s ease;
}

.ppfd-bar {
    background: linear-gradient(to top, #10b981, #34d399);
    border-radius: 2px;
    min-height: 4px;
    transition: height 0.3s ease;
}

.prediction-values {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    font-size: 0.7rem;
}

.height-value {
    color: #3b82f6;
    font-weight: 600;
}

.ppfd-value {
    color: #10b981;
    font-weight: 600;
}

.prediction-actions {
    display: flex;
    justify-content: center;
}

/* Problem Prediction Card */
.problem-prediction-card {
    border-color: #ef4444 !important;
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
    position: relative;
    overflow: hidden;
}

.problem-prediction-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ef4444, #dc2626);
}

.problem-count {
    display: flex;
    align-items: center;
    gap: 10px;
}

.count-badge {
    background: #ef4444;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    min-width: 20px;
    text-align: center;
}

.problems-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.problem-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    border-left: 4px solid #e5e7eb;
    transition: all 0.3s ease;
}

.problem-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.problem-item.high {
    border-left-color: #ef4444;
}

.problem-item.medium {
    border-left-color: #f59e0b;
}

.problem-item.low {
    border-left-color: #10b981;
}

.problem-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.severity-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.severity-badge.high {
    background: #fee2e2;
    color: #dc2626;
}

.severity-badge.medium {
    background: #fef3c7;
    color: #d97706;
}

.severity-badge.low {
    background: #d1fae5;
    color: #059669;
}

.probability {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 600;
}

.problem-content p {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 0.9rem;
    line-height: 1.4;
}

.problem-recommendation {
    font-size: 0.85rem;
    color: #059669;
    background: rgba(16, 185, 129, 0.1);
    padding: 8px;
    border-radius: 6px;
    border-left: 3px solid #10b981;
}

/* Harvest Prediction Card */
.harvest-prediction-card {
    border-color: #8b5cf6 !important;
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%) !important;
    position: relative;
    overflow: hidden;
}

.harvest-prediction-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #8b5cf6, #7c3aed);
}

.harvest-confidence {
    display: flex;
    align-items: center;
    gap: 10px;
}

.harvest-info {
    margin-bottom: 20px;
}

.harvest-date, .harvest-window {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(139, 92, 246, 0.2);
}

.harvest-date:last-child, .harvest-window:last-child {
    border-bottom: none;
}

.harvest-date .label, .harvest-window .label {
    font-weight: 600;
    color: #581c87;
    font-size: 0.9rem;
}

.harvest-date .value, .harvest-window .value {
    color: #7c3aed;
    font-weight: 700;
    font-size: 1rem;
}

.harvest-timeline {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.timeline-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    flex: 1;
    text-align: center;
    transition: all 0.3s ease;
}

.timeline-item:hover {
    transform: translateY(-2px);
}

.timeline-item.early {
    background: rgba(16, 185, 129, 0.1);
    border: 2px solid #10b981;
}

.timeline-item.optimal {
    background: rgba(139, 92, 246, 0.1);
    border: 2px solid #8b5cf6;
}

.timeline-item.late {
    background: rgba(245, 158, 11, 0.1);
    border: 2px solid #f59e0b;
}

.timeline-label {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.timeline-item.early .timeline-label {
    color: #065f46;
}

.timeline-item.optimal .timeline-label {
    color: #581c87;
}

.timeline-item.late .timeline-label {
    color: #92400e;
}

.timeline-day {
    font-size: 0.9rem;
    font-weight: 700;
}

.timeline-item.early .timeline-day {
    color: #047857;
}

.timeline-item.optimal .timeline-day {
    color: #7c3aed;
}

.timeline-item.late .timeline-day {
    color: #d97706;
}

/* Dark Mode für Phase 5 */
[data-theme="dark"] .smart-schedule-card {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
    border-color: #10b981 !important;
}

[data-theme="dark"] .schedule-info h6 {
    color: #34d399;
}

[data-theme="dark"] .schedule-info p {
    color: #6ee7b7;
}

[data-theme="dark"] .schedule-item {
    background: rgba(16, 185, 129, 0.1);
    border-left-color: #10b981;
}

[data-theme="dark"] .schedule-item .label {
    color: #34d399;
}

[data-theme="dark"] .schedule-item .value {
    color: #6ee7b7;
}

[data-theme="dark"] .growth-prediction-card {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    border-color: #3b82f6 !important;
}

[data-theme="dark"] .prediction-summary p {
    color: #93c5fd;
}

[data-theme="dark"] .day-label {
    color: #93c5fd;
}

[data-theme="dark"] .height-value {
    color: #60a5fa;
}

[data-theme="dark"] .ppfd-value {
    color: #34d399;
}

[data-theme="dark"] .problem-prediction-card {
    background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%) !important;
    border-color: #ef4444 !important;
}

[data-theme="dark"] .problem-item {
    background: #374151;
    border-left-color: #4b5563;
}

[data-theme="dark"] .problem-content p {
    color: #e5e7eb;
}

[data-theme="dark"] .problem-recommendation {
    background: rgba(16, 185, 129, 0.1);
    border-left-color: #10b981;
    color: #34d399;
}

[data-theme="dark"] .harvest-prediction-card {
    background: linear-gradient(135deg, #581c87 0%, #7c3aed 100%) !important;
    border-color: #8b5cf6 !important;
}

[data-theme="dark"] .harvest-date .label, 
[data-theme="dark"] .harvest-window .label {
    color: #c4b5fd;
}

[data-theme="dark"] .harvest-date .value, 
[data-theme="dark"] .harvest-window .value {
    color: #a78bfa;
}

[data-theme="dark"] .harvest-date, 
[data-theme="dark"] .harvest-window {
    border-bottom-color: rgba(139, 92, 246, 0.3);
}

[data-theme="dark"] .timeline-item.early {
    background: rgba(16, 185, 129, 0.1);
    border-color: #10b981;
}

[data-theme="dark"] .timeline-item.optimal {
    background: rgba(139, 92, 246, 0.1);
    border-color: #8b5cf6;
}

[data-theme="dark"] .timeline-item.late {
    background: rgba(245, 158, 11, 0.1);
    border-color: #f59e0b;
}

/* Responsive Design für Phase 5 */
@media (max-width: 768px) {
    .schedule-details {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .schedule-item {
        padding: 6px 10px;
    }
    
    .chart-container {
        height: 100px;
        gap: 4px;
    }
    
    .prediction-day {
        max-width: 50px;
    }
    
    .day-label {
        font-size: 0.65rem;
    }
    
    .prediction-values {
        font-size: 0.65rem;
    }
    
    .harvest-timeline {
        flex-direction: column;
        gap: 8px;
    }
    
    .timeline-item {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }
    
    .problems-list {
        gap: 8px;
    }
    
    .problem-item {
        padding: 10px;
    }
    
    .problem-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

/* ===== Phase 6: Advanced ML & IoT Integration ===== */

/* Pattern Recognition Card */
.pattern-recognition-card {
    border-color: #06b6d4 !important;
    background: linear-gradient(135deg, #ecfeff 0%, #cffafe 100%) !important;
    position: relative;
    overflow: hidden;
}

.pattern-recognition-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #06b6d4, #0891b2);
}

.pattern-count {
    display: flex;
    align-items: center;
    gap: 10px;
}

.patterns-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.pattern-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    border-left: 4px solid #06b6d4;
    transition: all 0.3s ease;
}

.pattern-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pattern-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.pattern-type {
    background: #ecfeff;
    color: #0891b2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.confidence {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 600;
}

.pattern-content p {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 0.9rem;
    line-height: 1.4;
}

.pattern-data {
    font-size: 0.85rem;
    color: #0891b2;
    background: rgba(6, 182, 212, 0.1);
    padding: 8px;
    border-radius: 6px;
    border-left: 3px solid #06b6d4;
}

/* Anomaly Detection Card */
.anomaly-detection-card {
    border-color: #f97316 !important;
    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%) !important;
    position: relative;
    overflow: hidden;
}

.anomaly-detection-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #f97316, #ea580c);
}

.anomaly-count {
    display: flex;
    align-items: center;
    gap: 10px;
}

.anomalies-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.anomaly-item {
    background: white;
    border-radius: 8px;
    padding: 12px;
    border-left: 4px solid #e5e7eb;
    transition: all 0.3s ease;
}

.anomaly-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.anomaly-item.high {
    border-left-color: #ef4444;
}

.anomaly-item.medium {
    border-left-color: #f97316;
}

.anomaly-item.low {
    border-left-color: #10b981;
}

.anomaly-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.anomaly-content p {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 0.9rem;
    line-height: 1.4;
}

.anomaly-timestamp {
    font-size: 0.8rem;
    color: #6b7280;
    font-style: italic;
}

/* IoT Status Card */
.iot-status-card {
    border-color: #6366f1 !important;
    background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%) !important;
    position: relative;
    overflow: hidden;
}

.iot-status-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #6366f1, #4f46e5);
}

.iot-status {
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge.active {
    background: #10b981;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.inactive {
    background: #6b7280;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.iot-info {
    margin-bottom: 20px;
}

.sensor-count, .last-sync {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid rgba(99, 102, 241, 0.2);
}

.sensor-count:last-child, .last-sync:last-child {
    border-bottom: none;
}

.sensor-count .label, .last-sync .label {
    font-weight: 600;
    color: #3730a3;
    font-size: 0.9rem;
}

.sensor-count .value, .last-sync .value {
    color: #6366f1;
    font-weight: 600;
    font-size: 0.9rem;
}

.iot-data {
    margin-bottom: 20px;
}

.iot-data h6 {
    margin: 0 0 12px 0;
    color: #3730a3;
    font-weight: 600;
    font-size: 1rem;
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    font-size: 0.85rem;
}

.data-item .type {
    color: #4f46e5;
    font-weight: 600;
}

.data-item .values {
    color: #6366f1;
    font-weight: 600;
}

.iot-alerts {
    border-top: 1px solid rgba(99, 102, 241, 0.2);
    padding-top: 15px;
}

.iot-alerts h6 {
    margin: 0 0 12px 0;
    color: #3730a3;
    font-weight: 600;
    font-size: 1rem;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.alert-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(99, 102, 241, 0.1);
    border-radius: 6px;
    border-left: 3px solid #6366f1;
    font-size: 0.8rem;
}

.alert-item.warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
}

.alert-item.info {
    background: rgba(6, 182, 212, 0.1);
    border-left-color: #06b6d4;
}

.alert-item .message {
    color: #4f46e5;
    font-weight: 500;
}

.alert-item .time {
    color: #6b7280;
    font-size: 0.75rem;
}

/* Dark Mode für Phase 6 */
[data-theme="dark"] .pattern-recognition-card {
    background: linear-gradient(135deg, #0c4a6e 0%, #075985 100%) !important;
    border-color: #06b6d4 !important;
}

[data-theme="dark"] .pattern-type {
    background: rgba(6, 182, 212, 0.2);
    color: #67e8f9;
}

[data-theme="dark"] .pattern-content p {
    color: #e5e7eb;
}

[data-theme="dark"] .pattern-data {
    background: rgba(6, 182, 212, 0.1);
    border-left-color: #06b6d4;
    color: #67e8f9;
}

[data-theme="dark"] .anomaly-detection-card {
    background: linear-gradient(135deg, #7c2d12 0%, #9a3412 100%) !important;
    border-color: #f97316 !important;
}

[data-theme="dark"] .anomaly-item {
    background: #374151;
    border-left-color: #4b5563;
}

[data-theme="dark"] .anomaly-content p {
    color: #e5e7eb;
}

[data-theme="dark"] .iot-status-card {
    background: linear-gradient(135deg, #312e81 0%, #3730a3 100%) !important;
    border-color: #6366f1 !important;
}

[data-theme="dark"] .sensor-count .label, 
[data-theme="dark"] .last-sync .label {
    color: #c7d2fe;
}

[data-theme="dark"] .sensor-count .value, 
[data-theme="dark"] .last-sync .value {
    color: #a5b4fc;
}

[data-theme="dark"] .sensor-count, 
[data-theme="dark"] .last-sync {
    border-bottom-color: rgba(99, 102, 241, 0.3);
}

[data-theme="dark"] .iot-data h6 {
    color: #c7d2fe;
}

[data-theme="dark"] .data-item .type {
    color: #a5b4fc;
}

[data-theme="dark"] .data-item .values {
    color: #818cf8;
}

[data-theme="dark"] .iot-alerts {
    border-top-color: rgba(99, 102, 241, 0.3);
}

[data-theme="dark"] .iot-alerts h6 {
    color: #c7d2fe;
}

[data-theme="dark"] .alert-item {
    background: rgba(99, 102, 241, 0.1);
    border-left-color: #6366f1;
}

[data-theme="dark"] .alert-item.warning {
    background: rgba(245, 158, 11, 0.1);
    border-left-color: #f59e0b;
}

[data-theme="dark"] .alert-item.info {
    background: rgba(6, 182, 212, 0.1);
    border-left-color: #06b6d4;
}

[data-theme="dark"] .alert-item .message {
    color: #a5b4fc;
}

[data-theme="dark"] .alert-item .time {
    color: #9ca3af;
}

/* Responsive Design für Phase 6 */
@media (max-width: 768px) {
    .patterns-list {
        gap: 8px;
    }
    
    .pattern-item {
        padding: 10px;
    }
    
    .pattern-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .anomalies-list {
        gap: 8px;
    }
    
    .anomaly-item {
        padding: 10px;
    }
    
    .anomaly-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .sensor-count, .last-sync {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .data-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }
    
    .alert-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}