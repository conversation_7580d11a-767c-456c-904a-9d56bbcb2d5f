#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Grow-Diary-Basic - Minimale Flask-App
Basis-Version des Grow-Tagebuchs mit Kernfunktionen
"""

import os
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from werkzeug.utils import secure_filename
import json

# Import der neuen Datenbank
from database_basic import db

# Import route modules
from routes.plant_routes import plant_bp
from routes.api_routes import api_bp

# Import widget route modules
from routes.widgets.trichome_routes import trichome_bp
from routes.widgets.flush_routes import flush_bp
from routes.widgets.flowering_routes import flowering_bp
from routes.widgets.nutrient_routes import nutrient_routes
from routes.widgets.vpd_routes import vpd_bp
from routes.widgets.lighting_routes import lighting_bp
from routes.widgets.stress_routes import stress_bp
from routes.widgets.watering_routes import watering_bp

# Import sensor routes
from routes.sensor_routes import sensor_bp

# App-Konfiguration
app = Flask(__name__)
app.secret_key = 'grow-diary-basic-secret-key-2025'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max

# Upload-Ordner erstellen falls nicht vorhanden
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Register blueprints
app.register_blueprint(plant_bp)
app.register_blueprint(api_bp)

# Register widget blueprints
app.register_blueprint(trichome_bp)
app.register_blueprint(flush_bp)
app.register_blueprint(flowering_bp)
app.register_blueprint(nutrient_routes)
app.register_blueprint(vpd_bp)
app.register_blueprint(lighting_bp)
app.register_blueprint(stress_bp)
app.register_blueprint(watering_bp)

# Register sensor blueprints
app.register_blueprint(sensor_bp)

def format_date(date_obj):
    """Datum im deutschen Format formatieren"""
    if isinstance(date_obj, str):
        try:
            date_obj = datetime.strptime(date_obj, '%Y-%m-%d')
        except ValueError:
            return date_obj
    return date_obj.strftime('%d.%m.%Y')

def calculate_plant_status(plant):
    """Pflanzenstatus basierend auf Blüte- und Enddaten berechnen"""
    # Wenn ein Grow-Enddatum gesetzt ist, ist die Pflanze beendet
    if plant.get('grow_end_date'):
        return 'finished'
    
    # Wenn ein Blüte-Startdatum gesetzt ist, ist die Pflanze in der Blüte
    if plant.get('flower_start_date'):
        return 'flowering'
    
    # Ansonsten ist die Pflanze aktiv (Vegetationsphase)
    return 'active'

def get_feature_config():
    """Feature-Konfiguration laden"""
    return db.get_config()

def save_feature_config(features):
    """Feature-Konfiguration speichern"""
    db.save_config(features)

# Main Routes
@app.route('/')
def index():
    """Hauptseite"""
    plants = db.get_all_plants()
    
    # Status für jede Pflanze berechnen
    for plant in plants:
        plant['status'] = calculate_plant_status(plant)
        plant['entry_count'] = len(db.get_entries_by_plant(plant['id']))
        
        # Letzten Eintrag finden
        entries = db.get_entries_by_plant(plant['id'])
        plant['last_entry_date'] = entries[0]['date'] if entries else None
    
    # Statistiken
    active_plants = [p for p in plants if p['status'] == 'active']
    stats = {
        'total_plants': len(plants),
        'total_entries': sum(p['entry_count'] for p in plants),
        'active_plants': len(active_plants)
    }
    
    return render_template('index.html', 
                         plants=plants, 
                         stats=stats,
                         features=get_feature_config(),
                         format_date=format_date)

@app.route('/config')
def config():
    """Konfigurationsseite"""
    features = get_feature_config()
    return render_template('config.html', features=features)

@app.route('/entry/<int:entry_id>/edit', methods=['GET', 'POST'])
def edit_entry(entry_id):
    """Eintrag bearbeiten"""
    entry = db.get_entry_by_id(entry_id)
    if not entry:
        flash('Eintrag nicht gefunden', 'error')
        return redirect(url_for('index'))
    
    if request.method == 'POST':
        try:
            data = request.form
            
            entry_data = {
                'entry_type': data.get('entry_type', 'note'),
                'date': data['entry_date'],
                'title': data['title'],
                'note': data.get('content', '')
            }
            
            db.update_entry(entry_id, entry_data)
            flash('Eintrag erfolgreich aktualisiert', 'success')
            
            # Zurück zur Pflanzenseite
            return redirect(url_for('plant.plant_detail', plant_id=entry['plant_id']))
            
        except Exception as e:
            flash(f'Fehler beim Aktualisieren des Eintrags: {str(e)}', 'error')
    
    return render_template('edit_entry.html', entry=entry)

@app.route('/entry/<int:entry_id>/delete', methods=['POST'])
def delete_entry(entry_id):
    """Eintrag löschen"""
    try:
        # Plant-ID für Redirect speichern
        entry = db.get_entry_by_id(entry_id)
        plant_id = entry['plant_id'] if entry else None
        
        db.delete_entry(entry_id)
        flash('Eintrag erfolgreich gelöscht', 'success')
        
        if plant_id:
            return redirect(url_for('plant.plant_detail', plant_id=plant_id))
        else:
            return redirect(url_for('index'))
            
    except Exception as e:
        flash(f'Fehler beim Löschen des Eintrags: {str(e)}', 'error')
        return redirect(url_for('index'))

# Error Handler
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# Template Filter
@app.template_filter('date')
def date_filter(date_obj):
    """Datum-Filter für Templates"""
    return format_date(date_obj)

@app.route('/test-theme')
def test_theme():
    return render_template('test_theme.html')

if __name__ == '__main__':
    # Datenbank initialisieren
    db.init_database()
    
    print("🌱 Grow-Diary-Basic wird gestartet...")
    print("📊 Datenbank initialisiert")
    print("🚀 Server startet auf http://localhost:5002")
    
    # Development Server starten
    app.run(debug=True, host='0.0.0.0', port=5002) 